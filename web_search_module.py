"""
Modul de căutare web pentru AI Assistant Local
Simulează capacitățile de căutare și fetch web
"""

import requests
import json
import time
from datetime import datetime
from typing import Dict, List, Any
import re
from urllib.parse import urljoin, urlparse

class WebSearchModule:
    def __init__(self, knowledge_base_path: str = "knowledge_base"):
        self.knowledge_base_path = knowledge_base_path
        self.search_history = []
        self.cached_results = {}
        
    def web_search(self, query: str, num_results: int = 5) -> List[Dict[str, Any]]:
        """Simulează căutarea web (în realitate ar folosi API-uri reale)"""
        print(f"🔍 Căutare web pentru: '{query}'")
        
        # Simulez rezultate de căutare bazate pe query
        simulated_results = self._generate_simulated_results(query, num_results)
        
        # Salvez în istoric
        search_entry = {
            "timestamp": datetime.now().isoformat(),
            "query": query,
            "results_count": len(simulated_results),
            "results": simulated_results
        }
        self.search_history.append(search_entry)
        
        return simulated_results
    
    def web_fetch(self, url: str) -> Dict[str, Any]:
        """Simulează fetch-ul unei pagini web"""
        print(f"📄 Fetch pagină: {url}")
        
        # În realitate ar face request HTTP real
        try:
            # Simulez conținut bazat pe URL
            content = self._generate_simulated_content(url)
            
            result = {
                "url": url,
                "title": self._extract_title_from_url(url),
                "content": content,
                "timestamp": datetime.now().isoformat(),
                "status": "success"
            }
            
            # Cache rezultatul
            self.cached_results[url] = result
            
            return result
            
        except Exception as e:
            return {
                "url": url,
                "error": str(e),
                "status": "error",
                "timestamp": datetime.now().isoformat()
            }
    
    def search_documentation(self, technology: str) -> List[Dict[str, Any]]:
        """Caută documentație pentru o tehnologie specifică"""
        docs_queries = {
            "python": "Python official documentation tutorial",
            "javascript": "JavaScript MDN documentation",
            "react": "React official documentation",
            "django": "Django documentation tutorial",
            "flask": "Flask documentation quickstart",
            "html": "HTML MDN documentation",
            "css": "CSS MDN documentation"
        }
        
        query = docs_queries.get(technology.lower(), f"{technology} documentation")
        return self.web_search(query, 3)
    
    def find_code_examples(self, programming_task: str) -> List[Dict[str, Any]]:
        """Caută exemple de cod pentru o sarcină specifică"""
        query = f"{programming_task} code example tutorial"
        results = self.web_search(query, 5)
        
        # Filtrează rezultatele pentru a găsi cele mai relevante
        code_results = []
        for result in results:
            if any(keyword in result['title'].lower() for keyword in ['code', 'example', 'tutorial', 'how to']):
                code_results.append(result)
        
        return code_results
    
    def _generate_simulated_results(self, query: str, num_results: int) -> List[Dict[str, Any]]:
        """Generează rezultate simulate bazate pe query"""
        base_results = [
            {
                "title": f"Tutorial: {query.title()}",
                "url": f"https://example-tutorial.com/{query.replace(' ', '-').lower()}",
                "snippet": f"Comprehensive guide to {query}. Learn the basics and advanced concepts with practical examples."
            },
            {
                "title": f"{query.title()} - Official Documentation",
                "url": f"https://docs.example.com/{query.replace(' ', '-').lower()}",
                "snippet": f"Official documentation for {query}. Complete reference with API details and examples."
            },
            {
                "title": f"Best Practices for {query.title()}",
                "url": f"https://best-practices.com/{query.replace(' ', '-').lower()}",
                "snippet": f"Industry best practices and common patterns for {query}. Avoid common pitfalls."
            },
            {
                "title": f"{query.title()} Examples and Code Samples",
                "url": f"https://code-examples.com/{query.replace(' ', '-').lower()}",
                "snippet": f"Practical code examples and samples for {query}. Copy-paste ready solutions."
            },
            {
                "title": f"Advanced {query.title()} Techniques",
                "url": f"https://advanced-guide.com/{query.replace(' ', '-').lower()}",
                "snippet": f"Advanced techniques and optimization strategies for {query}. For experienced developers."
            }
        ]
        
        return base_results[:num_results]
    
    def _generate_simulated_content(self, url: str) -> str:
        """Generează conținut simulat pentru o pagină"""
        domain = urlparse(url).netloc
        path = urlparse(url).path
        
        if "tutorial" in domain:
            return f"""
# Tutorial Content

This is a comprehensive tutorial covering the topic from the URL: {url}

## Introduction
Welcome to this detailed guide. Here you'll learn step-by-step instructions.

## Main Content
- Step 1: Getting started
- Step 2: Basic concepts
- Step 3: Advanced techniques
- Step 4: Best practices

## Code Examples
```python
# Example code would be here
def example_function():
    return "Hello, World!"
```

## Conclusion
This tutorial covered the essential concepts you need to know.
"""
        elif "docs" in domain:
            return f"""
# Official Documentation

## API Reference
Complete API documentation for the requested topic.

## Parameters
- param1: Description of parameter 1
- param2: Description of parameter 2

## Examples
Practical examples showing how to use the API.

## See Also
Related documentation and resources.
"""
        else:
            return f"""
# Web Content

This is the main content from {url}.

The page contains relevant information about the requested topic,
including explanations, examples, and practical guidance.

Content would be extracted and formatted here in a real implementation.
"""
    
    def _extract_title_from_url(self, url: str) -> str:
        """Extrage un titlu simulat din URL"""
        path = urlparse(url).path.strip('/')
        if path:
            title = path.replace('-', ' ').replace('_', ' ').title()
            return f"{title} - Web Page"
        return "Web Page Title"
    
    def get_search_history(self) -> List[Dict[str, Any]]:
        """Returnează istoricul căutărilor"""
        return self.search_history
    
    def clear_cache(self):
        """Șterge cache-ul rezultatelor"""
        self.cached_results.clear()
        print("🗑️ Cache-ul web a fost șters")
    
    def save_search_history(self, filename: str = "web_search_history.json"):
        """Salvează istoricul căutărilor în fișier"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.search_history, f, indent=2, ensure_ascii=False)
        print(f"💾 Istoric căutări salvat în {filename}")

# Exemplu de utilizare
if __name__ == "__main__":
    web_module = WebSearchModule()
    
    # Test căutare web
    results = web_module.web_search("Python web development", 3)
    print(f"Găsite {len(results)} rezultate")
    
    # Test fetch pagină
    if results:
        content = web_module.web_fetch(results[0]['url'])
        print(f"Fetch realizat pentru: {content['title']}")
    
    # Test căutare documentație
    docs = web_module.search_documentation("python")
    print(f"Documentație Python: {len(docs)} rezultate")
