"""
SIMPLE WEB INTERFACE - Interfață web simplificată fără thread-uri problematice
"""

from flask import Flask, render_template, request, jsonify
import json
import time
from datetime import datetime
from true_ai_system import TrueAISystem

app = Flask(__name__)
app.config['SECRET_KEY'] = 'true_ai_secret_key_2024'

# Variabile globale pentru sistemul AI
ai_system = None
system_status = {
    'initialized': False,
    'modules_active': 0,
    'consciousness_level': 0.0,
    'interactions_count': 0,
    'uptime_start': None
}

def initialize_ai_system():
    """Inițializează sistemul AI"""
    global ai_system, system_status
    
    try:
        print("🚀 Inițializare sistem AI pentru interfața web...")
        
        # Creează sistemul AI fără thread-uri problematice
        ai_system = TrueAISystem()
        
        # Actualizează statusul
        system_status.update({
            'initialized': True,
            'modules_active': 9,  # Toate modulele
            'consciousness_level': ai_system.neural_core.consciousness_level,
            'uptime_start': datetime.now(),
            'interactions_count': 0
        })
        
        print("✅ Sistem AI inițializat pentru interfața web!")
        return True
        
    except Exception as e:
        print(f"❌ Eroare la inițializarea sistemului AI: {e}")
        system_status['error'] = str(e)
        return False

@app.route('/')
def index():
    """Pagina principală"""
    return render_template('index.html')

@app.route('/api/status')
def get_status():
    """API pentru statusul sistemului"""
    if not ai_system:
        if not initialize_ai_system():
            return jsonify({'status': 'error', 'error': 'Nu s-a putut inițializa sistemul AI'})
    
    try:
        stats = ai_system.get_system_statistics()
        consciousness_report = ai_system.consciousness_engine.get_consciousness_report()
        
        return jsonify({
            'status': 'active',
            'system_stats': stats,
            'consciousness': consciousness_report,
            'uptime': (datetime.now() - system_status['uptime_start']).total_seconds() if system_status['uptime_start'] else 0,
            'interactions_count': system_status['interactions_count']
        })
    except Exception as e:
        return jsonify({'status': 'error', 'error': str(e)})

@app.route('/api/chat', methods=['POST'])
def chat():
    """API pentru conversația cu AI-ul"""
    if not ai_system:
        if not initialize_ai_system():
            return jsonify({'error': 'Sistemul AI nu este încă inițializat'})
    
    try:
        data = request.json
        message = data.get('message', '')
        
        if not message:
            return jsonify({'error': 'Mesajul nu poate fi gol'})
        
        # Procesează mesajul prin sistemul AI
        result = ai_system.process_request(message)
        
        # Actualizează contorul
        system_status['interactions_count'] += 1
        system_status['consciousness_level'] = result.get('consciousness_level', 0.0)
        
        return jsonify({
            'response': result['response'],
            'processing_time': result['processing_time'],
            'consciousness_level': result['consciousness_level'],
            'interaction_id': result['interaction_id'],
            'advanced_modules': len(result.get('advanced_modules_results', {})),
            'timestamp': result['timestamp']
        })
        
    except Exception as e:
        return jsonify({'error': f'Eroare în procesare: {str(e)}'})

@app.route('/api/vision', methods=['POST'])
def vision():
    """API pentru procesarea vizuală"""
    if not ai_system:
        if not initialize_ai_system():
            return jsonify({'error': 'Sistemul AI nu este încă inițializat'})
    
    try:
        data = request.json
        concept = data.get('concept', '')
        
        if not concept:
            return jsonify({'error': 'Conceptul vizual nu poate fi gol'})
        
        # Procesează prin modulul de viziune
        vision_module = ai_system.advanced_modules['vision_module']
        result = vision_module.process_visual_concept(concept)
        
        # Obține detaliile vizuale
        if vision_module.visual_memory:
            latest_vision = vision_module.visual_memory[-1]
            
            return jsonify({
                'visual_processing': result['visual_processing'],
                'visual_features_count': result['visual_features_count'],
                'spatial_complexity': result['spatial_complexity'],
                'emotional_impact': result['emotional_impact'],
                'pattern_confidence': result['pattern_match_confidence'],
                'visual_details': {
                    'colors': latest_vision['color_palette'],
                    'layout': latest_vision['spatial_layout'],
                    'movement': latest_vision['movement_patterns'],
                    'texture': latest_vision['texture_properties']
                }
            })
        else:
            return jsonify(result)
            
    except Exception as e:
        return jsonify({'error': f'Eroare în procesarea vizuală: {str(e)}'})

@app.route('/api/predict', methods=['POST'])
def predict():
    """API pentru predicții"""
    if not ai_system:
        if not initialize_ai_system():
            return jsonify({'error': 'Sistemul AI nu este încă inițializat'})
    
    try:
        data = request.json
        context = data.get('context', '')
        time_horizon = data.get('time_horizon', '1 zi')
        
        if not context:
            return jsonify({'error': 'Contextul nu poate fi gol'})
        
        # Generează predicția
        prediction_module = ai_system.advanced_modules['predictive_modeling']
        result = prediction_module.generate_prediction(context, time_horizon)
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'error': f'Eroare în generarea predicției: {str(e)}'})

@app.route('/api/consciousness')
def get_consciousness():
    """API pentru raportul de conștiință"""
    if not ai_system:
        if not initialize_ai_system():
            return jsonify({'error': 'Sistemul AI nu este încă inițializat'})
    
    try:
        consciousness_report = ai_system.consciousness_engine.get_consciousness_report()
        consciousness_stream = ai_system.consciousness_engine.get_consciousness_stream(10)
        
        return jsonify({
            'consciousness_report': consciousness_report,
            'consciousness_stream': consciousness_stream
        })
        
    except Exception as e:
        return jsonify({'error': f'Eroare la obținerea conștiinței: {str(e)}'})

if __name__ == '__main__':
    print("🌐 Pornire interfață web simplificată pentru TRUE AI SYSTEM...")
    print("🔗 Accesează: http://localhost:5000")
    print("🚀 Toate capacitățile AI-ului sunt disponibile prin interfața web!")
    
    # Inițializează sistemul AI la pornire
    initialize_ai_system()
    
    app.run(host='0.0.0.0', port=5000, debug=False, threaded=True)
