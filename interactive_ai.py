#!/usr/bin/env python3
"""
Script interactiv pentru AI Assistant Local
Permite utilizatorului să interacționeze cu toate capacitățile AI
"""

import os
import sys
from ai_assistant_core import AIAssistantCore

class InteractiveAI:
    def __init__(self):
        self.ai = AIAssistantCore(".")
        self.running = True
        
    def show_menu(self):
        """Afișează meniul principal"""
        print("\n" + "="*50)
        print("🤖 AI ASSISTANT LOCAL - MENIU INTERACTIV")
        print("="*50)
        print("1. 🔍 Căutare Web")
        print("2. 📋 Gestionare Task-uri")
        print("3. 📄 Procesare Fișiere")
        print("4. 🏗️  Setup Proiect")
        print("5. 🧠 Baza de Cunoștințe")
        print("6. 📊 Rapoarte și Statistici")
        print("7. ❓ Ajutor")
        print("0. 🚪 Ieșire")
        print("-" * 50)
    
    def handle_web_search(self):
        """Gestionează căutarea web"""
        print("\n🔍 MODUL CĂUTARE WEB")
        print("1. Căutare generală")
        print("2. Căutare documentație")
        print("3. Căutare exemple cod")
        print("4. Fetch pagină web")
        
        choice = input("\nAlege opțiunea (1-4): ").strip()
        
        if choice == "1":
            query = input("Introdu termenul de căutare: ").strip()
            if query:
                results = self.ai.search_web(query, 5)
                print(f"\n📋 Găsite {len(results)} rezultate:")
                for i, result in enumerate(results, 1):
                    print(f"{i}. {result['title']}")
                    print(f"   URL: {result['url']}")
                    print(f"   Preview: {result['snippet'][:100]}...")
                    print()
        
        elif choice == "2":
            tech = input("Introdu tehnologia (python, javascript, react, etc.): ").strip()
            if tech:
                docs = self.ai.web_search.search_documentation(tech)
                print(f"\n📚 Documentație pentru {tech}:")
                for i, doc in enumerate(docs, 1):
                    print(f"{i}. {doc['title']}")
                    print(f"   URL: {doc['url']}")
                    print()
        
        elif choice == "3":
            task = input("Introdu sarcina de programare: ").strip()
            if task:
                examples = self.ai.web_search.find_code_examples(task)
                print(f"\n💻 Exemple cod pentru '{task}':")
                for i, example in enumerate(examples, 1):
                    print(f"{i}. {example['title']}")
                    print(f"   URL: {example['url']}")
                    print()
        
        elif choice == "4":
            url = input("Introdu URL-ul paginii: ").strip()
            if url:
                content = self.ai.fetch_webpage(url)
                if content.get('status') == 'success':
                    print(f"\n✅ Pagină încărcată: {content['title']}")
                    print(f"📄 Conținut: {len(content.get('content', ''))} caractere")
                else:
                    print(f"❌ Eroare: {content.get('error', 'Necunoscută')}")
    
    def handle_task_management(self):
        """Gestionează task-urile"""
        print("\n📋 MODUL GESTIONARE TASK-URI")
        print("1. Creează task nou")
        print("2. Listează task-uri")
        print("3. Actualizează task")
        print("4. Raport progres")
        print("5. Generează plan proiect")
        
        choice = input("\nAlege opțiunea (1-5): ").strip()
        
        if choice == "1":
            name = input("Nume task: ").strip()
            description = input("Descriere: ").strip()
            priority = input("Prioritate (LOW/MEDIUM/HIGH/URGENT): ").strip().upper()
            
            if name and description:
                result = self.ai.create_task(name, description, priority or "MEDIUM")
                if result['status'] == 'success':
                    print(f"✅ Task creat: {result['name']} (ID: {result['task_id'][:8]})")
                else:
                    print(f"❌ Eroare: {result.get('error', 'Necunoscută')}")
        
        elif choice == "2":
            tasks = self.ai.task_manager.list_tasks()
            if tasks:
                print(f"\n📋 Lista task-uri ({len(tasks)} total):")
                for task in tasks:
                    status_icon = {"NOT_STARTED": "⏳", "IN_PROGRESS": "🔄", 
                                 "COMPLETE": "✅", "CANCELLED": "❌"}
                    icon = status_icon.get(task.state.value, "❓")
                    print(f"{icon} {task.name} ({task.priority.value})")
                    print(f"   ID: {task.id[:8]} | {task.description[:60]}...")
                    print()
            else:
                print("📭 Nu există task-uri")
        
        elif choice == "3":
            task_id = input("ID task (primele 8 caractere): ").strip()
            new_state = input("Stare nouă (NOT_STARTED/IN_PROGRESS/COMPLETE/CANCELLED): ").strip().upper()
            
            if task_id and new_state:
                # Găsește task-ul complet după ID parțial
                full_id = None
                for tid in self.ai.task_manager.tasks.keys():
                    if tid.startswith(task_id):
                        full_id = tid
                        break
                
                if full_id:
                    result = self.ai.update_task_status(full_id, new_state)
                    if result['status'] == 'success':
                        print(f"✅ {result['message']}")
                    else:
                        print(f"❌ {result['message']}")
                else:
                    print("❌ Task nu a fost găsit")
        
        elif choice == "4":
            progress = self.ai.get_task_progress()
            print(f"\n📊 RAPORT PROGRES:")
            print(f"📋 Total task-uri: {progress['total_tasks']}")
            print(f"✅ Complete: {progress['completed']}")
            print(f"🔄 În progres: {progress['in_progress']}")
            print(f"⏳ Neîncepute: {progress['not_started']}")
            print(f"❌ Anulate: {progress['cancelled']}")
            print(f"📈 Progres: {progress['progress_percentage']}%")
        
        elif choice == "5":
            project_name = input("Nume proiect: ").strip()
            print("Introdu cerințele (o pe linie, linie goală pentru a termina):")
            requirements = []
            while True:
                req = input(f"Cerința {len(requirements)+1}: ").strip()
                if not req:
                    break
                requirements.append(req)
            
            if project_name and requirements:
                tasks = self.ai.task_manager.generate_project_plan(project_name, requirements)
                print(f"✅ Plan generat cu {len(tasks)} task-uri pentru '{project_name}'")
    
    def handle_file_processing(self):
        """Gestionează procesarea fișierelor"""
        print("\n📄 MODUL PROCESARE FIȘIERE")
        print("1. Analizează fișier")
        print("2. Editează fișier")
        print("3. Caută în fișier")
        print("4. Creează fișier nou")
        print("5. Istoric editări")
        
        choice = input("\nAlege opțiunea (1-5): ").strip()
        
        if choice == "1":
            file_path = input("Calea fișierului: ").strip()
            if file_path:
                analysis = self.ai.analyze_file(file_path)
                if analysis.get('status') != 'error':
                    print(f"\n🔍 ANALIZĂ FIȘIER: {file_path}")
                    print(f"📄 Limbaj: {analysis.get('language', 'N/A')}")
                    print(f"📏 Linii: {analysis.get('total_lines', 0)}")
                    print(f"🔧 Funcții: {len(analysis.get('functions', []))}")
                    print(f"🏗️  Clase: {len(analysis.get('classes', []))}")
                    print(f"📊 Complexitate: {analysis.get('complexity_score', 0)}")
                    
                    if analysis.get('functions'):
                        print(f"📝 Funcții: {', '.join(analysis['functions'][:5])}")
                    if analysis.get('classes'):
                        print(f"📝 Clase: {', '.join(analysis['classes'][:5])}")
                else:
                    print(f"❌ Eroare: {analysis.get('error', 'Necunoscută')}")
        
        elif choice == "2":
            file_path = input("Calea fișierului: ").strip()
            old_text = input("Text de înlocuit: ").strip()
            new_text = input("Text nou: ").strip()
            
            if file_path and old_text:
                result = self.ai.edit_file(file_path, old_text, new_text)
                if result.get('status') == 'success':
                    print(f"✅ Editare reușită: {result.get('replacements', 0)} înlocuiri")
                else:
                    print(f"❌ Eroare: {result.get('error', 'Necunoscută')}")
        
        elif choice == "3":
            file_path = input("Calea fișierului: ").strip()
            pattern = input("Pattern de căutare: ").strip()
            
            if file_path and pattern:
                result = self.ai.file_processor.search_in_file(file_path, pattern)
                if result.get('status') == 'success':
                    matches = result.get('matches', [])
                    print(f"\n🔍 Găsite {len(matches)} potriviri în {file_path}:")
                    for match in matches[:10]:  # Primele 10
                        print(f"Linia {match['line_number']}: {match['line_content']}")
                else:
                    print(f"❌ Eroare: {result.get('error', 'Necunoscută')}")
        
        elif choice == "4":
            file_path = input("Calea fișierului nou: ").strip()
            print("Introdu conținutul (Ctrl+D pentru a termina):")
            try:
                content = sys.stdin.read()
                if file_path and content:
                    result = self.ai.file_processor.write_file(file_path, content)
                    if result.get('status') == 'success':
                        print(f"✅ Fișier creat: {file_path}")
                    else:
                        print(f"❌ Eroare: {result.get('error', 'Necunoscută')}")
            except KeyboardInterrupt:
                print("\n❌ Operațiune anulată")
        
        elif choice == "5":
            history = self.ai.file_processor.get_edit_history()
            if history:
                print(f"\n📝 ISTORIC EDITĂRI ({len(history)} intrări):")
                for entry in history[-10:]:  # Ultimele 10
                    print(f"⏰ {entry['timestamp'][:19]}")
                    print(f"📄 {entry['action']} în {entry['file']}")
                    if 'replacements' in entry:
                        print(f"🔄 {entry['replacements']} înlocuiri")
                    print()
            else:
                print("📭 Nu există istoric de editări")
    
    def handle_knowledge_base(self):
        """Gestionează baza de cunoștințe"""
        print("\n🧠 BAZA DE CUNOȘTINȚE")
        topics = list(self.ai.knowledge_base.keys())
        
        if topics:
            print(f"📚 Topicuri disponibile ({len(topics)}):")
            for i, topic in enumerate(topics, 1):
                entries = len(self.ai.knowledge_base[topic])
                print(f"{i}. {topic} ({entries} intrări)")
            
            choice = input(f"\nAlege topic (1-{len(topics)}) sau 0 pentru toate: ").strip()
            
            if choice == "0":
                total_entries = sum(len(entries) for entries in self.ai.knowledge_base.values())
                print(f"\n📊 SUMAR BAZĂ DE CUNOȘTINȚE:")
                print(f"📚 Total topicuri: {len(topics)}")
                print(f"📝 Total intrări: {total_entries}")
                for topic, entries in self.ai.knowledge_base.items():
                    print(f"  • {topic}: {len(entries)} intrări")
            
            elif choice.isdigit() and 1 <= int(choice) <= len(topics):
                topic = topics[int(choice) - 1]
                entries = self.ai.retrieve_knowledge(topic)
                print(f"\n📖 TOPIC: {topic} ({len(entries)} intrări)")
                for i, entry in enumerate(entries[-5:], 1):  # Ultimele 5
                    print(f"\n{i}. ⏰ {entry['timestamp'][:19]}")
                    data = entry['data']
                    if isinstance(data, dict):
                        for key, value in list(data.items())[:3]:  # Primele 3 chei
                            print(f"   {key}: {str(value)[:100]}...")
                    else:
                        print(f"   {str(data)[:200]}...")
        else:
            print("📭 Baza de cunoștințe este goală")
    
    def show_help(self):
        """Afișează ajutorul"""
        print("\n❓ AJUTOR AI ASSISTANT LOCAL")
        print("="*40)
        print("🤖 Acest AI Assistant Local recreează capacitățile unui asistent AI")
        print("   într-un mediu local complet funcțional.")
        print()
        print("📋 CAPACITĂȚI PRINCIPALE:")
        print("• 🔍 Căutare web simulată cu rezultate relevante")
        print("• 📋 Gestionare task-uri cu progres și planificare")
        print("• 📄 Procesare fișiere cu analiză și editare")
        print("• 🏗️  Setup proiecte complete cu structuri")
        print("• 🧠 Bază de cunoștințe persistentă")
        print("• 📊 Rapoarte și statistici detaliate")
        print()
        print("💾 PERSISTENȚĂ:")
        print("• Toate datele sunt salvate local în JSON")
        print("• Backup-uri automate pentru fișiere")
        print("• Istoric complet al acțiunilor")
        print()
        print("🎯 UTILIZARE:")
        print("• Navighează prin meniuri cu numere")
        print("• Urmează instrucțiunile pentru fiecare modul")
        print("• Toate modificările sunt salvate automat")
    
    def run(self):
        """Rulează interfața interactivă"""
        print("🚀 Bun venit la AI Assistant Local!")
        print("Toate capacitățile AI recreate într-un mediu local complet funcțional.")
        
        while self.running:
            try:
                self.show_menu()
                choice = input("Alege opțiunea: ").strip()
                
                if choice == "1":
                    self.handle_web_search()
                elif choice == "2":
                    self.handle_task_management()
                elif choice == "3":
                    self.handle_file_processing()
                elif choice == "4":
                    project_name = input("Nume proiect: ").strip()
                    project_type = input("Tip proiect (web_app/python_project/data_science): ").strip()
                    requirements = []
                    print("Introdu cerințele (linie goală pentru a termina):")
                    while True:
                        req = input(f"Cerința {len(requirements)+1}: ").strip()
                        if not req:
                            break
                        requirements.append(req)
                    
                    if project_name and project_type and requirements:
                        result = self.ai.comprehensive_project_setup(project_name, project_type, requirements)
                        print(f"✅ Proiect '{project_name}' configurat!")
                        for step in result.get('steps_completed', []):
                            print(f"  ✅ {step}")
                elif choice == "5":
                    self.handle_knowledge_base()
                elif choice == "6":
                    progress = self.ai.get_task_progress()
                    print(f"\n📊 STATISTICI GENERALE:")
                    print(f"📋 Task-uri: {progress['completion_rate']} ({progress['progress_percentage']}%)")
                    print(f"🧠 Cunoștințe: {len(self.ai.knowledge_base)} topicuri")
                    print(f"📝 Editări: {len(self.ai.file_processor.get_edit_history())} acțiuni")
                    print(f"🔍 Căutări: {len(self.ai.web_search.get_search_history())} query-uri")
                elif choice == "7":
                    self.show_help()
                elif choice == "0":
                    print("👋 La revedere! Toate datele au fost salvate.")
                    self.running = False
                else:
                    print("❌ Opțiune invalidă. Încearcă din nou.")
                
                if self.running and choice != "0":
                    input("\n⏸️  Apasă Enter pentru a continua...")
                    
            except KeyboardInterrupt:
                print("\n\n👋 Ieșire forțată. La revedere!")
                self.running = False
            except Exception as e:
                print(f"\n❌ Eroare neașteptată: {e}")
                input("⏸️  Apasă Enter pentru a continua...")

if __name__ == "__main__":
    interactive_ai = InteractiveAI()
    interactive_ai.run()
