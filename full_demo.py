#!/usr/bin/env python3
"""
Demonstrație completă a AI Assistant Local
Testează toate capacitățile integrate
"""

import os
import time
from ai_assistant_core import AIAssistantCore

def print_separator(title: str):
    """Printează un separator frumos"""
    print("\n" + "="*60)
    print(f"🎯 {title}")
    print("="*60)

def print_step(step: str):
    """Printează un pas"""
    print(f"\n📋 {step}")
    print("-" * 40)

def demo_ai_assistant():
    """Demonstrație completă a AI Assistant"""
    
    print("🚀 DEMONSTRAȚIE COMPLETĂ AI ASSISTANT LOCAL")
    print("Recrearea tuturor capacităților într-un mediu local")
    
    # Inițializează AI Assistant
    print_separator("INIȚIALIZARE AI ASSISTANT")
    ai = AIAssistantCore(".")
    
    # 1. DEMONSTRAȚIE CĂUTARE WEB
    print_separator("MODUL CĂUTARE WEB")
    
    print_step("Căutare documentație Python")
    web_results = ai.search_web("Python programming tutorial", 3)
    for i, result in enumerate(web_results, 1):
        print(f"  {i}. {result['title']}")
        print(f"     URL: {result['url']}")
        print(f"     Preview: {result['snippet'][:100]}...")
    
    print_step("Fetch conținut pagină web")
    if web_results:
        page_content = ai.fetch_webpage(web_results[0]['url'])
        print(f"  ✅ Pagină încărcată: {page_content.get('title', 'N/A')}")
        print(f"  📄 Conținut: {len(page_content.get('content', ''))} caractere")
    
    # 2. DEMONSTRAȚIE GESTIONARE TASK-URI
    print_separator("MODUL GESTIONARE TASK-URI")
    
    print_step("Creare task-uri pentru un proiect")
    task1 = ai.create_task("Învață Python Basics", "Studiază sintaxa de bază Python", "HIGH")
    task2 = ai.create_task("Creează aplicație Flask", "Dezvoltă o aplicație web simplă", "MEDIUM")
    task3 = ai.create_task("Testează aplicația", "Scrie și rulează teste", "MEDIUM")
    
    print(f"  ✅ Task 1: {task1['name']} (ID: {task1['task_id'][:8]})")
    print(f"  ✅ Task 2: {task2['name']} (ID: {task2['task_id'][:8]})")
    print(f"  ✅ Task 3: {task3['name']} (ID: {task3['task_id'][:8]})")
    
    print_step("Actualizare stare task-uri")
    ai.update_task_status(task1['task_id'], "COMPLETE")
    ai.update_task_status(task2['task_id'], "IN_PROGRESS")
    
    print_step("Raport progres")
    progress = ai.get_task_progress()
    print(f"  📊 Total task-uri: {progress['total_tasks']}")
    print(f"  ✅ Complete: {progress['completed']}")
    print(f"  🔄 În progres: {progress['in_progress']}")
    print(f"  📈 Progres: {progress['progress_percentage']}%")
    
    # 3. DEMONSTRAȚIE PROCESARE FIȘIERE
    print_separator("MODUL PROCESARE FIȘIERE")
    
    print_step("Creare fișier de test")
    test_code = '''def hello_world():
    """Funcție de test"""
    print("Hello, World!")
    return "success"

class TestClass:
    def __init__(self):
        self.name = "Test"
    
    def get_name(self):
        return self.name

# Test principal
if __name__ == "__main__":
    hello_world()
    test = TestClass()
    print(test.get_name())
'''
    
    # Salvează fișierul de test
    ai.file_processor.write_file("test_example.py", test_code)
    print("  ✅ Fișier test_example.py creat")
    
    print_step("Analiză structură cod")
    analysis = ai.analyze_file("test_example.py")
    print(f"  📄 Limbaj: {analysis.get('language', 'N/A')}")
    print(f"  📏 Linii: {analysis.get('total_lines', 0)}")
    print(f"  🔧 Funcții: {len(analysis.get('functions', []))}")
    print(f"  🏗️  Clase: {len(analysis.get('classes', []))}")
    print(f"  📊 Complexitate: {analysis.get('complexity_score', 0)}")
    
    if analysis.get('functions'):
        print(f"  📝 Funcții găsite: {', '.join(analysis['functions'])}")
    if analysis.get('classes'):
        print(f"  📝 Clase găsite: {', '.join(analysis['classes'])}")
    
    print_step("Editare fișier")
    edit_result = ai.edit_file("test_example.py", 'print("Hello, World!")', 'print("Hello, AI Assistant!")')
    if edit_result.get('status') == 'success':
        print(f"  ✅ Editare reușită: {edit_result.get('replacements', 0)} înlocuiri")
    
    # 4. DEMONSTRAȚIE SETUP PROIECT COMPLET
    print_separator("SETUP PROIECT COMPLET")
    
    print_step("Configurare proiect nou")
    project_requirements = [
        "Creează interfață utilizator cu HTML/CSS",
        "Implementează logica backend în Python",
        "Adaugă bază de date SQLite",
        "Scrie teste unitare",
        "Creează documentație"
    ]
    
    project_result = ai.comprehensive_project_setup(
        "my_web_app", 
        "web_app", 
        project_requirements
    )
    
    print(f"  🎯 Proiect: {project_result['project_name']}")
    print(f"  📋 Pași completați:")
    for step in project_result.get('steps_completed', []):
        print(f"    ✅ {step}")
    
    if project_result.get('errors'):
        print(f"  ❌ Erori:")
        for error in project_result['errors']:
            print(f"    ❌ {error}")
    
    # 5. DEMONSTRAȚIE BAZĂ DE CUNOȘTINȚE
    print_separator("BAZA DE CUNOȘTINȚE")
    
    print_step("Salvare cunoștințe noi")
    ai.save_knowledge("ai_capabilities", {
        "modules": ["web_search", "task_management", "file_processing"],
        "features": ["code_analysis", "project_setup", "knowledge_persistence"],
        "demo_completed": True,
        "performance": "excellent"
    })
    
    print_step("Recuperare cunoștințe")
    web_knowledge = ai.retrieve_knowledge("web_searches")
    file_knowledge = ai.retrieve_knowledge("file_edits")
    project_knowledge = ai.retrieve_knowledge("projects")
    
    print(f"  🔍 Căutări web salvate: {len(web_knowledge)}")
    print(f"  📝 Editări fișiere salvate: {len(file_knowledge)}")
    print(f"  🏗️  Proiecte salvate: {len(project_knowledge)}")
    
    # 6. RAPORT FINAL
    print_separator("RAPORT FINAL")
    
    print("🎉 DEMONSTRAȚIA S-A ÎNCHEIAT CU SUCCES!")
    print("\n📊 SUMAR CAPACITĂȚI DEMONSTRATE:")
    print("  ✅ Căutare și fetch web")
    print("  ✅ Gestionare task-uri și progres")
    print("  ✅ Analiză și editare fișiere")
    print("  ✅ Setup proiecte complete")
    print("  ✅ Persistență cunoștințe")
    print("  ✅ Integrare module")
    
    print(f"\n🏠 Toate datele sunt salvate în: {ai.workspace_path}")
    print("📁 Structura creată:")
    print("  - knowledge_base.json (baza de cunoștințe)")
    print("  - tasks.json (task-uri și progres)")
    print("  - backups/ (backup-uri fișiere)")
    print("  - my_web_app/ (proiect demonstrativ)")
    
    print("\n💡 AI Assistant Local este complet funcțional!")
    print("🤖 Toate capacitățile au fost recreate cu succes!")
    
    return ai

if __name__ == "__main__":
    try:
        ai_instance = demo_ai_assistant()
        
        print("\n" + "="*60)
        print("🎯 DEMONSTRAȚIA S-A ÎNCHEIAT")
        print("Poți acum să folosești ai_instance pentru interacțiuni suplimentare")
        print("="*60)
        
    except Exception as e:
        print(f"\n❌ Eroare în demonstrație: {e}")
        import traceback
        traceback.print_exc()
