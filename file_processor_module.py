"""
Modul de procesare fișiere pentru AI Assistant Local
Simulează capacitățile de editare, analiză și manipulare fișiere
"""

import os
import json
import re
import shutil
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

class FileProcessor:
    def __init__(self, workspace_path: str = "."):
        self.workspace_path = Path(workspace_path)
        self.supported_extensions = {
            '.py': 'Python',
            '.js': 'JavaScript', 
            '.html': 'HTML',
            '.css': 'CSS',
            '.json': 'JSON',
            '.md': 'Markdown',
            '.txt': 'Text',
            '.yml': 'YAML',
            '.yaml': 'YAML',
            '.xml': 'XML',
            '.sql': 'SQL',
            '.sh': 'Shell',
            '.bat': 'Batch'
        }
        self.edit_history = []
    
    def read_file(self, file_path: str, encoding: str = 'utf-8') -> Dict[str, Any]:
        """Citește conținutul unui fișier"""
        try:
            full_path = self.workspace_path / file_path
            
            with open(full_path, 'r', encoding=encoding) as f:
                content = f.read()
            
            file_info = {
                "path": str(file_path),
                "content": content,
                "size": len(content),
                "lines": len(content.split('\n')),
                "language": self._detect_language(file_path),
                "last_modified": datetime.fromtimestamp(full_path.stat().st_mtime).isoformat(),
                "status": "success"
            }
            
            return file_info
            
        except Exception as e:
            return {
                "path": str(file_path),
                "error": str(e),
                "status": "error"
            }
    
    def write_file(self, file_path: str, content: str, encoding: str = 'utf-8', 
                   backup: bool = True) -> Dict[str, Any]:
        """Scrie conținut într-un fișier"""
        try:
            full_path = self.workspace_path / file_path
            
            # Creează directoarele dacă nu există
            full_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Backup dacă fișierul există
            if backup and full_path.exists():
                self._create_backup(full_path)
            
            with open(full_path, 'w', encoding=encoding) as f:
                f.write(content)
            
            # Adaugă în istoric
            self.edit_history.append({
                "action": "write",
                "file": str(file_path),
                "timestamp": datetime.now().isoformat(),
                "size": len(content)
            })
            
            return {
                "path": str(file_path),
                "size": len(content),
                "lines": len(content.split('\n')),
                "status": "success",
                "message": f"Fișier salvat: {file_path}"
            }
            
        except Exception as e:
            return {
                "path": str(file_path),
                "error": str(e),
                "status": "error"
            }
    
    def str_replace(self, file_path: str, old_str: str, new_str: str, 
                   count: int = -1) -> Dict[str, Any]:
        """Înlocuiește text într-un fișier (simulează str-replace-editor)"""
        try:
            # Citește fișierul
            file_info = self.read_file(file_path)
            if file_info["status"] == "error":
                return file_info
            
            content = file_info["content"]
            
            # Efectuează înlocuirea
            if count == -1:
                new_content = content.replace(old_str, new_str)
                replacements = content.count(old_str)
            else:
                new_content = content.replace(old_str, new_str, count)
                replacements = min(content.count(old_str), count)
            
            if replacements == 0:
                return {
                    "path": str(file_path),
                    "status": "warning",
                    "message": "Nu s-au găsit potriviri pentru înlocuire"
                }
            
            # Salvează fișierul modificat
            result = self.write_file(file_path, new_content)
            
            if result["status"] == "success":
                result["replacements"] = replacements
                result["message"] = f"Efectuate {replacements} înlocuiri în {file_path}"
                
                # Adaugă în istoric
                self.edit_history.append({
                    "action": "str_replace",
                    "file": str(file_path),
                    "old_str": old_str[:100] + "..." if len(old_str) > 100 else old_str,
                    "new_str": new_str[:100] + "..." if len(new_str) > 100 else new_str,
                    "replacements": replacements,
                    "timestamp": datetime.now().isoformat()
                })
            
            return result
            
        except Exception as e:
            return {
                "path": str(file_path),
                "error": str(e),
                "status": "error"
            }
    
    def insert_text(self, file_path: str, line_number: int, text: str) -> Dict[str, Any]:
        """Inserează text la o linie specifică"""
        try:
            file_info = self.read_file(file_path)
            if file_info["status"] == "error":
                return file_info
            
            lines = file_info["content"].split('\n')
            
            # Validează numărul liniei
            if line_number < 0 or line_number > len(lines):
                return {
                    "path": str(file_path),
                    "status": "error",
                    "message": f"Numărul liniei {line_number} este invalid"
                }
            
            # Inserează textul
            lines.insert(line_number, text)
            new_content = '\n'.join(lines)
            
            result = self.write_file(file_path, new_content)
            
            if result["status"] == "success":
                result["message"] = f"Text inserat la linia {line_number} în {file_path}"
                
                self.edit_history.append({
                    "action": "insert",
                    "file": str(file_path),
                    "line": line_number,
                    "text": text[:100] + "..." if len(text) > 100 else text,
                    "timestamp": datetime.now().isoformat()
                })
            
            return result
            
        except Exception as e:
            return {
                "path": str(file_path),
                "error": str(e),
                "status": "error"
            }
    
    def analyze_code_structure(self, file_path: str) -> Dict[str, Any]:
        """Analizează structura codului dintr-un fișier"""
        file_info = self.read_file(file_path)
        if file_info["status"] == "error":
            return file_info
        
        content = file_info["content"]
        language = file_info["language"]
        
        analysis = {
            "path": str(file_path),
            "language": language,
            "total_lines": file_info["lines"],
            "functions": [],
            "classes": [],
            "imports": [],
            "comments": [],
            "complexity_score": 0
        }
        
        if language == "Python":
            analysis.update(self._analyze_python_code(content))
        elif language == "JavaScript":
            analysis.update(self._analyze_javascript_code(content))
        elif language == "HTML":
            analysis.update(self._analyze_html_code(content))
        
        return analysis
    
    def search_in_file(self, file_path: str, pattern: str, regex: bool = False, 
                      case_sensitive: bool = False) -> Dict[str, Any]:
        """Caută un pattern într-un fișier"""
        try:
            file_info = self.read_file(file_path)
            if file_info["status"] == "error":
                return file_info
            
            content = file_info["content"]
            lines = content.split('\n')
            
            matches = []
            flags = 0 if case_sensitive else re.IGNORECASE
            
            for line_num, line in enumerate(lines, 1):
                if regex:
                    found = re.finditer(pattern, line, flags)
                    for match in found:
                        matches.append({
                            "line_number": line_num,
                            "line_content": line.strip(),
                            "match": match.group(),
                            "start": match.start(),
                            "end": match.end()
                        })
                else:
                    if case_sensitive:
                        if pattern in line:
                            matches.append({
                                "line_number": line_num,
                                "line_content": line.strip(),
                                "match": pattern
                            })
                    else:
                        if pattern.lower() in line.lower():
                            matches.append({
                                "line_number": line_num,
                                "line_content": line.strip(),
                                "match": pattern
                            })
            
            return {
                "path": str(file_path),
                "pattern": pattern,
                "matches_count": len(matches),
                "matches": matches,
                "status": "success"
            }
            
        except Exception as e:
            return {
                "path": str(file_path),
                "error": str(e),
                "status": "error"
            }
    
    def _detect_language(self, file_path: str) -> str:
        """Detectează limbajul de programare"""
        extension = Path(file_path).suffix.lower()
        return self.supported_extensions.get(extension, "Unknown")
    
    def _create_backup(self, file_path: Path):
        """Creează backup pentru un fișier"""
        backup_dir = self.workspace_path / "backups"
        backup_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"{file_path.stem}_{timestamp}{file_path.suffix}"
        backup_path = backup_dir / backup_name
        
        shutil.copy2(file_path, backup_path)
    
    def _analyze_python_code(self, content: str) -> Dict[str, Any]:
        """Analizează cod Python"""
        functions = re.findall(r'def\s+(\w+)\s*\(', content)
        classes = re.findall(r'class\s+(\w+)\s*[\(:]', content)
        imports = re.findall(r'(?:from\s+\w+\s+)?import\s+([^\n]+)', content)
        
        return {
            "functions": functions,
            "classes": classes,
            "imports": [imp.strip() for imp in imports],
            "complexity_score": len(functions) + len(classes) * 2
        }
    
    def _analyze_javascript_code(self, content: str) -> Dict[str, Any]:
        """Analizează cod JavaScript"""
        functions = re.findall(r'function\s+(\w+)\s*\(', content)
        arrow_functions = re.findall(r'(\w+)\s*=\s*\([^)]*\)\s*=>', content)
        classes = re.findall(r'class\s+(\w+)\s*{', content)
        
        return {
            "functions": functions + arrow_functions,
            "classes": classes,
            "complexity_score": len(functions) + len(arrow_functions) + len(classes) * 2
        }
    
    def _analyze_html_code(self, content: str) -> Dict[str, Any]:
        """Analizează cod HTML"""
        tags = re.findall(r'<(\w+)', content)
        unique_tags = list(set(tags))
        
        return {
            "tags": unique_tags,
            "total_tags": len(tags),
            "complexity_score": len(unique_tags)
        }
    
    def get_edit_history(self) -> List[Dict[str, Any]]:
        """Returnează istoricul editărilor"""
        return self.edit_history
    
    def clear_edit_history(self):
        """Șterge istoricul editărilor"""
        self.edit_history.clear()
        print("🗑️ Istoric editări șters")

# Exemplu de utilizare
if __name__ == "__main__":
    fp = FileProcessor()
    
    # Test citire fișier
    result = fp.read_file("ai_assistant_core.py")
    if result["status"] == "success":
        print(f"📄 Fișier citit: {result['lines']} linii, {result['size']} caractere")
    
    # Test analiză cod
    analysis = fp.analyze_code_structure("ai_assistant_core.py")
    if "functions" in analysis:
        print(f"🔍 Analiză: {len(analysis['functions'])} funcții, {len(analysis['classes'])} clase")
