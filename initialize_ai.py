#!/usr/bin/env python3
"""
Script de inițializare pentru AI Assistant Local
Acest script demonstrează cum ai putea "reface" un asistent AI local
"""

import os
import json
from ai_assistant_core import AIAssistantCore

def initialize_ai_assistant():
    """Inițializează asistentul AI în mediul local"""
    
    print("🚀 Inițializare AI Assistant Local...")
    print("=" * 50)
    
    # Verifică dacă folderul workspace există
    workspace = "/home/<USER>/Desktop/claude ai"
    if not os.path.exists(workspace):
        os.makedirs(workspace)
        print(f"✅ Workspace creat: {workspace}")
    
    # Inițializează core-ul AI
    ai = AIAssistantCore(workspace)
    
    # Încarcă configurația
    try:
        with open("config.json", "r", encoding="utf-8") as f:
            config = json.load(f)
        print("✅ Configurație încărcată cu succes")
    except Exception as e:
        print(f"⚠️  Eroare la încărcarea configurației: {e}")
        config = {}
    
    # Creează structura de foldere pentru AI
    ai_folders = [
        "knowledge_base",
        "projects",
        "backups",
        "logs",
        "templates"
    ]
    
    for folder in ai_folders:
        folder_path = os.path.join(workspace, folder)
        os.makedirs(folder_path, exist_ok=True)
        print(f"📁 Folder creat: {folder}")
    
    # Salvează cunoștințe inițiale
    initial_knowledge = {
        "programming_languages": {
            "Python": "Limbaj versatil pentru dezvoltare generală",
            "JavaScript": "Limbaj pentru dezvoltare web",
            "HTML": "Markup pentru structura web",
            "CSS": "Stilizare pentru pagini web"
        },
        "best_practices": [
            "Scrie cod curat și lizibil",
            "Folosește nume descriptive pentru variabile",
            "Comentează codul complex",
            "Testează codul înainte de deployment",
            "Folosește control de versiuni (Git)"
        ],
        "helpful_commands": {
            "git": "Control de versiuni",
            "pip": "Manager de pachete Python",
            "npm": "Manager de pachete Node.js",
            "code": "Editor VS Code"
        }
    }
    
    for topic, data in initial_knowledge.items():
        ai.save_knowledge(topic, data)
        print(f"💾 Cunoștințe salvate: {topic}")
    
    # Creează un fișier README
    readme_content = """# AI Assistant Local Instance

Acest folder conține o recreare locală a capacităților AI Assistant.

## Structura folderelor:
- `knowledge_base/` - Baza de cunoștințe persistentă
- `projects/` - Proiecte create de AI
- `backups/` - Backup-uri automate
- `logs/` - Jurnale de activitate
- `templates/` - Template-uri pentru proiecte

## Utilizare:
```python
from ai_assistant_core import AIAssistantCore

# Inițializează AI
ai = AIAssistantCore(".")

# Analizează cod
analysis = ai.analyze_code("example.py")

# Creează proiect nou
ai.create_project_structure("my_app", "web_app")
```

## Caracteristici:
- ✅ Analiză de cod
- ✅ Gestionare proiecte
- ✅ Bază de cunoștințe persistentă
- ✅ Backup automat
- ✅ Suport multilingv

Creat cu ❤️ pentru a demonstra cum ai putea ajuta un AI să-și recreeze capacitățile local.
"""
    
    with open(os.path.join(workspace, "README.md"), "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    print("📝 README creat")
    print("=" * 50)
    print("🎉 AI Assistant Local inițializat cu succes!")
    print(f"📍 Locație: {workspace}")
    print("💡 Poți acum să folosești ai_assistant_core.py pentru a interacționa cu AI-ul local")
    
    return ai

if __name__ == "__main__":
    ai_instance = initialize_ai_assistant()
    
    # Demonstrează funcționalitatea
    print("\n🔍 Demonstrație rapidă:")
    
    # Analizează fișierul curent
    analysis = ai_instance.analyze_code("ai_assistant_core.py")
    print(f"📊 Analiză cod: {analysis['lines_count']} linii, {len(analysis['functions'])} funcții, {len(analysis['classes'])} clase")
    
    # Creează un proiect de test
    result = ai_instance.create_project_structure("test_project", "web_app")
    print(f"🏗️  {result}")
