"""
Conversation Manager - Sistem avansat pentru gestionarea conversațiilor
Gestionează contextul, istoricul și fluxul conversațiilor cu AI
"""

import json
import uuid
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import re

class ConversationState(Enum):
    """Stările unei conversații"""
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    ARCHIVED = "archived"

class MessageType(Enum):
    """Tipurile de mesaje"""
    USER_INPUT = "user_input"
    AI_RESPONSE = "ai_response"
    SYSTEM_MESSAGE = "system_message"
    ERROR_MESSAGE = "error_message"
    CONTEXT_UPDATE = "context_update"

@dataclass
class ConversationMessage:
    """Structură pentru un mesaj în conversație"""
    id: str
    session_id: str
    message_type: MessageType
    content: str
    metadata: Dict[str, Any]
    timestamp: datetime
    processed: bool = False

@dataclass
class ConversationSession:
    """Structură pentru o sesiune de conversație"""
    session_id: str
    user_id: str
    title: str
    state: ConversationState
    created_at: datetime
    last_activity: datetime
    message_count: int
    context: Dict[str, Any]
    preferences: Dict[str, Any]
    summary: str = ""

class ConversationManager:
    """Manager principal pentru conversații"""
    
    def __init__(self, workspace_path: str = ".", db_path: str = "conversations.db"):
        self.workspace_path = workspace_path
        self.db_path = db_path
        self.active_sessions = {}
        self.context_memory = {}
        
        # Inițializează baza de date
        self.init_database()
        
        # Încarcă sesiunile active
        self.load_active_sessions()
        
        print("💬 Conversation Manager inițializat!")
    
    def init_database(self):
        """Inițializează baza de date pentru conversații"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Tabel pentru sesiuni
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS conversation_sessions (
                    session_id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    title TEXT NOT NULL,
                    state TEXT NOT NULL,
                    created_at TIMESTAMP NOT NULL,
                    last_activity TIMESTAMP NOT NULL,
                    message_count INTEGER DEFAULT 0,
                    context TEXT,
                    preferences TEXT,
                    summary TEXT
                )
            ''')
            
            # Tabel pentru mesaje
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS conversation_messages (
                    id TEXT PRIMARY KEY,
                    session_id TEXT NOT NULL,
                    message_type TEXT NOT NULL,
                    content TEXT NOT NULL,
                    metadata TEXT,
                    timestamp TIMESTAMP NOT NULL,
                    processed BOOLEAN DEFAULT FALSE,
                    FOREIGN KEY (session_id) REFERENCES conversation_sessions (session_id)
                )
            ''')
            
            # Tabel pentru context
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS conversation_context (
                    session_id TEXT PRIMARY KEY,
                    current_topic TEXT,
                    active_capabilities TEXT,
                    user_preferences TEXT,
                    conversation_flow TEXT,
                    last_updated TIMESTAMP NOT NULL,
                    FOREIGN KEY (session_id) REFERENCES conversation_sessions (session_id)
                )
            ''')
            
            conn.commit()
    
    def create_session(self, user_id: str, title: str = None) -> ConversationSession:
        """Creează o nouă sesiune de conversație"""
        session_id = str(uuid.uuid4())
        
        if not title:
            title = f"Conversație {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        
        session = ConversationSession(
            session_id=session_id,
            user_id=user_id,
            title=title,
            state=ConversationState.ACTIVE,
            created_at=datetime.now(),
            last_activity=datetime.now(),
            message_count=0,
            context={
                "topics": [],
                "capabilities_used": [],
                "user_intent_history": [],
                "complexity_progression": []
            },
            preferences={
                "response_style": "detailed",
                "technical_level": "intermediate",
                "language": "romanian"
            }
        )
        
        # Salvează în baza de date
        self.save_session(session)
        
        # Adaugă la sesiunile active
        self.active_sessions[session_id] = session
        
        print(f"💬 Sesiune nouă creată: {session_id[:8]} - {title}")
        return session
    
    def add_message(self, session_id: str, message_type: MessageType, 
                   content: str, metadata: Dict[str, Any] = None) -> ConversationMessage:
        """Adaugă un mesaj la conversație"""
        if session_id not in self.active_sessions:
            raise ValueError(f"Sesiunea {session_id} nu există sau nu este activă")
        
        message_id = str(uuid.uuid4())
        message = ConversationMessage(
            id=message_id,
            session_id=session_id,
            message_type=message_type,
            content=content,
            metadata=metadata or {},
            timestamp=datetime.now()
        )
        
        # Salvează mesajul în baza de date
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO conversation_messages 
                (id, session_id, message_type, content, metadata, timestamp, processed)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                message.id, message.session_id, message.message_type.value,
                message.content, json.dumps(message.metadata),
                message.timestamp, message.processed
            ))
            conn.commit()
        
        # Actualizează sesiunea
        session = self.active_sessions[session_id]
        session.message_count += 1
        session.last_activity = datetime.now()
        
        # Actualizează contextul
        self.update_context(session_id, message)
        
        # Salvează sesiunea actualizată
        self.save_session(session)
        
        return message
    
    def update_context(self, session_id: str, message: ConversationMessage):
        """Actualizează contextul conversației"""
        session = self.active_sessions[session_id]
        
        # Analizează mesajul pentru actualizarea contextului
        if message.message_type == MessageType.USER_INPUT:
            # Extrage topicul
            topic = self.extract_topic_from_message(message.content)
            if topic and topic not in session.context["topics"]:
                session.context["topics"].append(topic)
            
            # Analizează intenția
            intent = self.analyze_message_intent(message.content)
            session.context["user_intent_history"].append({
                "intent": intent,
                "timestamp": message.timestamp.isoformat(),
                "confidence": 0.8  # Placeholder
            })
            
            # Evaluează complexitatea
            complexity = self.evaluate_message_complexity(message.content)
            session.context["complexity_progression"].append({
                "complexity": complexity,
                "timestamp": message.timestamp.isoformat()
            })
        
        elif message.message_type == MessageType.AI_RESPONSE:
            # Extrage capacitățile folosite din metadata
            capabilities = message.metadata.get("capabilities_used", [])
            for cap in capabilities:
                if cap not in session.context["capabilities_used"]:
                    session.context["capabilities_used"].append(cap)
        
        # Limitează istoricul pentru a evita creșterea excesivă
        session.context["user_intent_history"] = session.context["user_intent_history"][-20:]
        session.context["complexity_progression"] = session.context["complexity_progression"][-20:]
        session.context["topics"] = session.context["topics"][-10:]
    
    def get_conversation_history(self, session_id: str, limit: int = 50) -> List[ConversationMessage]:
        """Obține istoricul conversației"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT id, session_id, message_type, content, metadata, timestamp, processed
                FROM conversation_messages
                WHERE session_id = ?
                ORDER BY timestamp DESC
                LIMIT ?
            ''', (session_id, limit))
            
            rows = cursor.fetchall()
            messages = []
            
            for row in rows:
                message = ConversationMessage(
                    id=row[0],
                    session_id=row[1],
                    message_type=MessageType(row[2]),
                    content=row[3],
                    metadata=json.loads(row[4]) if row[4] else {},
                    timestamp=datetime.fromisoformat(row[5]),
                    processed=bool(row[6])
                )
                messages.append(message)
            
            return list(reversed(messages))  # Returnează în ordine cronologică
    
    def generate_conversation_summary(self, session_id: str) -> str:
        """Generează un sumar al conversației"""
        if session_id not in self.active_sessions:
            return "Sesiunea nu există"
        
        session = self.active_sessions[session_id]
        messages = self.get_conversation_history(session_id)
        
        # Analizează conversația
        user_messages = [m for m in messages if m.message_type == MessageType.USER_INPUT]
        ai_messages = [m for m in messages if m.message_type == MessageType.AI_RESPONSE]
        
        # Extrage informații cheie
        topics = session.context.get("topics", [])
        capabilities = session.context.get("capabilities_used", [])
        
        # Generează sumarul
        summary_parts = []
        summary_parts.append(f"Conversație cu {len(user_messages)} întrebări și {len(ai_messages)} răspunsuri.")
        
        if topics:
            summary_parts.append(f"Topicuri discutate: {', '.join(topics[:5])}.")
        
        if capabilities:
            summary_parts.append(f"Capacități folosite: {', '.join(capabilities[:5])}.")
        
        # Analizează progresul complexității
        complexity_history = session.context.get("complexity_progression", [])
        if len(complexity_history) >= 2:
            first_complexity = complexity_history[0]["complexity"]
            last_complexity = complexity_history[-1]["complexity"]
            if last_complexity > first_complexity:
                summary_parts.append("Complexitatea întrebărilor a crescut pe parcursul conversației.")
            elif last_complexity < first_complexity:
                summary_parts.append("Conversația a devenit mai simplă spre final.")
        
        summary = " ".join(summary_parts)
        
        # Salvează sumarul
        session.summary = summary
        self.save_session(session)
        
        return summary
    
    def extract_topic_from_message(self, content: str) -> Optional[str]:
        """Extrage topicul principal dintr-un mesaj"""
        content_lower = content.lower()
        
        # Mapare cuvinte cheie -> topicuri
        topic_keywords = {
            "programare": ["cod", "funcție", "algoritm", "programare", "dezvoltare"],
            "web": ["site", "pagină", "html", "css", "javascript", "web"],
            "ai": ["inteligență artificială", "ai", "neural", "învățare", "model"],
            "baze de date": ["bază de date", "sql", "mysql", "postgresql", "mongodb"],
            "testing": ["test", "testare", "debugging", "bug", "eroare"],
            "proiect": ["proiect", "aplicație", "sistem", "arhitectură"],
            "învățare": ["învăț", "tutorial", "curs", "documentație", "ghid"]
        }
        
        for topic, keywords in topic_keywords.items():
            if any(keyword in content_lower for keyword in keywords):
                return topic
        
        return None
    
    def analyze_message_intent(self, content: str) -> str:
        """Analizează intenția dintr-un mesaj"""
        content_lower = content.lower()
        
        # Mapare pattern-uri -> intenții
        intent_patterns = {
            "întrebare": ["ce", "cum", "când", "unde", "de ce", "care", "?"],
            "cerere_ajutor": ["ajută", "poți să", "vreau să", "am nevoie"],
            "creare": ["creează", "generează", "fă", "construiește"],
            "analiză": ["analizează", "verifică", "evaluează", "studiază"],
            "explicație": ["explică", "descrie", "spune-mi", "clarifica"],
            "comparație": ["compară", "diferența", "mai bun", "versus"],
            "recomandare": ["recomandă", "sugerează", "ce ar trebui", "sfat"]
        }
        
        for intent, patterns in intent_patterns.items():
            if any(pattern in content_lower for pattern in patterns):
                return intent
        
        return "general"
    
    def evaluate_message_complexity(self, content: str) -> int:
        """Evaluează complexitatea unui mesaj (1-5)"""
        # Factori de complexitate
        word_count = len(content.split())
        technical_terms = len(re.findall(r'\b(?:algoritm|funcție|clasă|obiect|variabilă|metodă|API|framework|library|database|query|optimization|performance|architecture|design pattern|inheritance|polymorphism|encapsulation|abstraction)\b', content.lower()))
        
        complexity_score = 1
        
        # Bazat pe lungime
        if word_count > 50:
            complexity_score += 1
        if word_count > 100:
            complexity_score += 1
        
        # Bazat pe termeni tehnici
        if technical_terms > 2:
            complexity_score += 1
        if technical_terms > 5:
            complexity_score += 1
        
        return min(complexity_score, 5)
    
    def save_session(self, session: ConversationSession):
        """Salvează o sesiune în baza de date"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO conversation_sessions
                (session_id, user_id, title, state, created_at, last_activity, 
                 message_count, context, preferences, summary)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                session.session_id, session.user_id, session.title,
                session.state.value, session.created_at, session.last_activity,
                session.message_count, json.dumps(session.context),
                json.dumps(session.preferences), session.summary
            ))
            conn.commit()
    
    def load_active_sessions(self):
        """Încarcă sesiunile active din baza de date"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT session_id, user_id, title, state, created_at, last_activity,
                           message_count, context, preferences, summary
                    FROM conversation_sessions
                    WHERE state = ?
                ''', (ConversationState.ACTIVE.value,))
                
                rows = cursor.fetchall()
                
                for row in rows:
                    session = ConversationSession(
                        session_id=row[0],
                        user_id=row[1],
                        title=row[2],
                        state=ConversationState(row[3]),
                        created_at=datetime.fromisoformat(row[4]),
                        last_activity=datetime.fromisoformat(row[5]),
                        message_count=row[6],
                        context=json.loads(row[7]) if row[7] else {},
                        preferences=json.loads(row[8]) if row[8] else {},
                        summary=row[9] or ""
                    )
                    self.active_sessions[session.session_id] = session
                
                print(f"💬 Încărcate {len(self.active_sessions)} sesiuni active")
                
        except Exception as e:
            print(f"⚠️ Eroare la încărcarea sesiunilor: {e}")
    
    def get_session_stats(self) -> Dict[str, Any]:
        """Obține statistici despre sesiuni"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Total sesiuni
            cursor.execute("SELECT COUNT(*) FROM conversation_sessions")
            total_sessions = cursor.fetchone()[0]
            
            # Sesiuni active
            cursor.execute("SELECT COUNT(*) FROM conversation_sessions WHERE state = ?", 
                          (ConversationState.ACTIVE.value,))
            active_sessions = cursor.fetchone()[0]
            
            # Total mesaje
            cursor.execute("SELECT COUNT(*) FROM conversation_messages")
            total_messages = cursor.fetchone()[0]
            
            # Mesaje pe zi (ultima săptămână)
            week_ago = datetime.now() - timedelta(days=7)
            cursor.execute("SELECT COUNT(*) FROM conversation_messages WHERE timestamp > ?", 
                          (week_ago,))
            messages_last_week = cursor.fetchone()[0]
            
            return {
                "total_sessions": total_sessions,
                "active_sessions": active_sessions,
                "total_messages": total_messages,
                "messages_last_week": messages_last_week,
                "average_messages_per_session": total_messages / total_sessions if total_sessions > 0 else 0
            }

# Exemplu de utilizare
if __name__ == "__main__":
    manager = ConversationManager()
    
    # Creează o sesiune de test
    session = manager.create_session("test_user", "Test Conversation")
    
    # Adaugă mesaje
    manager.add_message(session.session_id, MessageType.USER_INPUT, 
                       "Cum pot să creez o funcție în Python?")
    manager.add_message(session.session_id, MessageType.AI_RESPONSE,
                       "Pentru a crea o funcție în Python, folosești cuvântul cheie 'def'...",
                       {"capabilities_used": ["code_generation", "documentation"]})
    
    # Generează sumar
    summary = manager.generate_conversation_summary(session.session_id)
    print(f"Sumar conversație: {summary}")
    
    # Statistici
    stats = manager.get_session_stats()
    print(f"Statistici: {stats}")
