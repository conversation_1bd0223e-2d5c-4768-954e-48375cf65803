"""
AI Helper Methods - Metode helper pentru toate capacitățile AI
Acest modul conține implementările concrete pentru metodele helper
"""

import os
import re
import ast
import json
import random
import hashlib
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

class AIHelperMethods:
    """Clasa cu toate metodele helper pentru AI"""
    
    def __init__(self, workspace_path: str, db_path: str):
        self.workspace_path = Path(workspace_path)
        self.db_path = db_path
    
    # ===== METODE HELPER PENTRU ANALIZA CODULUI =====
    
    def analyze_code_file(self, file_path: Path) -> Dict[str, Any]:
        """Analizează un fișier de cod în detaliu"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            analysis = {
                "file_path": str(file_path),
                "language": self.detect_file_language(file_path),
                "size": len(content),
                "lines": len(content.split('\n')),
                "functions": [],
                "classes": [],
                "imports": [],
                "complexity_score": 0,
                "quality_metrics": {}
            }
            
            if analysis["language"] == "Python":
                analysis.update(self.analyze_python_file(content))
            elif analysis["language"] == "JavaScript":
                analysis.update(self.analyze_javascript_file(content))
            elif analysis["language"] == "HTML":
                analysis.update(self.analyze_html_file(content))
            elif analysis["language"] == "CSS":
                analysis.update(self.analyze_css_file(content))
            
            # Calculează metrici de calitate
            analysis["quality_metrics"] = self.calculate_code_quality_metrics(content, analysis)
            
            return analysis
            
        except Exception as e:
            return {"error": str(e), "file_path": str(file_path)}
    
    def analyze_python_file(self, content: str) -> Dict[str, Any]:
        """Analizează specific fișiere Python"""
        try:
            tree = ast.parse(content)
            
            functions = []
            classes = []
            imports = []
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    functions.append({
                        "name": node.name,
                        "line": node.lineno,
                        "args": len(node.args.args),
                        "docstring": ast.get_docstring(node),
                        "complexity": self.calculate_function_complexity(node)
                    })
                elif isinstance(node, ast.ClassDef):
                    classes.append({
                        "name": node.name,
                        "line": node.lineno,
                        "methods": len([n for n in node.body if isinstance(n, ast.FunctionDef)]),
                        "docstring": ast.get_docstring(node)
                    })
                elif isinstance(node, (ast.Import, ast.ImportFrom)):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            imports.append(alias.name)
                    else:
                        imports.append(node.module or "")
            
            return {
                "functions": functions,
                "classes": classes,
                "imports": list(set(imports)),
                "complexity_score": sum(f["complexity"] for f in functions) + len(classes) * 2
            }
            
        except SyntaxError as e:
            return {
                "functions": [],
                "classes": [],
                "imports": [],
                "complexity_score": 0,
                "syntax_error": str(e)
            }
    
    def analyze_javascript_file(self, content: str) -> Dict[str, Any]:
        """Analizează specific fișiere JavaScript"""
        functions = re.findall(r'function\s+(\w+)\s*\([^)]*\)', content)
        arrow_functions = re.findall(r'(\w+)\s*=\s*\([^)]*\)\s*=>', content)
        classes = re.findall(r'class\s+(\w+)\s*{', content)
        imports = re.findall(r'import\s+.*?from\s+[\'"]([^\'"]+)[\'"]', content)
        
        return {
            "functions": [{"name": f, "type": "function"} for f in functions] + 
                        [{"name": f, "type": "arrow_function"} for f in arrow_functions],
            "classes": [{"name": c} for c in classes],
            "imports": imports,
            "complexity_score": len(functions) + len(arrow_functions) + len(classes) * 2
        }
    
    def analyze_html_file(self, content: str) -> Dict[str, Any]:
        """Analizează specific fișiere HTML"""
        tags = re.findall(r'<(\w+)', content)
        unique_tags = list(set(tags))
        
        # Analizează structura
        title = re.search(r'<title>(.*?)</title>', content, re.IGNORECASE)
        meta_tags = re.findall(r'<meta[^>]*>', content, re.IGNORECASE)
        scripts = re.findall(r'<script[^>]*src=[\'"]([^\'"]+)[\'"]', content, re.IGNORECASE)
        stylesheets = re.findall(r'<link[^>]*href=[\'"]([^\'"]+\.css)[\'"]', content, re.IGNORECASE)
        
        return {
            "tags": unique_tags,
            "total_tags": len(tags),
            "title": title.group(1) if title else None,
            "meta_tags": len(meta_tags),
            "external_scripts": scripts,
            "external_stylesheets": stylesheets,
            "complexity_score": len(unique_tags)
        }
    
    def analyze_css_file(self, content: str) -> Dict[str, Any]:
        """Analizează specific fișiere CSS"""
        selectors = re.findall(r'([^{]+){', content)
        properties = re.findall(r'(\w+(?:-\w+)*)\s*:', content)
        
        return {
            "selectors": len(selectors),
            "properties": len(set(properties)),
            "rules": len(selectors),
            "complexity_score": len(selectors) + len(set(properties))
        }
    
    def calculate_function_complexity(self, node: ast.FunctionDef) -> int:
        """Calculează complexitatea unei funcții Python"""
        complexity = 1  # Complexitate de bază
        
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.Try)):
                complexity += 1
            elif isinstance(child, ast.BoolOp):
                complexity += len(child.values) - 1
        
        return complexity
    
    def calculate_code_quality_metrics(self, content: str, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Calculează metrici de calitate pentru cod"""
        lines = content.split('\n')
        
        # Calculează diverse metrici
        blank_lines = sum(1 for line in lines if not line.strip())
        comment_lines = sum(1 for line in lines if line.strip().startswith('#') or line.strip().startswith('//'))
        
        return {
            "blank_lines": blank_lines,
            "comment_lines": comment_lines,
            "code_lines": len(lines) - blank_lines - comment_lines,
            "comment_ratio": comment_lines / len(lines) if lines else 0,
            "average_line_length": sum(len(line) for line in lines) / len(lines) if lines else 0,
            "maintainability_index": self.calculate_maintainability_index(analysis)
        }
    
    def calculate_maintainability_index(self, analysis: Dict[str, Any]) -> float:
        """Calculează indexul de mentenabilitate"""
        # Formula simplificată pentru indexul de mentenabilitate
        complexity = analysis.get("complexity_score", 1)
        lines = analysis.get("lines", 1)
        
        # Normalizează valorile
        complexity_factor = max(0, 100 - complexity * 2)
        size_factor = max(0, 100 - lines / 10)
        
        return (complexity_factor + size_factor) / 2
    
    def detect_file_language(self, file_path: Path) -> str:
        """Detectează limbajul unui fișier"""
        extension_map = {
            '.py': 'Python',
            '.js': 'JavaScript',
            '.ts': 'TypeScript',
            '.html': 'HTML',
            '.css': 'CSS',
            '.java': 'Java',
            '.cpp': 'C++',
            '.c': 'C',
            '.cs': 'C#',
            '.php': 'PHP',
            '.rb': 'Ruby',
            '.go': 'Go',
            '.rs': 'Rust',
            '.swift': 'Swift',
            '.kt': 'Kotlin'
        }
        
        return extension_map.get(file_path.suffix.lower(), 'Unknown')
    
    # ===== METODE HELPER PENTRU CĂUTARE WEB =====
    
    def extract_search_terms(self, request: str) -> List[str]:
        """Extrage termenii de căutare din cerere"""
        # Elimină cuvintele comune
        stop_words = {'și', 'sau', 'de', 'la', 'în', 'cu', 'pentru', 'despre', 'cum', 'ce', 'când', 'unde'}
        
        # Extrage cuvintele importante
        words = re.findall(r'\b\w+\b', request.lower())
        search_terms = [word for word in words if word not in stop_words and len(word) > 2]
        
        return search_terms[:5]  # Limitează la 5 termeni
    
    def perform_web_search(self, term: str) -> List[Dict[str, Any]]:
        """Simulează căutarea web pentru un termen"""
        # Generează rezultate simulate bazate pe termen
        base_results = [
            {
                "title": f"Tutorial complet pentru {term}",
                "url": f"https://tutorial-{term.replace(' ', '-')}.com",
                "snippet": f"Ghid complet și detaliat pentru {term}. Învață pas cu pas toate conceptele importante.",
                "relevance": 0.9
            },
            {
                "title": f"Documentația oficială {term}",
                "url": f"https://docs.{term.replace(' ', '-')}.org",
                "snippet": f"Documentația oficială pentru {term}. Referință completă cu exemple și API.",
                "relevance": 0.95
            },
            {
                "title": f"Exemple practice {term}",
                "url": f"https://examples-{term.replace(' ', '-')}.dev",
                "snippet": f"Colecție de exemple practice și cod pentru {term}. Soluții ready-to-use.",
                "relevance": 0.8
            },
            {
                "title": f"Best practices pentru {term}",
                "url": f"https://bestpractices-{term.replace(' ', '-')}.com",
                "snippet": f"Cele mai bune practici și recomandări pentru {term}. Evită greșelile comune.",
                "relevance": 0.85
            },
            {
                "title": f"Forum și comunitate {term}",
                "url": f"https://community-{term.replace(' ', '-')}.org",
                "snippet": f"Comunitatea activă pentru {term}. Întrebări, răspunsuri și discuții.",
                "relevance": 0.7
            }
        ]
        
        # Sortează după relevanță
        return sorted(base_results, key=lambda x: x['relevance'], reverse=True)[:3]
    
    def generate_search_summary(self, results: List[Dict[str, Any]]) -> str:
        """Generează un sumar al rezultatelor căutării"""
        if not results:
            return "Nu s-au găsit rezultate relevante."
        
        summary_parts = []
        
        # Analizează tipurile de resurse găsite
        resource_types = []
        for result in results:
            title_lower = result['title'].lower()
            if 'tutorial' in title_lower:
                resource_types.append('tutorial')
            elif 'documentație' in title_lower or 'docs' in title_lower:
                resource_types.append('documentație')
            elif 'exemple' in title_lower:
                resource_types.append('exemple')
            elif 'best practices' in title_lower:
                resource_types.append('best practices')
        
        if resource_types:
            summary_parts.append(f"Am găsit {len(results)} resurse relevante, incluzând {', '.join(set(resource_types))}.")
        
        # Adaugă informații despre relevanță
        high_relevance = [r for r in results if r.get('relevance', 0) > 0.8]
        if high_relevance:
            summary_parts.append(f"{len(high_relevance)} dintre resurse au relevanță înaltă.")
        
        return " ".join(summary_parts)
    
    # ===== METODE HELPER PENTRU OPERAȚII FIȘIERE =====
    
    def identify_file_operations(self, request: str) -> List[Dict[str, Any]]:
        """Identifică operațiile cu fișiere din cerere"""
        operations = []
        request_lower = request.lower()
        
        # Mapare cuvinte cheie -> operații
        operation_keywords = {
            'creează': 'create',
            'salvează': 'save',
            'citește': 'read',
            'editează': 'edit',
            'șterge': 'delete',
            'copiază': 'copy',
            'mută': 'move',
            'redenumește': 'rename'
        }
        
        # Extrage numele fișierelor
        file_patterns = re.findall(r'[\w\-_]+\.\w+', request)
        
        for keyword, operation in operation_keywords.items():
            if keyword in request_lower:
                operations.append({
                    'type': operation,
                    'files': file_patterns,
                    'context': request
                })
        
        return operations
    
    def execute_file_operation(self, operation: Dict[str, Any]) -> Dict[str, Any]:
        """Execută o operație cu fișiere"""
        op_type = operation['type']
        files = operation.get('files', [])
        
        result = {
            'operation': op_type,
            'status': 'success',
            'files_processed': [],
            'details': {}
        }
        
        try:
            if op_type == 'create' and files:
                for file_name in files:
                    file_path = self.workspace_path / file_name
                    if not file_path.exists():
                        file_path.touch()
                        result['files_processed'].append(str(file_path))
            
            elif op_type == 'read' and files:
                for file_name in files:
                    file_path = self.workspace_path / file_name
                    if file_path.exists():
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        result['details'][file_name] = {
                            'size': len(content),
                            'lines': len(content.split('\n'))
                        }
                        result['files_processed'].append(str(file_path))
            
            # Adaugă alte operații după necesitate
            
        except Exception as e:
            result['status'] = 'error'
            result['error'] = str(e)
        
        return result

    # ===== METODE HELPER PENTRU CAPACITĂȚI AVANSATE =====

    def identify_usage_patterns(self) -> List[Dict[str, Any]]:
        """Identifică pattern-uri în utilizarea sistemului"""
        patterns = []

        # Simulează pattern-uri de utilizare
        patterns.append({
            "pattern_type": "frequent_requests",
            "description": "Cereri frecvente pentru analiză cod",
            "frequency": 0.8,
            "trend": "increasing"
        })

        patterns.append({
            "pattern_type": "time_based",
            "description": "Activitate mai mare în timpul zilei",
            "frequency": 0.6,
            "trend": "stable"
        })

        return patterns

    def make_behavioral_adaptations(self, patterns: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Face adaptări comportamentale bazate pe pattern-uri"""
        adaptations = []

        for pattern in patterns:
            if pattern["pattern_type"] == "frequent_requests":
                adaptations.append({
                    "adaptation_type": "priority_adjustment",
                    "description": "Prioritizează analizele de cod",
                    "impact": "medium"
                })

        return adaptations

    def calculate_performance_improvements(self) -> Dict[str, float]:
        """Calculează îmbunătățiri de performanță"""
        return {
            "response_time": 0.15,  # 15% îmbunătățire
            "accuracy": 0.08,       # 8% îmbunătățire
            "user_satisfaction": 0.12  # 12% îmbunătățire
        }

    def calculate_learning_confidence(self, patterns: List[Dict[str, Any]]) -> float:
        """Calculează încrederea în învățare"""
        if not patterns:
            return 0.0

        confidence = sum(p.get("frequency", 0) for p in patterns) / len(patterns)
        return min(confidence, 1.0)

    def decompose_logical_problem(self, request: str) -> List[Dict[str, Any]]:
        """Descompune o problemă în pași logici"""
        steps = []

        # Analizează cererea pentru a identifica pașii
        if "creează" in request.lower():
            steps.append({"step": 1, "action": "Analizează cerințele", "type": "analysis"})
            steps.append({"step": 2, "action": "Planifică structura", "type": "planning"})
            steps.append({"step": 3, "action": "Implementează soluția", "type": "implementation"})
            steps.append({"step": 4, "action": "Testează rezultatul", "type": "validation"})

        elif "analizează" in request.lower():
            steps.append({"step": 1, "action": "Colectează datele", "type": "data_collection"})
            steps.append({"step": 2, "action": "Procesează informațiile", "type": "processing"})
            steps.append({"step": 3, "action": "Identifică pattern-uri", "type": "pattern_recognition"})
            steps.append({"step": 4, "action": "Generează concluzii", "type": "conclusion"})

        else:
            steps.append({"step": 1, "action": "Înțelege problema", "type": "comprehension"})
            steps.append({"step": 2, "action": "Găsește soluții", "type": "solution_finding"})
            steps.append({"step": 3, "action": "Evaluează opțiunile", "type": "evaluation"})

        return steps

    def identify_assumptions(self, request: str, logical_steps: List[Dict[str, Any]]) -> List[str]:
        """Identifică asumpțiile dintr-o cerere"""
        assumptions = []

        # Analizează cererea pentru asumpții implicite
        if "cel mai bun" in request.lower():
            assumptions.append("Există o soluție optimă unică")

        if "rapid" in request.lower() or "urgent" in request.lower():
            assumptions.append("Timpul este o constrângere critică")

        if "simplu" in request.lower():
            assumptions.append("Soluția trebuie să fie ușor de înțeles")

        return assumptions

    def generate_logical_conclusions(self, logical_steps: List[Dict[str, Any]],
                                   assumptions: List[str]) -> List[Dict[str, Any]]:
        """Generează concluzii logice"""
        conclusions = []

        if logical_steps:
            conclusions.append({
                "conclusion": f"Problema poate fi rezolvată în {len(logical_steps)} pași principali",
                "confidence": 0.8,
                "type": "structural"
            })

        if assumptions:
            conclusions.append({
                "conclusion": f"Soluția depinde de {len(assumptions)} asumpții cheie",
                "confidence": 0.7,
                "type": "conditional"
            })

        return conclusions

    def calculate_reasoning_confidence(self, logical_steps: List[Dict[str, Any]],
                                     assumptions: List[str],
                                     conclusions: List[Dict[str, Any]]) -> float:
        """Calculează încrederea în raționament"""
        base_confidence = 0.5

        # Ajustează bazat pe numărul de pași
        if logical_steps:
            base_confidence += min(len(logical_steps) * 0.1, 0.3)

        # Scade încrederea pentru multe asumpții
        if assumptions:
            base_confidence -= min(len(assumptions) * 0.05, 0.2)

        # Ajustează bazat pe concluzii
        if conclusions:
            avg_conclusion_confidence = sum(c.get("confidence", 0) for c in conclusions) / len(conclusions)
            base_confidence = (base_confidence + avg_conclusion_confidence) / 2

        return min(max(base_confidence, 0.0), 1.0)

    def generate_creative_ideas(self, request: str) -> List[Dict[str, Any]]:
        """Generează idei creative"""
        ideas = []

        # Extrage cuvinte cheie pentru inspirație
        keywords = request.lower().split()

        # Generează idei bazate pe combinații
        creative_approaches = [
            "Abordare inovatoare prin combinarea tehnologiilor",
            "Soluție minimalistă cu impact maxim",
            "Implementare modulară și extensibilă",
            "Integrare cu tehnologii emergente",
            "Optimizare pentru experiența utilizatorului"
        ]

        for i, approach in enumerate(creative_approaches):
            ideas.append({
                "id": i + 1,
                "idea": approach,
                "novelty_score": 0.6 + (i * 0.1),
                "feasibility": 0.8 - (i * 0.05),
                "impact": 0.7 + (i * 0.05)
            })

        return ideas

    def identify_inspiration_sources(self, request: str) -> List[str]:
        """Identifică surse de inspirație"""
        sources = []

        if "web" in request.lower():
            sources.extend(["React", "Vue.js", "Angular", "Modern web frameworks"])

        if "ai" in request.lower() or "inteligent" in request.lower():
            sources.extend(["Machine Learning", "Neural Networks", "AI Research"])

        if "mobil" in request.lower():
            sources.extend(["Flutter", "React Native", "Mobile-first design"])

        return sources[:5]  # Limitează la 5 surse

    def calculate_novelty_score(self, creative_ideas: List[Dict[str, Any]]) -> float:
        """Calculează scorul de noutate pentru idei"""
        if not creative_ideas:
            return 0.0

        total_novelty = sum(idea.get("novelty_score", 0) for idea in creative_ideas)
        return total_novelty / len(creative_ideas)

    def analyze_idea_feasibility(self, creative_ideas: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analizează fezabilitatea ideilor"""
        if not creative_ideas:
            return {"feasibility": "unknown", "analysis": "No ideas to analyze"}

        avg_feasibility = sum(idea.get("feasibility", 0) for idea in creative_ideas) / len(creative_ideas)

        analysis = {
            "average_feasibility": avg_feasibility,
            "most_feasible": max(creative_ideas, key=lambda x: x.get("feasibility", 0)),
            "least_feasible": min(creative_ideas, key=lambda x: x.get("feasibility", 0)),
            "recommendation": "high" if avg_feasibility > 0.7 else "medium" if avg_feasibility > 0.4 else "low"
        }

        return analysis
