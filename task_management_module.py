"""
Modul de gestionare task-uri pentru AI Assistant Local
Simulează capacitățile de planificare și organizare task-uri
"""

import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from enum import Enum

class TaskState(Enum):
    NOT_STARTED = "NOT_STARTED"
    IN_PROGRESS = "IN_PROGRESS" 
    COMPLETE = "COMPLETE"
    CANCELLED = "CANCELLED"

class TaskPriority(Enum):
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    URGENT = "URGENT"

class Task:
    def __init__(self, name: str, description: str, priority: TaskPriority = TaskPriority.MEDIUM):
        self.id = str(uuid.uuid4())
        self.name = name
        self.description = description
        self.state = TaskState.NOT_STARTED
        self.priority = priority
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
        self.parent_id = None
        self.subtasks = []
        self.estimated_duration = None
        self.actual_duration = None
        self.tags = []
        self.dependencies = []
        
    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "state": self.state.value,
            "priority": self.priority.value,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "parent_id": self.parent_id,
            "subtasks": [subtask.to_dict() for subtask in self.subtasks],
            "estimated_duration": self.estimated_duration,
            "actual_duration": self.actual_duration,
            "tags": self.tags,
            "dependencies": self.dependencies
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Task':
        task = cls(data["name"], data["description"])
        task.id = data["id"]
        task.state = TaskState(data["state"])
        task.priority = TaskPriority(data["priority"])
        task.created_at = datetime.fromisoformat(data["created_at"])
        task.updated_at = datetime.fromisoformat(data["updated_at"])
        task.parent_id = data.get("parent_id")
        task.estimated_duration = data.get("estimated_duration")
        task.actual_duration = data.get("actual_duration")
        task.tags = data.get("tags", [])
        task.dependencies = data.get("dependencies", [])
        
        # Recreează subtask-urile
        for subtask_data in data.get("subtasks", []):
            subtask = Task.from_dict(subtask_data)
            task.subtasks.append(subtask)
        
        return task

class TaskManager:
    def __init__(self, storage_file: str = "tasks.json"):
        self.storage_file = storage_file
        self.tasks = {}
        self.load_tasks()
    
    def create_task(self, name: str, description: str, priority: TaskPriority = TaskPriority.MEDIUM, 
                   parent_id: Optional[str] = None, tags: List[str] = None) -> Task:
        """Creează un task nou"""
        task = Task(name, description, priority)
        
        if tags:
            task.tags = tags
            
        if parent_id and parent_id in self.tasks:
            task.parent_id = parent_id
            self.tasks[parent_id].subtasks.append(task)
        
        self.tasks[task.id] = task
        self.save_tasks()
        
        print(f"✅ Task creat: {name} (ID: {task.id[:8]})")
        return task
    
    def update_task(self, task_id: str, **kwargs) -> bool:
        """Actualizează un task existent"""
        if task_id not in self.tasks:
            print(f"❌ Task cu ID {task_id} nu există")
            return False
        
        task = self.tasks[task_id]
        
        if 'name' in kwargs:
            task.name = kwargs['name']
        if 'description' in kwargs:
            task.description = kwargs['description']
        if 'state' in kwargs:
            if isinstance(kwargs['state'], str):
                task.state = TaskState(kwargs['state'])
            else:
                task.state = kwargs['state']
        if 'priority' in kwargs:
            if isinstance(kwargs['priority'], str):
                task.priority = TaskPriority(kwargs['priority'])
            else:
                task.priority = kwargs['priority']
        if 'tags' in kwargs:
            task.tags = kwargs['tags']
        
        task.updated_at = datetime.now()
        self.save_tasks()
        
        print(f"🔄 Task actualizat: {task.name}")
        return True
    
    def delete_task(self, task_id: str) -> bool:
        """Șterge un task"""
        if task_id not in self.tasks:
            return False
        
        task = self.tasks[task_id]
        
        # Șterge și subtask-urile
        for subtask in task.subtasks:
            if subtask.id in self.tasks:
                del self.tasks[subtask.id]
        
        # Elimină din parent dacă există
        if task.parent_id and task.parent_id in self.tasks:
            parent = self.tasks[task.parent_id]
            parent.subtasks = [st for st in parent.subtasks if st.id != task_id]
        
        del self.tasks[task_id]
        self.save_tasks()
        
        print(f"🗑️ Task șters: {task.name}")
        return True
    
    def get_task(self, task_id: str) -> Optional[Task]:
        """Obține un task după ID"""
        return self.tasks.get(task_id)
    
    def list_tasks(self, filter_state: Optional[TaskState] = None, 
                  filter_priority: Optional[TaskPriority] = None) -> List[Task]:
        """Listează task-urile cu filtrare opțională"""
        tasks = list(self.tasks.values())
        
        if filter_state:
            tasks = [t for t in tasks if t.state == filter_state]
        
        if filter_priority:
            tasks = [t for t in tasks if t.priority == filter_priority]
        
        # Sortează după prioritate și dată
        priority_order = {TaskPriority.URGENT: 0, TaskPriority.HIGH: 1, 
                         TaskPriority.MEDIUM: 2, TaskPriority.LOW: 3}
        
        tasks.sort(key=lambda t: (priority_order[t.priority], t.created_at))
        return tasks
    
    def get_task_tree(self) -> List[Task]:
        """Obține task-urile organizate ca arbore (doar root tasks)"""
        return [task for task in self.tasks.values() if task.parent_id is None]
    
    def generate_project_plan(self, project_name: str, requirements: List[str]) -> List[Task]:
        """Generează un plan de proiect bazat pe cerințe"""
        print(f"📋 Generez plan pentru proiectul: {project_name}")
        
        # Creează task-ul principal
        main_task = self.create_task(
            name=f"Proiect: {project_name}",
            description=f"Task principal pentru proiectul {project_name}",
            priority=TaskPriority.HIGH,
            tags=["project", "main"]
        )
        
        # Generează subtask-uri bazate pe cerințe
        subtasks = []
        for i, requirement in enumerate(requirements, 1):
            subtask = self.create_task(
                name=f"Cerința {i}: {requirement[:50]}...",
                description=requirement,
                priority=TaskPriority.MEDIUM,
                parent_id=main_task.id,
                tags=["requirement", "subtask"]
            )
            subtasks.append(subtask)
        
        return [main_task] + subtasks
    
    def get_progress_report(self) -> Dict[str, Any]:
        """Generează un raport de progres"""
        all_tasks = list(self.tasks.values())
        
        if not all_tasks:
            return {"message": "Nu există task-uri"}
        
        total = len(all_tasks)
        completed = len([t for t in all_tasks if t.state == TaskState.COMPLETE])
        in_progress = len([t for t in all_tasks if t.state == TaskState.IN_PROGRESS])
        not_started = len([t for t in all_tasks if t.state == TaskState.NOT_STARTED])
        cancelled = len([t for t in all_tasks if t.state == TaskState.CANCELLED])
        
        progress_percentage = (completed / total) * 100 if total > 0 else 0
        
        return {
            "total_tasks": total,
            "completed": completed,
            "in_progress": in_progress,
            "not_started": not_started,
            "cancelled": cancelled,
            "progress_percentage": round(progress_percentage, 2),
            "completion_rate": f"{completed}/{total}"
        }
    
    def save_tasks(self):
        """Salvează task-urile în fișier"""
        data = {
            "tasks": {task_id: task.to_dict() for task_id, task in self.tasks.items()},
            "last_updated": datetime.now().isoformat()
        }
        
        with open(self.storage_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
    
    def load_tasks(self):
        """Încarcă task-urile din fișier"""
        try:
            with open(self.storage_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.tasks = {}
            for task_id, task_data in data.get("tasks", {}).items():
                self.tasks[task_id] = Task.from_dict(task_data)
            
            print(f"📂 Încărcate {len(self.tasks)} task-uri din {self.storage_file}")
            
        except FileNotFoundError:
            print(f"📝 Fișier {self.storage_file} nu există, se va crea la prima salvare")
            self.tasks = {}
        except Exception as e:
            print(f"❌ Eroare la încărcarea task-urilor: {e}")
            self.tasks = {}

# Exemplu de utilizare
if __name__ == "__main__":
    tm = TaskManager()
    
    # Creează niște task-uri de test
    task1 = tm.create_task("Învață Python", "Studiază bazele Python", TaskPriority.HIGH)
    task2 = tm.create_task("Creează aplicație web", "Dezvoltă o aplicație Flask", TaskPriority.MEDIUM)
    
    # Creează subtask-uri
    subtask1 = tm.create_task("Setup environment", "Instalează Python și pip", 
                             TaskPriority.HIGH, parent_id=task1.id)
    
    # Actualizează starea
    tm.update_task(subtask1.id, state=TaskState.COMPLETE)
    
    # Generează raport
    report = tm.get_progress_report()
    print(f"📊 Progres: {report['completion_rate']} ({report['progress_percentage']}%)")
