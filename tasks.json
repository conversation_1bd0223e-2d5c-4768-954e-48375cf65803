{"tasks": {"e407a0c7-6fc9-41a0-bf82-b7cb31d208fc": {"id": "e407a0c7-6fc9-41a0-bf82-b7cb31d208fc", "name": "Învață Python Basics", "description": "Studiază sintaxa de bază Python", "state": "COMPLETE", "priority": "HIGH", "created_at": "2025-07-20T13:20:16.481633", "updated_at": "2025-07-20T13:20:16.482034", "parent_id": null, "subtasks": [], "estimated_duration": null, "actual_duration": null, "tags": [], "dependencies": []}, "2b85cdd3-0cd6-4ca5-955a-17b7c3f26195": {"id": "2b85cdd3-0cd6-4ca5-955a-17b7c3f26195", "name": "Creează aplicație Flask", "description": "Dezvoltă o aplicație web simplă", "state": "IN_PROGRESS", "priority": "MEDIUM", "created_at": "2025-07-20T13:20:16.481766", "updated_at": "2025-07-20T13:20:16.482234", "parent_id": null, "subtasks": [], "estimated_duration": null, "actual_duration": null, "tags": [], "dependencies": []}, "19298a34-fcce-4a66-904d-9f641df2802c": {"id": "19298a34-fcce-4a66-904d-9f641df2802c", "name": "Testează aplicația", "description": "Scrie și rulează teste", "state": "NOT_STARTED", "priority": "MEDIUM", "created_at": "2025-07-20T13:20:16.481894", "updated_at": "2025-07-20T13:20:16.481895", "parent_id": null, "subtasks": [], "estimated_duration": null, "actual_duration": null, "tags": [], "dependencies": []}, "1e62e17d-da18-4454-a203-6abaf86cd6b8": {"id": "1e62e17d-da18-4454-a203-6abaf86cd6b8", "name": "Proiect: my_web_app", "description": "Task principal pentru proiectul my_web_app", "state": "NOT_STARTED", "priority": "HIGH", "created_at": "2025-07-20T13:20:16.483868", "updated_at": "2025-07-20T13:20:16.483869", "parent_id": null, "subtasks": [{"id": "e39f674f-9afd-4113-9402-cd353ad4924a", "name": "Cerința 1: Creează interfață utilizator cu HTML/CSS...", "description": "Creează interfață utilizator cu HTML/CSS", "state": "NOT_STARTED", "priority": "MEDIUM", "created_at": "2025-07-20T13:20:16.484053", "updated_at": "2025-07-20T13:20:16.484054", "parent_id": "1e62e17d-da18-4454-a203-6abaf86cd6b8", "subtasks": [], "estimated_duration": null, "actual_duration": null, "tags": ["requirement", "subtask"], "dependencies": []}, {"id": "13172212-efaf-480d-b6a9-c349fa67fe3a", "name": "Cerința 2: Implementează logica backend în Python...", "description": "Implementează logica backend în Python", "state": "NOT_STARTED", "priority": "MEDIUM", "created_at": "2025-07-20T13:20:16.484211", "updated_at": "2025-07-20T13:20:16.484212", "parent_id": "1e62e17d-da18-4454-a203-6abaf86cd6b8", "subtasks": [], "estimated_duration": null, "actual_duration": null, "tags": ["requirement", "subtask"], "dependencies": []}, {"id": "1d09b283-1407-4d0b-9253-8186b6c224c5", "name": "Cerința 3: Adaugă bază de date SQLite...", "description": "Adaugă bază de date SQLite", "state": "NOT_STARTED", "priority": "MEDIUM", "created_at": "2025-07-20T13:20:16.484390", "updated_at": "2025-07-20T13:20:16.484391", "parent_id": "1e62e17d-da18-4454-a203-6abaf86cd6b8", "subtasks": [], "estimated_duration": null, "actual_duration": null, "tags": ["requirement", "subtask"], "dependencies": []}, {"id": "2f12e921-d66e-4d96-bafb-37709617afbd", "name": "Cerința 4: <PERSON><PERSON> teste unitare...", "description": "Scrie teste unitare", "state": "NOT_STARTED", "priority": "MEDIUM", "created_at": "2025-07-20T13:20:16.484612", "updated_at": "2025-07-20T13:20:16.484614", "parent_id": "1e62e17d-da18-4454-a203-6abaf86cd6b8", "subtasks": [], "estimated_duration": null, "actual_duration": null, "tags": ["requirement", "subtask"], "dependencies": []}, {"id": "ddc0e25b-cef2-4183-9031-267fcf361f0f", "name": "Cerința 5: Creează documentație...", "description": "Creează documentație", "state": "NOT_STARTED", "priority": "MEDIUM", "created_at": "2025-07-20T13:20:16.484843", "updated_at": "2025-07-20T13:20:16.484843", "parent_id": "1e62e17d-da18-4454-a203-6abaf86cd6b8", "subtasks": [], "estimated_duration": null, "actual_duration": null, "tags": ["requirement", "subtask"], "dependencies": []}], "estimated_duration": null, "actual_duration": null, "tags": ["project", "main"], "dependencies": []}, "e39f674f-9afd-4113-9402-cd353ad4924a": {"id": "e39f674f-9afd-4113-9402-cd353ad4924a", "name": "Cerința 1: Creează interfață utilizator cu HTML/CSS...", "description": "Creează interfață utilizator cu HTML/CSS", "state": "NOT_STARTED", "priority": "MEDIUM", "created_at": "2025-07-20T13:20:16.484053", "updated_at": "2025-07-20T13:20:16.484054", "parent_id": "1e62e17d-da18-4454-a203-6abaf86cd6b8", "subtasks": [], "estimated_duration": null, "actual_duration": null, "tags": ["requirement", "subtask"], "dependencies": []}, "13172212-efaf-480d-b6a9-c349fa67fe3a": {"id": "13172212-efaf-480d-b6a9-c349fa67fe3a", "name": "Cerința 2: Implementează logica backend în Python...", "description": "Implementează logica backend în Python", "state": "NOT_STARTED", "priority": "MEDIUM", "created_at": "2025-07-20T13:20:16.484211", "updated_at": "2025-07-20T13:20:16.484212", "parent_id": "1e62e17d-da18-4454-a203-6abaf86cd6b8", "subtasks": [], "estimated_duration": null, "actual_duration": null, "tags": ["requirement", "subtask"], "dependencies": []}, "1d09b283-1407-4d0b-9253-8186b6c224c5": {"id": "1d09b283-1407-4d0b-9253-8186b6c224c5", "name": "Cerința 3: Adaugă bază de date SQLite...", "description": "Adaugă bază de date SQLite", "state": "NOT_STARTED", "priority": "MEDIUM", "created_at": "2025-07-20T13:20:16.484390", "updated_at": "2025-07-20T13:20:16.484391", "parent_id": "1e62e17d-da18-4454-a203-6abaf86cd6b8", "subtasks": [], "estimated_duration": null, "actual_duration": null, "tags": ["requirement", "subtask"], "dependencies": []}, "2f12e921-d66e-4d96-bafb-37709617afbd": {"id": "2f12e921-d66e-4d96-bafb-37709617afbd", "name": "Cerința 4: <PERSON><PERSON> teste unitare...", "description": "Scrie teste unitare", "state": "NOT_STARTED", "priority": "MEDIUM", "created_at": "2025-07-20T13:20:16.484612", "updated_at": "2025-07-20T13:20:16.484614", "parent_id": "1e62e17d-da18-4454-a203-6abaf86cd6b8", "subtasks": [], "estimated_duration": null, "actual_duration": null, "tags": ["requirement", "subtask"], "dependencies": []}, "ddc0e25b-cef2-4183-9031-267fcf361f0f": {"id": "ddc0e25b-cef2-4183-9031-267fcf361f0f", "name": "Cerința 5: Creează documentație...", "description": "Creează documentație", "state": "NOT_STARTED", "priority": "MEDIUM", "created_at": "2025-07-20T13:20:16.484843", "updated_at": "2025-07-20T13:20:16.484843", "parent_id": "1e62e17d-da18-4454-a203-6abaf86cd6b8", "subtasks": [], "estimated_duration": null, "actual_duration": null, "tags": ["requirement", "subtask"], "dependencies": []}}, "last_updated": "2025-07-20T13:20:16.484874"}