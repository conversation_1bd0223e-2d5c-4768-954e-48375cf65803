#!/usr/bin/env python3
"""
INTERFAȚĂ WEB REALĂ pentru LFM2-1.2B
Interfață web pentru sistemul AI real cu modelul LFM2-1.2B
"""

from flask import Flask, render_template, request, jsonify
from flask_socketio import Socket<PERSON>, emit
import time
import threading
from datetime import datetime
from real_lfm2_system import RealLFM2AISystem

app = Flask(__name__)
app.config['SECRET_KEY'] = 'real_lfm2_secret_key'
socketio = SocketIO(app, cors_allowed_origins="*")

# Instanța globală a sistemului AI real
ai_system = None
status_thread = None

def initialize_real_ai():
    """Inițializează sistemul AI real"""
    global ai_system
    if ai_system is None:
        print("🚀 Inițializare sistem AI real cu LFM2-1.2B...")
        ai_system = RealLFM2AISystem()
    return ai_system

def broadcast_status():
    """Transmite statusul în timp real"""
    while True:
        try:
            if ai_system:
                status = ai_system.get_system_status()
                socketio.emit('status_update', status)
            time.sleep(3)  # Update la fiecare 3 secunde
        except Exception as e:
            print(f"Eroare broadcast status: {e}")
            time.sleep(10)

@app.route('/')
def index():
    """Pagina principală"""
    return render_template('real_lfm2_chat.html')

@app.route('/api/chat', methods=['POST'])
def chat():
    """Endpoint pentru chat cu modelul REAL"""
    try:
        data = request.get_json()
        message = data.get('message', '').strip()
        
        if not message:
            return jsonify({'error': 'Mesajul nu poate fi gol'}), 400
        
        # Inițializează sistemul dacă nu e inițializat
        ai = initialize_real_ai()
        
        # Procesează cu modelul REAL LFM2-1.2B
        result = ai.process_conversation(message)
        
        # Transmite mesajul în timp real
        socketio.emit('new_message', {
            'user_message': message,
            'ai_response': result['response'],
            'timestamp': datetime.now().isoformat(),
            'quality_score': result.get('quality_score', 0),
            'learning_value': result.get('learning_value', 0),
            'processing_time': result.get('processing_time', 0),
            'model_status': result.get('model_status', 'unknown')
        })
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/status')
def status():
    """Status sistem real"""
    try:
        ai = initialize_real_ai()
        return jsonify(ai.get_system_status())
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/model/info')
def model_info():
    """Informații despre modelul real"""
    try:
        ai = initialize_real_ai()
        
        return jsonify({
            'model_name': 'LiquidAI/LFM2-1.2B',
            'model_type': 'Hybrid Liquid Model',
            'parameters': '1.17B',
            'context_length': '32,768 tokens',
            'architecture': 'Multiplicative gates + short convolutions',
            'device': ai.model_manager.device or 'Loading...',
            'is_loaded': ai.model_manager.is_loaded,
            'loading_progress': ai.model_manager.loading_progress,
            'supported_languages': ['English', 'Arabic', 'Chinese', 'French', 'German', 'Japanese', 'Korean', 'Spanish']
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/conversations')
def conversations():
    """Istoricul conversațiilor reale"""
    try:
        ai = initialize_real_ai()
        
        # Ultimele 30 de conversații
        recent_conversations = ai.conversation_history[-30:]
        
        conversations_data = []
        for i, conv in enumerate(recent_conversations):
            conversations_data.append({
                'id': i,
                'timestamp': conv.timestamp,
                'user_input': conv.user_input,
                'ai_response': conv.ai_response,
                'quality_score': conv.quality_score,
                'learning_value': conv.learning_value,
                'processing_time': conv.processing_time
            })
        
        return jsonify({
            'conversations': conversations_data,
            'total_count': len(ai.conversation_history)
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/learning/stats')
def learning_stats():
    """Statistici învățare continuă"""
    try:
        ai = initialize_real_ai()
        
        if not ai.learning_engine:
            return jsonify({
                'learning_active': False,
                'training_sessions': 0,
                'high_quality_conversations': 0,
                'learning_data_size': 0
            })
        
        # Calculează conversațiile de calitate înaltă
        high_quality_convs = [
            conv for conv in ai.conversation_history 
            if conv.quality_score > 0.7
        ]
        
        return jsonify({
            'learning_active': True,
            'training_sessions': ai.learning_engine.training_sessions,
            'high_quality_conversations': len(high_quality_convs),
            'learning_data_size': len(ai.learning_engine.learning_data),
            'conversation_buffer_size': len(ai.learning_engine.conversation_buffer),
            'is_training': ai.learning_engine.is_training,
            'average_quality': sum(conv.quality_score for conv in ai.conversation_history) / max(len(ai.conversation_history), 1),
            'average_learning_value': sum(conv.learning_value for conv in ai.conversation_history) / max(len(ai.conversation_history), 1)
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@socketio.on('connect')
def handle_connect():
    """Client conectat"""
    print(f"Client conectat: {request.sid}")
    
    # Trimite statusul inițial
    try:
        ai = initialize_real_ai()
        status = ai.get_system_status()
        emit('status_update', status)
    except Exception as e:
        print(f"Eroare trimitere status inițial: {e}")

@socketio.on('disconnect')
def handle_disconnect():
    """Client deconectat"""
    print(f"Client deconectat: {request.sid}")

@socketio.on('request_model_info')
def handle_model_info_request():
    """Cerere informații model"""
    try:
        ai = initialize_real_ai()
        model_info = {
            'name': 'LFM2-1.2B',
            'status': 'loaded' if ai.model_manager.is_loaded else 'loading',
            'progress': ai.model_manager.loading_progress,
            'device': ai.model_manager.device or 'detecting...'
        }
        emit('model_info_update', model_info)
    except Exception as e:
        emit('error', {'message': str(e)})

def start_background_services():
    """Pornește serviciile de background"""
    global status_thread
    
    if status_thread is None:
        status_thread = threading.Thread(target=broadcast_status, daemon=True)
        status_thread.start()

if __name__ == '__main__':
    print("🌐 INTERFAȚĂ WEB REALĂ pentru LFM2-1.2B")
    print("=" * 60)
    print("🔗 Accesează: http://localhost:5004")
    print("🧠 Model real: LiquidAI/LFM2-1.2B")
    print("🎓 Învățare continuă 24/7 activă")
    print("📊 Monitorizare în timp real")
    print("⚡ Arhitectură de înaltă performanță")
    print("=" * 60)
    print()
    
    # Inițializează sistemul AI real
    initialize_real_ai()
    
    # Pornește serviciile de background
    start_background_services()
    
    # Pornește serverul
    socketio.run(app, host='0.0.0.0', port=5004, debug=False)
