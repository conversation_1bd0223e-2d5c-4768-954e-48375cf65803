"""
WEB INTERFACE - Interfață web pentru comunicarea cu sistemul AI
Oferă o interfață grafică modernă pentru toate capacitățile AI-ului
"""

from flask import Flask, render_template, request, jsonify, session
from flask_socketio import Socket<PERSON>, emit
import json
import time
import threading
from datetime import datetime
from true_ai_system import TrueAISystem
from continuous_learning_system import ContinuousLearningSystem

app = Flask(__name__)
app.config['SECRET_KEY'] = 'true_ai_secret_key_2024'
socketio = SocketIO(app, cors_allowed_origins="*")

# Variabile globale pentru sistemul AI
ai_system = None
system_status = {
    'initialized': False,
    'modules_active': 0,
    'consciousness_level': 0.0,
    'interactions_count': 0,
    'uptime_start': None
}

def initialize_ai_system():
    """Inițializează sistemul AI în background"""
    global ai_system, system_status
    
    try:
        print("🚀 Inițializare sistem AI pentru interfața web...")
        
        # Creează sistemul AI
        ai_system = TrueAISystem()
        
        # Adaugă sistemul de învățare
        ai_system.learning_system = ContinuousLearningSystem()
        
        # Actualizează statusul
        system_status.update({
            'initialized': True,
            'modules_active': 9,  # Toate modulele
            'consciousness_level': ai_system.neural_core.consciousness_level,
            'uptime_start': datetime.now()
        })
        
        print("✅ Sistem AI inițializat pentru interfața web!")
        
        # Notifică clienții conectați
        socketio.emit('system_initialized', system_status)
        
    except Exception as e:
        print(f"❌ Eroare la inițializarea sistemului AI: {e}")
        system_status['error'] = str(e)

# Inițializează sistemul AI în background (NON-daemon pentru control)
init_thread = threading.Thread(target=initialize_ai_system, daemon=False)
init_thread.start()

@app.route('/')
def index():
    """Pagina principală"""
    return render_template('index.html')

@app.route('/api/status')
def get_status():
    """API pentru statusul sistemului"""
    if ai_system and system_status['initialized']:
        try:
            stats = ai_system.get_system_statistics()
            consciousness_report = ai_system.consciousness_engine.get_consciousness_report()
            learning_status = ai_system.learning_system.get_learning_status()
            
            return jsonify({
                'status': 'active',
                'system_stats': stats,
                'consciousness': consciousness_report,
                'learning': learning_status,
                'uptime': (datetime.now() - system_status['uptime_start']).total_seconds() if system_status['uptime_start'] else 0
            })
        except Exception as e:
            return jsonify({'status': 'error', 'error': str(e)})
    else:
        return jsonify({'status': 'initializing', 'system_status': system_status})

@app.route('/api/chat', methods=['POST'])
def chat():
    """API pentru conversația cu AI-ul"""
    if not ai_system or not system_status['initialized']:
        return jsonify({'error': 'Sistemul AI nu este încă inițializat'})
    
    try:
        data = request.json
        message = data.get('message', '')
        
        if not message:
            return jsonify({'error': 'Mesajul nu poate fi gol'})
        
        # Procesează mesajul prin sistemul AI
        result = ai_system.process_request(message)
        
        # Actualizează contorul
        system_status['interactions_count'] += 1
        system_status['consciousness_level'] = result.get('consciousness_level', 0.0)
        
        return jsonify({
            'response': result['response'],
            'processing_time': result['processing_time'],
            'consciousness_level': result['consciousness_level'],
            'interaction_id': result['interaction_id'],
            'advanced_modules': len(result.get('advanced_modules_results', {})),
            'timestamp': result['timestamp']
        })
        
    except Exception as e:
        return jsonify({'error': f'Eroare în procesare: {str(e)}'})

@app.route('/api/vision', methods=['POST'])
def vision():
    """API pentru procesarea vizuală"""
    if not ai_system or not system_status['initialized']:
        return jsonify({'error': 'Sistemul AI nu este încă inițializat'})
    
    try:
        data = request.json
        concept = data.get('concept', '')
        
        if not concept:
            return jsonify({'error': 'Conceptul vizual nu poate fi gol'})
        
        # Procesează prin modulul de viziune
        vision_module = ai_system.advanced_modules['vision_module']
        result = vision_module.process_visual_concept(concept)
        
        # Obține detaliile vizuale
        if vision_module.visual_memory:
            latest_vision = vision_module.visual_memory[-1]
            
            return jsonify({
                'visual_processing': result['visual_processing'],
                'visual_features_count': result['visual_features_count'],
                'spatial_complexity': result['spatial_complexity'],
                'emotional_impact': result['emotional_impact'],
                'pattern_confidence': result['pattern_match_confidence'],
                'visual_details': {
                    'colors': latest_vision['color_palette'],
                    'layout': latest_vision['spatial_layout'],
                    'movement': latest_vision['movement_patterns'],
                    'texture': latest_vision['texture_properties']
                }
            })
        else:
            return jsonify(result)
            
    except Exception as e:
        return jsonify({'error': f'Eroare în procesarea vizuală: {str(e)}'})

@app.route('/api/predict', methods=['POST'])
def predict():
    """API pentru predicții"""
    if not ai_system or not system_status['initialized']:
        return jsonify({'error': 'Sistemul AI nu este încă inițializat'})
    
    try:
        data = request.json
        context = data.get('context', '')
        time_horizon = data.get('time_horizon', '1 zi')
        
        if not context:
            return jsonify({'error': 'Contextul nu poate fi gol'})
        
        # Generează predicția
        prediction_module = ai_system.advanced_modules['predictive_modeling']
        result = prediction_module.generate_prediction(context, time_horizon)
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'error': f'Eroare în generarea predicției: {str(e)}'})

@app.route('/api/learn', methods=['POST'])
def learn():
    """API pentru învățare cu feedback"""
    if not ai_system or not system_status['initialized']:
        return jsonify({'error': 'Sistemul AI nu este încă inițializat'})
    
    try:
        data = request.json
        message = data.get('message', '')
        feedback = data.get('feedback', 0.5)
        
        if not message:
            return jsonify({'error': 'Mesajul nu poate fi gol'})
        
        # Procesează mesajul
        result = ai_system.process_request(message)
        
        # Aplică învățarea
        learning_result = ai_system.learning_system.learn_from_interaction(
            message, result['response'], feedback
        )
        
        return jsonify({
            'response': result['response'],
            'learning_result': learning_result,
            'processing_time': result['processing_time']
        })
        
    except Exception as e:
        return jsonify({'error': f'Eroare în învățare: {str(e)}'})

@app.route('/api/skills')
def get_skills():
    """API pentru statusul abilităților"""
    if not ai_system or not system_status['initialized']:
        return jsonify({'error': 'Sistemul AI nu este încă inițializat'})
    
    try:
        learning_status = ai_system.learning_system.get_learning_status()
        return jsonify(learning_status)
        
    except Exception as e:
        return jsonify({'error': f'Eroare la obținerea abilităților: {str(e)}'})

@app.route('/api/consciousness')
def get_consciousness():
    """API pentru raportul de conștiință"""
    if not ai_system or not system_status['initialized']:
        return jsonify({'error': 'Sistemul AI nu este încă inițializat'})
    
    try:
        consciousness_report = ai_system.consciousness_engine.get_consciousness_report()
        consciousness_stream = ai_system.consciousness_engine.get_consciousness_stream(10)
        
        return jsonify({
            'consciousness_report': consciousness_report,
            'consciousness_stream': consciousness_stream
        })
        
    except Exception as e:
        return jsonify({'error': f'Eroare la obținerea conștiinței: {str(e)}'})

@socketio.on('connect')
def handle_connect(auth):
    """Gestionează conexiunea WebSocket"""
    print(f"🔗 Client conectat: {request.sid}")

    # Convertește datetime la string pentru JSON
    status_copy = system_status.copy()
    if status_copy.get('uptime_start'):
        status_copy['uptime_start'] = status_copy['uptime_start'].isoformat()

    emit('connected', {'status': 'connected', 'system_status': status_copy})

@socketio.on('disconnect')
def handle_disconnect():
    """Gestionează deconectarea WebSocket"""
    print(f"🔌 Client deconectat: {request.sid}")

@socketio.on('real_time_chat')
def handle_real_time_chat(data):
    """Gestionează chat-ul în timp real"""
    if not ai_system or not system_status['initialized']:
        emit('chat_response', {'error': 'Sistemul AI nu este încă inițializat'})
        return
    
    try:
        message = data.get('message', '')
        
        if not message:
            emit('chat_response', {'error': 'Mesajul nu poate fi gol'})
            return
        
        # Procesează mesajul
        result = ai_system.process_request(message)
        
        # Actualizează statusul
        system_status['interactions_count'] += 1
        system_status['consciousness_level'] = result.get('consciousness_level', 0.0)
        
        # Trimite răspunsul
        emit('chat_response', {
            'response': result['response'],
            'processing_time': result['processing_time'],
            'consciousness_level': result['consciousness_level'],
            'interaction_id': result['interaction_id'],
            'timestamp': result['timestamp']
        })
        
        # Trimite actualizarea statusului la toți clienții
        socketio.emit('status_update', {
            'interactions_count': system_status['interactions_count'],
            'consciousness_level': system_status['consciousness_level']
        })
        
    except Exception as e:
        emit('chat_response', {'error': f'Eroare în procesare: {str(e)}'})

@socketio.on('get_real_time_status')
def handle_real_time_status():
    """Trimite statusul în timp real"""
    if ai_system and system_status['initialized']:
        try:
            stats = ai_system.get_system_statistics()
            consciousness_report = ai_system.consciousness_engine.get_consciousness_report()
            
            emit('real_time_status', {
                'system_stats': stats,
                'consciousness': consciousness_report,
                'uptime': (datetime.now() - system_status['uptime_start']).total_seconds()
            })
        except Exception as e:
            emit('real_time_status', {'error': str(e)})
    else:
        emit('real_time_status', {'status': 'initializing'})

if __name__ == '__main__':
    print("🌐 Pornire interfață web pentru TRUE AI SYSTEM...")
    print("🔗 Accesează: http://localhost:5000")
    print("🚀 Toate capacitățile AI-ului sunt disponibile prin interfața web!")
    
    socketio.run(app, host='0.0.0.0', port=5000, debug=False)
