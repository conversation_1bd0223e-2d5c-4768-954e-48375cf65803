#!/usr/bin/env python3
"""
SETUP ADVANCED AI - Installation and setup script for the sophisticated AI system
Handles model download, dependency installation, and system configuration
"""

import os
import sys
import subprocess
import platform
import json
from pathlib import Path

def print_banner():
    """Print setup banner"""
    print("=" * 80)
    print("🚀 ADVANCED AI SYSTEM SETUP")
    print("=" * 80)
    print("🧠 LFM2-1.2B Language Model Integration")
    print("🎓 Continuous Learning & Training Pipeline")
    print("📊 Real-time Analytics & Monitoring")
    print("🌐 Advanced Web Interface with WebSocket")
    print("=" * 80)
    print()

def check_python_version():
    """Check Python version compatibility"""
    print("🐍 Checking Python version...")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8+ is required")
        print(f"   Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} - Compatible")
    return True

def check_system_requirements():
    """Check system requirements"""
    print("\n💻 Checking system requirements...")
    
    # Check available memory
    try:
        if platform.system() == "Linux":
            with open('/proc/meminfo', 'r') as f:
                meminfo = f.read()
                for line in meminfo.split('\n'):
                    if 'MemTotal:' in line:
                        total_mem = int(line.split()[1]) / 1024 / 1024  # GB
                        break
        else:
            total_mem = 8  # Assume 8GB for other systems
        
        print(f"📊 Available RAM: ~{total_mem:.1f} GB")
        
        if total_mem < 4:
            print("⚠️  Warning: Less than 4GB RAM detected. Model may run slowly.")
        else:
            print("✅ Sufficient RAM for model operation")
            
    except Exception as e:
        print(f"⚠️  Could not determine RAM: {e}")
    
    # Check GPU availability
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            print(f"🎮 GPU Available: {gpu_name} ({gpu_memory:.1f} GB)")
            print("✅ CUDA acceleration available")
        else:
            print("⚠️  No CUDA GPU detected - will use CPU (slower)")
    except ImportError:
        print("⚠️  PyTorch not installed yet - GPU check will be done later")

def install_dependencies():
    """Install required dependencies"""
    print("\n📦 Installing dependencies...")
    
    # Core dependencies
    dependencies = [
        "torch>=2.0.0",
        "transformers>=4.36.0",
        "flask>=2.0.0",
        "flask-socketio>=5.0.0",
        "accelerate>=0.20.0",
        "bitsandbytes>=0.41.0",
        "datasets>=2.14.0",
        "peft>=0.6.0",
        "trl>=0.7.0"
    ]
    
    print("Installing core dependencies...")
    for dep in dependencies:
        print(f"  📥 Installing {dep}...")
        try:
            subprocess.run([
                sys.executable, "-m", "pip", "install", dep, "--upgrade"
            ], check=True, capture_output=True)
            print(f"  ✅ {dep} installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"  ❌ Failed to install {dep}: {e}")
            return False
    
    # Install transformers from main branch for latest features
    print("\n🔄 Installing latest transformers from main branch...")
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", 
            "git+https://github.com/huggingface/transformers.git@main"
        ], check=True, capture_output=True)
        print("✅ Latest transformers installed")
    except subprocess.CalledProcessError as e:
        print(f"⚠️  Could not install latest transformers: {e}")
        print("   Continuing with existing version...")
    
    return True

def download_model():
    """Download and cache the LFM2-1.2B model"""
    print("\n🧠 Downloading LFM2-1.2B model...")
    
    try:
        from transformers import AutoModelForCausalLM, AutoTokenizer
        
        model_name = "LiquidAI/LFM2-1.2B"
        
        print(f"📥 Downloading tokenizer for {model_name}...")
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        print("✅ Tokenizer downloaded and cached")
        
        print(f"📥 Downloading model weights for {model_name}...")
        print("   This may take several minutes depending on your internet connection...")
        
        # Download model without loading to GPU to save memory during setup
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            device_map="cpu",  # Keep on CPU during download
            torch_dtype="auto",
            trust_remote_code=True
        )
        print("✅ Model downloaded and cached successfully")
        
        # Clean up memory
        del model
        del tokenizer
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to download model: {e}")
        print("   You can try downloading manually later")
        return False

def create_config_file():
    """Create configuration file"""
    print("\n⚙️  Creating configuration file...")
    
    config = {
        "model": {
            "name": "LiquidAI/LFM2-1.2B",
            "device": "auto",
            "torch_dtype": "bfloat16",
            "temperature": 0.3,
            "min_p": 0.15,
            "repetition_penalty": 1.05,
            "max_new_tokens": 512,
            "context_length": 32768
        },
        "learning": {
            "enabled": True,
            "batch_size": 10,
            "learning_rate": 1e-5,
            "training_frequency": 50
        },
        "web_interface": {
            "host": "0.0.0.0",
            "port": 5003,
            "debug": False
        },
        "logging": {
            "level": "INFO",
            "file": "advanced_ai.log"
        }
    }
    
    try:
        with open("advanced_ai_config.json", "w") as f:
            json.dump(config, f, indent=2)
        print("✅ Configuration file created: advanced_ai_config.json")
        return True
    except Exception as e:
        print(f"❌ Failed to create config file: {e}")
        return False

def create_launcher_script():
    """Create launcher script"""
    print("\n🚀 Creating launcher script...")
    
    launcher_content = '''#!/usr/bin/env python3
"""
ADVANCED AI LAUNCHER - Start the sophisticated AI system
"""

import os
import sys
import subprocess
import time
import webbrowser
from datetime import datetime

def print_banner():
    print("=" * 80)
    print("🧠 ADVANCED AI SYSTEM - LFM2-1.2B")
    print("=" * 80)
    print("🎓 Continuous Learning & Training")
    print("📊 Real-time Analytics & Monitoring")
    print("🌐 Advanced Web Interface")
    print("📅 Started:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("=" * 80)
    print()

def main():
    print_banner()
    
    print("🎮 Choose launch mode:")
    print("1. 🖥️  Terminal Interface")
    print("2. 🌐 Web Interface (Recommended)")
    print("3. 🚀 Both Interfaces")
    print()
    
    while True:
        try:
            choice = input("Select option (1-3): ").strip()
            
            if choice == '1':
                print("\\n🖥️  Starting terminal interface...")
                subprocess.run([sys.executable, 'advanced_ai_system.py'])
                break
                
            elif choice == '2':
                print("\\n🌐 Starting web interface...")
                print("🔗 Access at: http://localhost:5003")
                
                # Start web interface
                web_process = subprocess.Popen([sys.executable, 'advanced_web_interface.py'])
                
                # Wait and open browser
                time.sleep(5)
                try:
                    webbrowser.open('http://localhost:5003')
                    print("🌐 Browser opened automatically!")
                except:
                    print("🔗 Open manually: http://localhost:5003")
                
                print("\\n💡 Press Ctrl+C to stop the server")
                
                try:
                    web_process.wait()
                except KeyboardInterrupt:
                    print("\\n🛑 Stopping server...")
                    web_process.terminate()
                    web_process.wait()
                    print("✅ Server stopped!")
                
                break
                
            elif choice == '3':
                print("\\n🚀 Starting both interfaces...")
                
                # Start web interface in background
                print("🌐 Starting web interface...")
                web_process = subprocess.Popen([sys.executable, 'advanced_web_interface.py'])
                
                time.sleep(5)
                try:
                    webbrowser.open('http://localhost:5003')
                    print("🌐 Web interface: http://localhost:5003")
                except:
                    print("🔗 Web interface: http://localhost:5003")
                
                print("🖥️  Starting terminal interface...")
                print("💡 Web interface runs in background")
                print()
                
                try:
                    subprocess.run([sys.executable, 'advanced_ai_system.py'])
                except KeyboardInterrupt:
                    print("\\n🛑 Stopping all interfaces...")
                finally:
                    web_process.terminate()
                    web_process.wait()
                    print("✅ All interfaces stopped!")
                
                break
                
            else:
                print("❌ Invalid option! Please choose 1, 2, or 3.")
                
        except KeyboardInterrupt:
            print("\\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print("\\n👋 Advanced AI System shutdown complete!")

if __name__ == "__main__":
    main()
'''
    
    try:
        with open("launch_advanced_ai.py", "w") as f:
            f.write(launcher_content)
        
        # Make executable
        os.chmod("launch_advanced_ai.py", 0o755)
        
        print("✅ Launcher script created: launch_advanced_ai.py")
        return True
    except Exception as e:
        print(f"❌ Failed to create launcher: {e}")
        return False

def run_tests():
    """Run basic system tests"""
    print("\n🧪 Running system tests...")
    
    try:
        # Test imports
        print("  📦 Testing imports...")
        import torch
        import transformers
        from flask import Flask
        from flask_socketio import SocketIO
        print("  ✅ All imports successful")
        
        # Test CUDA
        if torch.cuda.is_available():
            print(f"  🎮 CUDA available: {torch.cuda.device_count()} GPU(s)")
        else:
            print("  ⚠️  CUDA not available - will use CPU")
        
        # Test model loading (just tokenizer to save time)
        print("  🧠 Testing model access...")
        from transformers import AutoTokenizer
        tokenizer = AutoTokenizer.from_pretrained("LiquidAI/LFM2-1.2B")
        print("  ✅ Model access successful")
        
        print("✅ All tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def main():
    """Main setup function"""
    print_banner()
    
    # Check requirements
    if not check_python_version():
        sys.exit(1)
    
    check_system_requirements()
    
    # Install dependencies
    if not install_dependencies():
        print("❌ Dependency installation failed!")
        sys.exit(1)
    
    # Download model
    download_model()  # Continue even if this fails
    
    # Create configuration
    if not create_config_file():
        print("❌ Configuration creation failed!")
        sys.exit(1)
    
    # Create launcher
    if not create_launcher_script():
        print("❌ Launcher creation failed!")
        sys.exit(1)
    
    # Run tests
    if not run_tests():
        print("⚠️  Some tests failed, but setup may still work")
    
    print("\n" + "=" * 80)
    print("🎉 SETUP COMPLETE!")
    print("=" * 80)
    print("🚀 To start the Advanced AI System:")
    print("   python3 launch_advanced_ai.py")
    print()
    print("🌐 Web Interface: http://localhost:5003")
    print("🖥️  Terminal Interface: python3 advanced_ai_system.py")
    print()
    print("📚 Features available:")
    print("   • LFM2-1.2B language model")
    print("   • Continuous learning from conversations")
    print("   • Real-time analytics and monitoring")
    print("   • Advanced web interface with WebSocket")
    print("   • Conversation history and quality tracking")
    print("=" * 80)

if __name__ == "__main__":
    main()
