#!/usr/bin/env python3
"""
INTEGRATED AI SYSTEM - Sistem AI integrat complet cu LFM2-1.2B
Combină toate componentele: model real, antrenament continuu, învățare, interfețe
"""

import os
import sys
import threading
import time
import logging
import json
from datetime import datetime
from typing import Dict, List, Any, Optional

# Import module proprii
from lfm2_gguf_system import LFM2GGUFSystem
from continuous_training_engine import ContinuousTrainingEngine
from continuous_learning_system import ContinuousLearningSystem
from advanced_neural_modules import AdvancedNeuralModules
from consciousness_engine import ConsciousnessEngine
from cognitive_processor import CognitiveProcessor

# Configurare logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('integrated_ai_system.log'),
        logging.StreamHandler()
    ]
)

class IntegratedAISystem:
    """Sistem AI integrat complet cu toate componentele"""
    
    def __init__(self):
        self.system_name = "Advanced LFM2-1.2B Integrated AI"
        self.version = "2.0.0"
        self.start_time = datetime.now()
        
        # Componente principale
        self.lfm2_system = None
        self.training_engine = None
        self.learning_system = None
        self.neural_modules = None
        self.consciousness_engine = None
        self.cognitive_processor = None
        
        # Status sistem
        self.is_initialized = False
        self.is_running = False
        self.components_status = {}
        
        # Configurări
        self.config = {
            "auto_start_training": True,
            "auto_start_learning": True,
            "enable_consciousness": True,
            "enable_cognitive_processing": True,
            "save_interval": 300,  # 5 minute
            "backup_interval": 3600,  # 1 oră
            "max_memory_usage": 8192,  # MB
            "performance_monitoring": True
        }
        
        # Statistici sistem
        self.system_stats = {
            "total_conversations": 0,
            "total_training_exercises": 0,
            "total_learning_sessions": 0,
            "uptime_seconds": 0,
            "average_response_time": 0.0,
            "memory_usage_mb": 0,
            "cpu_usage_percent": 0.0,
            "model_accuracy": 0.0,
            "learning_progress": 0.0
        }
        
        logging.info(f"🚀 {self.system_name} v{self.version} - Inițializare...")
    
    def initialize_system(self) -> bool:
        """Inițializează toate componentele sistemului"""
        try:
            logging.info("🔧 Inițializare componente sistem...")
            
            # 1. Inițializează sistemul LFM2 de bază
            logging.info("📥 Încărcare sistem LFM2-1.2B...")
            self.lfm2_system = LFM2GGUFSystem()
            self.components_status["lfm2_system"] = "initialized"
            
            # 2. Inițializează modulele neurale avansate
            logging.info("🧠 Inițializare module neurale avansate...")
            self.neural_modules = AdvancedNeuralModules()
            self.components_status["neural_modules"] = "initialized"
            
            # 3. Inițializează procesorul cognitiv
            if self.config["enable_cognitive_processing"]:
                logging.info("🤔 Inițializare procesor cognitiv...")
                self.cognitive_processor = CognitiveProcessor()
                self.components_status["cognitive_processor"] = "initialized"
            
            # 4. Inițializează motorul de conștiință
            if self.config["enable_consciousness"]:
                logging.info("✨ Inițializare motor de conștiință...")
                self.consciousness_engine = ConsciousnessEngine()
                self.components_status["consciousness_engine"] = "initialized"
            
            # 5. Inițializează sistemul de învățare continuă
            if self.config["auto_start_learning"]:
                logging.info("📚 Inițializare sistem învățare continuă...")
                self.learning_system = ContinuousLearningSystem()
                self.components_status["learning_system"] = "initialized"
            
            # 6. Inițializează motorul de antrenament continuu
            if self.config["auto_start_training"]:
                logging.info("🎯 Inițializare motor antrenament continuu...")
                self.training_engine = ContinuousTrainingEngine(self)
                self.components_status["training_engine"] = "initialized"
            
            self.is_initialized = True
            logging.info("✅ Toate componentele au fost inițializate cu succes!")
            
            return True
            
        except Exception as e:
            logging.error(f"❌ Eroare inițializare sistem: {e}")
            return False
    
    def start_system(self) -> bool:
        """Pornește toate componentele sistemului"""
        if not self.is_initialized:
            logging.error("❌ Sistemul nu este inițializat!")
            return False
        
        try:
            logging.info("🚀 Pornire sistem integrat...")
            
            # Pornește învățarea continuă
            if self.learning_system and self.config["auto_start_learning"]:
                self.learning_system.start_continuous_learning()
                self.components_status["learning_system"] = "running"
                logging.info("📚 Învățare continuă pornită")
            
            # Pornește antrenamentul continuu
            if self.training_engine and self.config["auto_start_training"]:
                self.training_engine.start_continuous_training()
                self.components_status["training_engine"] = "running"
                logging.info("🎯 Antrenament continuu pornit")
            
            # Pornește monitorizarea performanței
            if self.config["performance_monitoring"]:
                self._start_performance_monitoring()
                logging.info("📊 Monitorizare performanță pornită")
            
            # Pornește salvarea automată
            self._start_auto_save()
            logging.info("💾 Salvare automată pornită")
            
            self.is_running = True
            logging.info("🎉 SISTEM INTEGRAT PORNIT CU SUCCES!")
            logging.info("=" * 60)
            logging.info("🧠 LFM2-1.2B Model: ACTIV")
            logging.info("🎓 Învățare Continuă: ACTIVĂ")
            logging.info("🏋️ Antrenament Non-Stop: ACTIV")
            logging.info("🤖 Procesare Cognitivă: ACTIVĂ")
            logging.info("✨ Motor Conștiință: ACTIV")
            logging.info("=" * 60)
            
            return True
            
        except Exception as e:
            logging.error(f"❌ Eroare pornire sistem: {e}")
            return False
    
    def stop_system(self):
        """Oprește toate componentele sistemului"""
        logging.info("⏹️ Oprire sistem integrat...")
        
        try:
            # Oprește antrenamentul continuu
            if self.training_engine:
                self.training_engine.stop_continuous_training()
                self.components_status["training_engine"] = "stopped"
            
            # Oprește învățarea continuă
            if self.learning_system:
                self.learning_system.stop_continuous_learning()
                self.components_status["learning_system"] = "stopped"
            
            # Salvează toate datele
            self._save_all_data()
            
            self.is_running = False
            logging.info("✅ Sistem oprit cu succes și date salvate")
            
        except Exception as e:
            logging.error(f"❌ Eroare oprire sistem: {e}")
    
    def process_conversation(self, message: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Procesează o conversație prin toate componentele sistemului"""
        if not self.is_running:
            return {"error": "Sistemul nu este pornit"}
        
        start_time = time.time()
        
        try:
            # 1. Procesare cognitivă inițială
            if self.cognitive_processor:
                cognitive_analysis = self.cognitive_processor.process_input(message, context or {})
                enhanced_context = cognitive_analysis.get("enhanced_context", context or {})
            else:
                enhanced_context = context or {}
            
            # 2. Procesare prin motorul de conștiință
            if self.consciousness_engine:
                consciousness_state = self.consciousness_engine.process_awareness(message, enhanced_context)
                enhanced_context.update(consciousness_state)
            
            # 3. Procesare prin sistemul LFM2 principal
            response = self.lfm2_system.process_conversation(message)
            
            if not response or "response" not in response:
                return {"error": "Eroare procesare LFM2"}
            
            # 4. Îmbunătățire răspuns prin module neurale
            if self.neural_modules:
                enhanced_response = self.neural_modules.enhance_response(
                    response["response"], 
                    message, 
                    enhanced_context
                )
                response["response"] = enhanced_response
            
            # 5. Adaugă metadate și statistici
            processing_time = time.time() - start_time
            response.update({
                "processing_time": processing_time,
                "system_version": self.version,
                "components_used": list(self.components_status.keys()),
                "enhanced_context": enhanced_context,
                "timestamp": datetime.now().isoformat()
            })
            
            # 6. Actualizează statistici
            self._update_conversation_stats(processing_time, response)
            
            # 7. Trimite către învățare continuă
            if self.learning_system:
                self.learning_system.add_conversation(message, response["response"], enhanced_context)
            
            return response
            
        except Exception as e:
            logging.error(f"❌ Eroare procesare conversație: {e}")
            return {
                "error": f"Eroare procesare: {str(e)}",
                "processing_time": time.time() - start_time
            }
    
    def _update_conversation_stats(self, processing_time: float, response: Dict[str, Any]):
        """Actualizează statisticile conversațiilor"""
        self.system_stats["total_conversations"] += 1
        
        # Actualizează timpul mediu de răspuns
        total_time = self.system_stats["average_response_time"] * (self.system_stats["total_conversations"] - 1)
        self.system_stats["average_response_time"] = (total_time + processing_time) / self.system_stats["total_conversations"]
        
        # Actualizează acuratețea modelului bazată pe scorul de calitate
        if "quality_score" in response:
            quality = response["quality_score"]
            total_accuracy = self.system_stats["model_accuracy"] * (self.system_stats["total_conversations"] - 1)
            self.system_stats["model_accuracy"] = (total_accuracy + quality) / self.system_stats["total_conversations"]
    
    def _start_performance_monitoring(self):
        """Pornește monitorizarea performanței"""
        def monitor_performance():
            while self.is_running:
                try:
                    import psutil
                    
                    # Monitorizează utilizarea memoriei
                    process = psutil.Process()
                    self.system_stats["memory_usage_mb"] = process.memory_info().rss / 1024 / 1024
                    self.system_stats["cpu_usage_percent"] = process.cpu_percent()
                    
                    # Actualizează uptime
                    self.system_stats["uptime_seconds"] = (datetime.now() - self.start_time).total_seconds()
                    
                    # Actualizează statistici antrenament
                    if self.training_engine:
                        training_status = self.training_engine.get_training_status()
                        self.system_stats["total_training_exercises"] = training_status["total_exercises"]
                    
                    # Actualizează progresul învățării
                    if self.learning_system:
                        learning_stats = self.learning_system.get_learning_stats()
                        self.system_stats["learning_progress"] = learning_stats.get("learning_progress", 0.0)
                    
                    time.sleep(30)  # Monitorizare la 30 secunde
                    
                except Exception as e:
                    logging.error(f"Eroare monitorizare performanță: {e}")
                    time.sleep(60)
        
        monitor_thread = threading.Thread(target=monitor_performance, daemon=True)
        monitor_thread.start()
    
    def _start_auto_save(self):
        """Pornește salvarea automată"""
        def auto_save():
            while self.is_running:
                try:
                    time.sleep(self.config["save_interval"])
                    self._save_all_data()
                    logging.info("💾 Salvare automată completă")
                except Exception as e:
                    logging.error(f"Eroare salvare automată: {e}")
        
        save_thread = threading.Thread(target=auto_save, daemon=True)
        save_thread.start()
    
    def _save_all_data(self):
        """Salvează toate datele sistemului"""
        try:
            # Salvează statistici sistem
            with open("integrated_system_stats.json", "w", encoding="utf-8") as f:
                json.dump(self.system_stats, f, indent=2, ensure_ascii=False)
            
            # Salvează statusul componentelor
            with open("components_status.json", "w", encoding="utf-8") as f:
                json.dump(self.components_status, f, indent=2, ensure_ascii=False)
            
            # Salvează configurația
            with open("system_config.json", "w", encoding="utf-8") as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            
        except Exception as e:
            logging.error(f"Eroare salvare date sistem: {e}")
    
    def get_system_status(self) -> Dict[str, Any]:
        """Returnează statusul complet al sistemului"""
        return {
            "system_info": {
                "name": self.system_name,
                "version": self.version,
                "is_running": self.is_running,
                "uptime": (datetime.now() - self.start_time).total_seconds(),
                "start_time": self.start_time.isoformat()
            },
            "components": self.components_status,
            "statistics": self.system_stats,
            "configuration": self.config,
            "model_status": {
                "model_loaded": self.lfm2_system.model_manager.is_loaded if self.lfm2_system else False,
                "model_path": "LFM2-1.2B-Q4_K_M.gguf",
                "model_size": "731 MB",
                "model_type": "GGUF Q4_K_M"
            }
        }
    
    def execute_command(self, command: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execută comenzi de control sistem"""
        params = params or {}
        
        try:
            if command == "start_training":
                if self.training_engine:
                    self.training_engine.start_continuous_training()
                    return {"success": True, "message": "Antrenament continuu pornit"}
                return {"success": False, "message": "Motor antrenament nu este disponibil"}
            
            elif command == "stop_training":
                if self.training_engine:
                    self.training_engine.stop_continuous_training()
                    return {"success": True, "message": "Antrenament continuu oprit"}
                return {"success": False, "message": "Motor antrenament nu este disponibil"}
            
            elif command == "start_learning":
                if self.learning_system:
                    self.learning_system.start_continuous_learning()
                    return {"success": True, "message": "Învățare continuă pornită"}
                return {"success": False, "message": "Sistem învățare nu este disponibil"}
            
            elif command == "stop_learning":
                if self.learning_system:
                    self.learning_system.stop_continuous_learning()
                    return {"success": True, "message": "Învățare continuă oprită"}
                return {"success": False, "message": "Sistem învățare nu este disponibil"}
            
            elif command == "save_data":
                self._save_all_data()
                return {"success": True, "message": "Date salvate cu succes"}
            
            elif command == "get_stats":
                return {"success": True, "data": self.get_system_status()}
            
            else:
                return {"success": False, "message": f"Comandă necunoscută: {command}"}
                
        except Exception as e:
            return {"success": False, "message": f"Eroare execuție comandă: {str(e)}"}

def main():
    """Funcția principală pentru testare"""
    print("🚀 INTEGRATED AI SYSTEM - Test Mode")
    print("=" * 50)
    
    # Creează și inițializează sistemul
    ai_system = IntegratedAISystem()
    
    if not ai_system.initialize_system():
        print("❌ Eroare inițializare sistem!")
        return
    
    if not ai_system.start_system():
        print("❌ Eroare pornire sistem!")
        return
    
    print("✅ Sistem pornit cu succes!")
    print("Sistemul rulează în background cu antrenament și învățare continuă...")
    
    try:
        # Testează o conversație
        response = ai_system.process_conversation("Salut! Cum funcționezi?")
        print(f"Răspuns test: {response.get('response', 'Eroare')}")
        
        # Afișează statusul
        status = ai_system.get_system_status()
        print(f"Conversații totale: {status['statistics']['total_conversations']}")
        
        # Rulează pentru 60 secunde apoi oprește
        print("Sistemul va rula 60 secunde pentru demonstrație...")
        time.sleep(60)
        
    except KeyboardInterrupt:
        print("\n⏹️ Oprire sistem...")
    finally:
        ai_system.stop_system()
        print("✅ Sistem oprit cu succes!")

if __name__ == "__main__":
    main()
