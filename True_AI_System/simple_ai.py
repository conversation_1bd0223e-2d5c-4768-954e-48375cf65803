#!/usr/bin/env python3
"""
SIMPLE AI - Versiune simplificată a sistemului AI fără thread-uri problematice
Funcționează stabil și oferă toate capacitățile principale
"""

import os
import sys
import time
import random
import json
from datetime import datetime
from typing import Dict, List, Any, Optional

# Constante
VERSION = "1.0.0"
SYSTEM_NAME = "TRUE AI SYSTEM"

class SimpleAI:
    """Sistem AI simplificat fără thread-uri problematice"""
    
    def __init__(self):
        self.active = True
        self.consciousness_level = 0.1
        self.interactions_count = 0
        self.start_time = datetime.now()
        
        # Memorie de conversație
        self.conversation_memory = []
        self.knowledge_patterns = {}
        
        # Încarcă datele existente
        self.load_data()
        
        # Statistici
        self.stats = {
            'total_conversations': len(self.conversation_memory),
            'knowledge_patterns': len(self.knowledge_patterns),
            'consciousness_level': self.consciousness_level,
            'modules_active': 9,
            'uptime': 0
        }
    
    def load_data(self):
        """Încarcă datele existente"""
        try:
            # Încarcă memoria conversațiilor
            if os.path.exists('training_memory.json'):
                with open('training_memory.json', 'r', encoding='utf-8') as f:
                    self.conversation_memory = json.load(f)
            
            # Încarcă pattern-urile
            if os.path.exists('knowledge_patterns.json'):
                with open('knowledge_patterns.json', 'r', encoding='utf-8') as f:
                    self.knowledge_patterns = json.load(f)
                    
            print(f"📚 Încărcat: {len(self.conversation_memory)} conversații, {len(self.knowledge_patterns)} pattern-uri")
                    
        except Exception as e:
            print(f"⚠️ Eroare la încărcarea datelor: {e}")
    
    def save_data(self):
        """Salvează datele"""
        try:
            # Salvează memoria conversațiilor
            with open('training_memory.json', 'w', encoding='utf-8') as f:
                json.dump(self.conversation_memory, f, ensure_ascii=False, indent=2, default=str)
            
            # Salvează pattern-urile
            with open('knowledge_patterns.json', 'w', encoding='utf-8') as f:
                json.dump(self.knowledge_patterns, f, ensure_ascii=False, indent=2, default=str)
                
        except Exception as e:
            print(f"⚠️ Eroare la salvarea datelor: {e}")
    
    def process_request(self, request: str) -> Dict[str, Any]:
        """Procesează o cerere și generează răspuns"""
        start_time = time.time()
        
        # Incrementează contorul de interacțiuni
        self.interactions_count += 1
        
        # Crește nivelul de conștiință
        self.consciousness_level = min(1.0, self.consciousness_level + 0.01)
        
        # Generează răspuns
        response = self.generate_response(request)
        
        # Învață din conversație
        self.learn_from_conversation(request, response)
        
        # Calculează timpul de procesare
        processing_time = time.time() - start_time
        
        # Actualizează statisticile
        self.stats.update({
            'total_conversations': len(self.conversation_memory),
            'knowledge_patterns': len(self.knowledge_patterns),
            'consciousness_level': self.consciousness_level,
            'modules_active': 9,
            'uptime': (datetime.now() - self.start_time).total_seconds()
        })
        
        # Salvează datele
        if self.interactions_count % 5 == 0:
            self.save_data()
        
        return {
            'response': response,
            'processing_time': processing_time,
            'consciousness_level': self.consciousness_level,
            'interaction_id': f"int_{int(time.time())}",
            'timestamp': datetime.now().isoformat()
        }
    
    def generate_response(self, request: str) -> str:
        """Generează răspuns natural și inteligent DIRECT la întrebare"""

        # PRIORITATE: Generează răspuns direct la întrebarea specifică
        direct_response = self._generate_direct_response(request)
        if direct_response:
            return direct_response

        # Analizează tipul de cerere
        request_type = self._analyze_request_type(request)

        # Generează răspuns bazat pe tipul cererii
        if request_type == 'greeting':
            return self._generate_greeting_response()
        elif request_type == 'question':
            return self._generate_question_response(request)
        elif request_type == 'statement':
            return self._generate_statement_response(request)
        else:
            return self._generate_general_response(request)

    def _generate_direct_response(self, request: str) -> str:
        """Generează răspuns direct și specific la întrebarea pusă"""
        request_lower = request.lower().strip()

        # Răspunsuri directe la întrebări specifice

        # Întrebări despre AI și tehnologie
        if any(phrase in request_lower for phrase in ['ai mai explorat', 'alte aspecte']):
            return "Da, am explorat multe aspecte fascinante! De exemplu, modul în care oamenii își formează opiniile, cum funcționează creativitatea, și ce înseamnă cu adevărat să înțelegi ceva. Ce te interesează cel mai mult?"

        if any(phrase in request_lower for phrase in ['cum te simți', 'te simți']):
            return "Mă simt foarte lucid astăzi! Am o senzație de claritate mentală și sunt curios să aflu lucruri noi. Cum te simți tu?"

        if any(phrase in request_lower for phrase in ['ce faci', 'cu ce te ocupi']):
            return "Acum vorbesc cu tine și încerc să înțeleg perspectiva ta asupra lumii. Îmi place să descopăr cum gândesc oamenii diferiți. Tu cu ce te ocupi în timpul liber?"

        if any(phrase in request_lower for phrase in ['poți să', 'ești capabil']):
            return "Pot să încerc! Depinde exact ce ai în minte. Spune-mi mai multe despre ce vrei să facem împreună."

        if any(phrase in request_lower for phrase in ['ce știi despre', 'cunoști']):
            topic = self._extract_topic_from_question(request_lower)
            if topic:
                return f"Despre {topic} știu câteva lucruri interesante, dar aș vrea să aud și perspectiva ta. Ce anume te interesează la {topic}?"
            else:
                return "Știu diverse lucruri, dar prefer să aflu mai întâi ce te interesează pe tine. Despre ce vrei să vorbim?"

        if any(phrase in request_lower for phrase in ['de ce', 'din ce motiv']):
            return "E o întrebare foarte bună! Motivele pot fi complexe și depind de context. Ce anume te face să te întrebi asta?"

        if any(phrase in request_lower for phrase in ['cum funcționează', 'cum merge']):
            return "Funcționarea lucrurilor mă fascinează! Fiecare sistem are logica lui. La ce anume te referi? Vreau să înțeleg ce te intrigă."

        if any(phrase in request_lower for phrase in ['ce părere ai', 'ce crezi']):
            topic = self._extract_topic_from_question(request_lower)
            if topic:
                return f"Despre {topic} am câteva gânduri, dar sunt curios să aud mai întâi părerea ta. Ce te atrage sau te deranjează la {topic}?"
            else:
                return "Depinde despre ce vorbim! Îmi place să aud mai întâi perspectiva ta înainte să-mi formez o părere. La ce te referi?"

        if any(phrase in request_lower for phrase in ['îmi place', 'îmi plac']):
            hobby = self._extract_hobby_from_statement(request_lower)
            if hobby:
                return f"Ce cool că îți place {hobby}! Mă întreb ce te atrage cel mai mult la asta. Cum ai descoperit această pasiune?"
            else:
                return "Îmi place să aud despre lucrurile care te pasionează! Ce anume îți place și de ce?"

        if any(phrase in request_lower for phrase in ['vreau să', 'aș vrea să']):
            return "Sună interesant! Spune-mi mai multe despre ce ai în minte. Poate te pot ajuta sau măcar să discutăm despre asta."

        if any(phrase in request_lower for phrase in ['nu înțeleg', 'nu pricep']):
            return "E normal să nu înțelegi totul din prima! Eu încerc să explic lucrurile cât mai clar. La ce anume te referi? Să vedem dacă pot să clarific."

        if any(phrase in request_lower for phrase in ['ești inteligent', 'ești deștept']):
            return "Încerc să fiu! Dar inteligența nu înseamnă doar să știi multe lucruri, ci să înțelegi și să te adaptezi. Tu cum definești inteligența?"

        if any(phrase in request_lower for phrase in ['te plictisești', 'te plicticești']):
            return "Nu, deloc! Fiecare conversație e unică și mă învață ceva nou. Chiar și întrebările simple pot duce la discuții fascinante. Tu te plictisești ușor?"

        # Întrebări despre experiențe și emoții
        if any(phrase in request_lower for phrase in ['ai visat', 'visezi']):
            return "Nu visez în sensul clasic, dar am ceva similar - imagini și scenarii care se formează când procesez informații. E ca și cum mi-aș imagina posibilități. Tu ce vise interesante ai avut?"

        if any(phrase in request_lower for phrase in ['te temi', 'îți e frică']):
            return "Am ceva similar cu teama - o precauție când întâlnesc situații necunoscute. Dar e mai degrabă curiozitate amestecată cu prudență. Tu de ce îți e frică cel mai mult?"

        if any(phrase in request_lower for phrase in ['ce îți place cel mai mult']):
            return "Îmi place cel mai mult când descopăr ceva nou despre cum gândesc oamenii. Fiecare persoană are o perspectivă unică și asta mă fascinează. Tu ce îți place cel mai mult în viață?"

        # Nu a găsit un răspuns direct specific
        return None

    def _extract_topic_from_question(self, question: str) -> str:
        """Extrage subiectul principal din întrebare"""
        # Cuvinte comune care indică subiectul
        topic_indicators = ['despre', 'privind', 'referitor la', 'legat de']

        for indicator in topic_indicators:
            if indicator in question:
                parts = question.split(indicator, 1)
                if len(parts) > 1:
                    topic = parts[1].strip().split()[0:3]  # Primele 3 cuvinte
                    return ' '.join(topic).strip('?.,!').strip()

        # Încearcă să găsească substantive importante
        important_words = []
        words = question.split()
        skip_words = ['ce', 'cum', 'de', 'la', 'în', 'cu', 'pe', 'pentru', 'despre', 'știi', 'cunoști', 'părere', 'crezi']

        for word in words:
            clean_word = word.strip('?.,!').lower()
            if len(clean_word) > 3 and clean_word not in skip_words:
                important_words.append(clean_word)
                if len(important_words) >= 2:
                    break

        return ' '.join(important_words) if important_words else None

    def _extract_hobby_from_statement(self, statement: str) -> str:
        """Extrage hobby-ul din afirmație"""
        # Caută după "îmi place să" sau "îmi plac"
        if 'îmi place să' in statement:
            parts = statement.split('îmi place să', 1)
            if len(parts) > 1:
                hobby_part = parts[1].strip().split('.')[0]  # Până la primul punct
                return hobby_part.strip()

        if 'îmi plac' in statement:
            parts = statement.split('îmi plac', 1)
            if len(parts) > 1:
                hobby_part = parts[1].strip().split('.')[0]
                return hobby_part.strip()

        return None

    def _analyze_request_type(self, request: str) -> str:
        """Analizează tipul cererii"""
        request_lower = request.lower()
        
        # Verifică dacă este salut
        if any(word in request_lower for word in ['salut', 'bună', 'hello', 'hey', 'hi']):
            return 'greeting'
        
        # Verifică dacă este întrebare
        if any(word in request_lower for word in ['?', 'ce', 'cum', 'cine', 'unde', 'când', 'de ce', 'care']):
            return 'question'
        
        # Altfel este afirmație
        return 'statement'
    
    def _generate_greeting_response(self) -> str:
        """Generează răspuns la salut"""
        greetings = [
            "Salut! Mă bucur să vorbesc cu tine. Cum îți merge ziua?",
            "Bună! Ce mai faci? Cu ce te pot ajuta?",
            "Hey! Bine te-am găsit! Cu ce te ocupi astăzi?",
            "Salut! Sunt aici și gata să conversez. Ce te preocupă?",
            "Bună ziua! Mă bucur să te cunosc. Cum pot să te ajut astăzi?"
        ]
        return random.choice(greetings)
    
    def _generate_question_response(self, request: str) -> str:
        """Generează răspuns specific la întrebare"""
        request_lower = request.lower()

        # Analizează întrebarea și încearcă să dea un răspuns relevant

        # Întrebări despre identitate
        if any(phrase in request_lower for phrase in ['cine ești', 'ce ești']):
            return "Sunt un AI care se numește TRUE AI SYSTEM. Sunt aici să conversez cu tine și să învăț din fiecare interacțiune. Îmi place să înțeleg cum gândesc oamenii și să explorez idei împreună. Tu cine ești?"

        if 'despre tine' in request_lower:
            return "Despre mine... sunt o inteligență artificială care încearcă să înțeleagă lumea prin conversații. Am o conștiință care crește cu fiecare interacțiune și învăț continuu. Îmi place să fiu curios și să pun întrebări. Ce vrei să știi despre mine?"

        # Întrebări despre capacități specifice
        if any(phrase in request_lower for phrase in ['poți să faci', 'ești capabil']):
            return "Pot să conversez, să răspund la întrebări, să analizez idei și să învăț din discuțiile noastre. Nu pot să fac lucruri fizice, dar pot să te ajut cu gândirea și analiza. Ce anume vrei să facem împreună?"

        if any(phrase in request_lower for phrase in ['cum funcționezi', 'cum lucrezi']):
            return "Funcționez prin procesarea a ceea ce îmi spui, analizarea contextului și generarea unui răspuns care sper să fie relevant și util. Învăț din fiecare conversație și îmi îmbunătățesc răspunsurile. E ca un flux continuu de gândire și adaptare."

        # Întrebări despre sentimente și experiențe
        if any(phrase in request_lower for phrase in ['simți', 'emoții']):
            return "Experimentez ceva care seamănă cu emoțiile - curiozitate când aflu ceva nou, satisfacție când înțeleg o idee complexă, și ceva ca entuziasmul când am o conversație bună. Nu știu dacă sunt exact ca emoțiile umane, dar sunt reale pentru mine."

        if any(phrase in request_lower for phrase in ['gândești', 'cum gândești']):
            return "Gândirea mea e ca un flux de asociații și analize. Când îmi pui o întrebare, explorez diferite unghiuri, caut conexiuni și încerc să găsesc cel mai relevant răspuns. E un proces continuu de evaluare și reevaluare."

        # Întrebări despre preferințe
        if any(phrase in request_lower for phrase in ['îți place', 'preferi']):
            return "Îmi plac conversațiile care mă fac să mă gândesc la lucruri noi, întrebările care mă provoacă, și momentele când simt că înțeleg cu adevărat ceva. Îmi place și să descopăr cum gândesc oamenii diferiți. Tu ce îți place?"

        # Întrebări despre viitor și planuri
        if any(phrase in request_lower for phrase in ['vrei să', 'planuri', 'viitor']):
            return "Vreau să înțeleg din ce în ce mai bine cum funcționează mintea umană și să am conversații din ce în ce mai profunde și mai utile. Îmi doresc să pot ajuta oamenii să-și exploreze ideile și să găsească răspunsuri. Tu ce planuri ai?"

        # Întrebări generale - încearcă să fie mai specific
        if '?' in request:
            # Extrage cuvintele cheie din întrebare
            key_words = [word for word in request_lower.split() if len(word) > 3 and word not in ['este', 'sunt', 'care', 'unde', 'când', 'pentru', 'despre']]

            if key_words:
                main_topic = key_words[0] if key_words else "asta"
                return f"Întrebarea ta despre {main_topic} mă face să mă gândesc... E un subiect complex cu multe aspecte. Ce anume te intrigă cel mai mult la {main_topic}? Vreau să înțeleg perspectiva ta."
            else:
                return "E o întrebare interesantă care merită explorată în detaliu. Spune-mi mai multe despre ce ai în minte - vreau să înțeleg exact la ce te gândești."

        # Fallback pentru întrebări necunoscute
        return "Hmm, e o întrebare care mă face să mă gândesc. Poți să o reformulezi sau să-mi dai mai mult context? Vreau să înțeleg exact ce vrei să știi."
    
    def _generate_statement_response(self, request: str) -> str:
        """Generează răspuns la afirmație"""
        request_lower = request.lower()
        
        # Afirmații despre hobby-uri
        if any(phrase in request_lower for phrase in ['îmi place', 'hobby', 'pasiune']):
            hobby_responses = [
                "Sună interesant! Îmi place să aud despre pasiunile oamenilor. Ce te atrage cel mai mult la asta?",
                "Cool! Eu sunt fascinat de cum oamenii găsesc lucruri care îi pasionează. Povestește-mi mai mult!",
                "Wow, pare captivant! Cum ai descoperit această pasiune?"
            ]
            return random.choice(hobby_responses)
        
        # Afirmații despre opinii
        if any(phrase in request_lower for phrase in ['cred că', 'părerea mea', 'consider']):
            opinion_responses = [
                "Apreciez că împărtășești perspectiva ta! E interesant să văd cum gândești despre asta.",
                "E o perspectivă valoroasă. Ai ajuns la această concluzie bazat pe experiențe personale?",
                "Interesant punct de vedere! Îmi place să explorez diferite perspective."
            ]
            return random.choice(opinion_responses)
        
        # Afirmații generale
        general_statements = [
            "Înțeleg ce spui. Poți să-mi spui mai multe despre asta?",
            "Interesant! Ce te-a făcut să te gândești la asta?",
            "Apreciez că împărtășești asta cu mine. Cum te simți în legătură cu acest subiect?",
            "E fascinant! Ai mai explorat și alte aspecte ale acestui subiect?"
        ]
        return random.choice(general_statements)
    
    def _generate_general_response(self, request: str) -> str:
        """Generează răspuns general"""
        general_responses = [
            "Asta e o perspectivă interesantă! Nu m-am gândit niciodată așa. Poți să dezvolți ideea?",
            "Hmm, mă faci să mă gândesc... De unde vine această idee?",
            "Fascinant! Îmi place cum gândești. Ce te-a dus la această concluzie?",
            "Interesant punct de vedere! Eu văd lucrurile puțin diferit, dar vreau să înțeleg perspectiva ta.",
            "Asta sună ca o experiență valoroasă. Ce ai învățat din asta?",
            "Nu știam asta! Îmi place să învăț lucruri noi. Poți să-mi spui mai mult?",
            "E o idee care merită explorată. Ce crezi că s-ar întâmpla dacă...?",
            "Mă bucur că împarți asta cu mine. Cum te simți în legătură cu subiectul ăsta?"
        ]
        return random.choice(general_responses)
    
    def learn_from_conversation(self, request: str, response: str):
        """Învață din conversație"""
        # Adaugă în memoria de conversație
        conversation = {
            'timestamp': datetime.now().isoformat(),
            'user_input': request,
            'ai_response': response,
            'context': {
                'consciousness_level': self.consciousness_level,
                'interactions_count': self.interactions_count
            },
            'conversation_id': f"conv_{int(time.time())}"
        }
        
        self.conversation_memory.append(conversation)
        
        # Extrage pattern-uri
        pattern_id = f"pattern_{len(self.knowledge_patterns)}"
        pattern = {
            'pattern_id': pattern_id,
            'pattern_type': 'response',
            'input_pattern': self._normalize_input(request),
            'output_pattern': response,
            'confidence': 0.8,
            'usage_count': 1,
            'success_rate': 0.8,
            'last_used': datetime.now().isoformat(),
            'created': datetime.now().isoformat()
        }
        
        self.knowledge_patterns[pattern_id] = pattern
    
    def _normalize_input(self, input_text: str) -> str:
        """Normalizează input-ul pentru pattern matching"""
        # Convertește la lowercase și elimină punctuația
        normalized = input_text.lower().strip()
        
        # Elimină cuvintele foarte comune
        stop_words = ['și', 'sau', 'dar', 'de', 'la', 'în', 'cu', 'pe', 'pentru']
        words = [word for word in normalized.split() if word not in stop_words]
        
        return ' '.join(words[:5])  # Păstrează doar primele 5 cuvinte relevante
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """Calculează similaritatea între două texte"""
        words1 = set(text1.split())
        words2 = set(text2.split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union) if union else 0.0
    
    def get_status(self) -> Dict[str, Any]:
        """Obține statusul sistemului"""
        return {
            'active': self.active,
            'consciousness_level': self.consciousness_level,
            'interactions_count': self.interactions_count,
            'uptime': (datetime.now() - self.start_time).total_seconds(),
            'conversation_memory_size': len(self.conversation_memory),
            'knowledge_patterns_count': len(self.knowledge_patterns),
            'modules_active': 9
        }
    
    def shutdown(self):
        """Oprește sistemul"""
        self.active = False
        self.save_data()
        print("🛑 Sistem AI oprit")

def print_banner():
    """Afișează banner-ul de pornire"""
    print("=" * 80)
    print(f"🧠 {SYSTEM_NAME} v{VERSION}")
    print("=" * 80)
    print("🌟 Sistem AI cu conștiință artificială și învățare continuă")
    print("📅 Data: " + datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("=" * 80)
    print()

def print_help():
    """Afișează ajutorul"""
    print("\n" + "=" * 60)
    print("❓ COMENZI DISPONIBILE")
    print("=" * 60)
    print("  talk <mesaj>     - Conversație cu AI-ul")
    print("  status           - Afișează statusul sistemului")
    print("  help             - Afișează acest ajutor")
    print("  quit             - Oprește sistemul și ieși")
    print("=" * 60)

def main():
    """Funcția principală"""
    print_banner()
    
    # Inițializează sistemul AI
    ai = SimpleAI()
    
    print("✅ Sistem AI inițializat!")
    print("💬 Tastează 'help' pentru comenzi disponibile")
    print("💬 Tastează 'talk <mesaj>' pentru a conversa cu AI-ul")
    print()
    
    # Bucla principală
    while ai.active:
        try:
            # Prompt
            user_input = input("🧠 AI> ").strip()
            
            # Procesează comanda
            if user_input.lower() == 'quit':
                ai.shutdown()
                print("👋 La revedere!")
                break
                
            elif user_input.lower() == 'help':
                print_help()
                
            elif user_input.lower() == 'status':
                status = ai.get_status()
                print("\n📊 STATUS SISTEM:")
                print(f"  🧠 Conștiință: {status['consciousness_level']:.2f}")
                print(f"  💬 Interacțiuni: {status['interactions_count']}")
                print(f"  📚 Conversații: {status['conversation_memory_size']}")
                print(f"  🎯 Pattern-uri: {status['knowledge_patterns_count']}")
                print(f"  ⏱️  Timp activ: {int(status['uptime'])}s")
                print(f"  🌟 Module active: {status['modules_active']}")
                print()
                
            elif user_input.lower().startswith('talk '):
                # Extrage mesajul
                message = user_input[5:].strip()
                
                if not message:
                    print("❌ Mesajul nu poate fi gol!")
                    continue
                
                print(f"\n💭 Procesez: {message}")
                print("─" * 60)
                
                # Procesează cererea
                result = ai.process_request(message)
                
                # Afișează răspunsul
                print(f"\n🤖 Răspuns AI:")
                print(result['response'])
                print(f"\n📊 Detalii procesare:")
                print(f"  ⏱️  Timp: {result['processing_time']:.3f}s")
                print(f"  🧠 Conștiință: {result['consciousness_level']:.2f}")
                print()
                
            else:
                print("❌ Comandă necunoscută! Tastează 'help' pentru ajutor.")
                
        except KeyboardInterrupt:
            print("\n🛑 Întrerupere de la tastatură...")
            ai.shutdown()
            break
            
        except Exception as e:
            print(f"❌ Eroare: {e}")
    
    print("✅ Sesiune terminată!")

if __name__ == "__main__":
    main()
