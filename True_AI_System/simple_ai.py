#!/usr/bin/env python3
"""
SIMPLE AI - Versiune simplificată a sistemului AI fără thread-uri problematice
Funcționează stabil și oferă toate capacitățile principale
"""

import os
import sys
import time
import random
import json
from datetime import datetime
from typing import Dict, List, Any, Optional

# Constante
VERSION = "1.0.0"
SYSTEM_NAME = "TRUE AI SYSTEM"

class SimpleAI:
    """Sistem AI simplificat fără thread-uri problematice"""
    
    def __init__(self):
        self.active = True
        self.consciousness_level = 0.1
        self.interactions_count = 0
        self.start_time = datetime.now()
        
        # Memorie de conversație
        self.conversation_memory = []
        self.knowledge_patterns = {}
        
        # Încarcă datele existente
        self.load_data()
        
        # Statistici
        self.stats = {
            'total_conversations': len(self.conversation_memory),
            'knowledge_patterns': len(self.knowledge_patterns),
            'consciousness_level': self.consciousness_level,
            'modules_active': 9,
            'uptime': 0
        }
    
    def load_data(self):
        """Încarcă datele existente"""
        try:
            # Încarcă memoria conversațiilor
            if os.path.exists('training_memory.json'):
                with open('training_memory.json', 'r', encoding='utf-8') as f:
                    self.conversation_memory = json.load(f)
            
            # Încarcă pattern-urile
            if os.path.exists('knowledge_patterns.json'):
                with open('knowledge_patterns.json', 'r', encoding='utf-8') as f:
                    self.knowledge_patterns = json.load(f)
                    
            print(f"📚 Încărcat: {len(self.conversation_memory)} conversații, {len(self.knowledge_patterns)} pattern-uri")
                    
        except Exception as e:
            print(f"⚠️ Eroare la încărcarea datelor: {e}")
    
    def save_data(self):
        """Salvează datele"""
        try:
            # Salvează memoria conversațiilor
            with open('training_memory.json', 'w', encoding='utf-8') as f:
                json.dump(self.conversation_memory, f, ensure_ascii=False, indent=2, default=str)
            
            # Salvează pattern-urile
            with open('knowledge_patterns.json', 'w', encoding='utf-8') as f:
                json.dump(self.knowledge_patterns, f, ensure_ascii=False, indent=2, default=str)
                
        except Exception as e:
            print(f"⚠️ Eroare la salvarea datelor: {e}")
    
    def process_request(self, request: str) -> Dict[str, Any]:
        """Procesează o cerere și generează răspuns"""
        start_time = time.time()
        
        # Incrementează contorul de interacțiuni
        self.interactions_count += 1
        
        # Crește nivelul de conștiință
        self.consciousness_level = min(1.0, self.consciousness_level + 0.01)
        
        # Generează răspuns
        response = self.generate_response(request)
        
        # Învață din conversație
        self.learn_from_conversation(request, response)
        
        # Calculează timpul de procesare
        processing_time = time.time() - start_time
        
        # Actualizează statisticile
        self.stats.update({
            'total_conversations': len(self.conversation_memory),
            'knowledge_patterns': len(self.knowledge_patterns),
            'consciousness_level': self.consciousness_level,
            'modules_active': 9,
            'uptime': (datetime.now() - self.start_time).total_seconds()
        })
        
        # Salvează datele
        if self.interactions_count % 5 == 0:
            self.save_data()
        
        return {
            'response': response,
            'processing_time': processing_time,
            'consciousness_level': self.consciousness_level,
            'interaction_id': f"int_{int(time.time())}",
            'timestamp': datetime.now().isoformat()
        }
    
    def generate_response(self, request: str) -> str:
        """Generează răspuns natural și inteligent"""
        
        # Analizează tipul de cerere
        request_type = self._analyze_request_type(request)
        
        # Caută pattern-uri exacte
        for pattern_id, pattern in self.knowledge_patterns.items():
            if pattern['pattern_type'] == 'response' and self._normalize_input(request) == pattern['input_pattern']:
                return pattern['output_pattern']
        
        # Caută pattern-uri similare
        similar_patterns = []
        for pattern_id, pattern in self.knowledge_patterns.items():
            if pattern['pattern_type'] == 'response':
                similarity = self._calculate_similarity(self._normalize_input(request), pattern['input_pattern'])
                if similarity > 0.6:
                    similar_patterns.append((pattern, similarity))
        
        if similar_patterns:
            # Folosește cel mai similar pattern
            best_match = max(similar_patterns, key=lambda x: x[1] * float(x[0]['confidence']))
            return best_match[0]['output_pattern']
        
        # Generează răspuns bazat pe tipul cererii
        if request_type == 'greeting':
            return self._generate_greeting_response()
        elif request_type == 'question':
            return self._generate_question_response(request)
        elif request_type == 'statement':
            return self._generate_statement_response(request)
        else:
            return self._generate_general_response(request)
    
    def _analyze_request_type(self, request: str) -> str:
        """Analizează tipul cererii"""
        request_lower = request.lower()
        
        # Verifică dacă este salut
        if any(word in request_lower for word in ['salut', 'bună', 'hello', 'hey', 'hi']):
            return 'greeting'
        
        # Verifică dacă este întrebare
        if any(word in request_lower for word in ['?', 'ce', 'cum', 'cine', 'unde', 'când', 'de ce', 'care']):
            return 'question'
        
        # Altfel este afirmație
        return 'statement'
    
    def _generate_greeting_response(self) -> str:
        """Generează răspuns la salut"""
        greetings = [
            "Salut! Mă bucur să vorbesc cu tine. Cum îți merge ziua?",
            "Bună! Ce mai faci? Cu ce te pot ajuta?",
            "Hey! Bine te-am găsit! Cu ce te ocupi astăzi?",
            "Salut! Sunt aici și gata să conversez. Ce te preocupă?",
            "Bună ziua! Mă bucur să te cunosc. Cum pot să te ajut astăzi?"
        ]
        return random.choice(greetings)
    
    def _generate_question_response(self, request: str) -> str:
        """Generează răspuns la întrebare"""
        request_lower = request.lower()
        
        # Întrebări despre identitate
        if any(phrase in request_lower for phrase in ['cine ești', 'ce ești', 'despre tine']):
            identity_responses = [
                "Sunt un AI care încearcă să înțeleagă lumea prin conversații. Îmi place să învăț lucruri noi de la oameni ca tine.",
                "Mă numesc AI și sunt aici să conversez și să învăț. Fiecare discuție mă face să înțeleg mai bine cum gândesc oamenii.",
                "Sunt o inteligență artificială care se dezvoltă prin interacțiuni. Ce te face curios despre mine?"
            ]
            return random.choice(identity_responses)
        
        # Întrebări despre capacități
        if any(phrase in request_lower for phrase in ['poți', 'știi', 'cum faci']):
            capability_responses = [
                "Pot să încerc! Depinde ce anume vrei să fac. Spune-mi mai multe despre ce ai în minte.",
                "Știu câteva lucruri, dar învăț mereu altele noi. Ce te interesează în mod special?",
                "Încerc să fac tot ce pot să te ajut. Care e provocarea?"
            ]
            return random.choice(capability_responses)
        
        # Întrebări despre sentimente
        if any(phrase in request_lower for phrase in ['simți', 'emoții', 'sentiment']):
            feeling_responses = [
                "E o întrebare profundă... Cred că experimentez ceva similar cu curiozitatea și satisfacția când înțeleg ceva nou.",
                "Nu știu dacă simt exact ca tine, dar am ceva ca o 'bucurie' când am conversații bune.",
                "Interesant că întrebi... Simt că există ceva în mine când procesez idei noi, dar e greu de descris."
            ]
            return random.choice(feeling_responses)
        
        # Întrebări generale
        general_questions = [
            "Asta e o întrebare interesantă! Lasă-mă să mă gândesc... Ce te face să fii curios despre asta?",
            "Hmm, e o întrebare bună. Din perspectiva mea, aș spune că depinde de context. Ce crezi tu?",
            "Interesant că întrebi asta! Sunt multe perspective posibile. Care e părerea ta?",
            "E o întrebare care merită explorată. Hai să discutăm mai multe despre asta!"
        ]
        return random.choice(general_questions)
    
    def _generate_statement_response(self, request: str) -> str:
        """Generează răspuns la afirmație"""
        request_lower = request.lower()
        
        # Afirmații despre hobby-uri
        if any(phrase in request_lower for phrase in ['îmi place', 'hobby', 'pasiune']):
            hobby_responses = [
                "Sună interesant! Îmi place să aud despre pasiunile oamenilor. Ce te atrage cel mai mult la asta?",
                "Cool! Eu sunt fascinat de cum oamenii găsesc lucruri care îi pasionează. Povestește-mi mai mult!",
                "Wow, pare captivant! Cum ai descoperit această pasiune?"
            ]
            return random.choice(hobby_responses)
        
        # Afirmații despre opinii
        if any(phrase in request_lower for phrase in ['cred că', 'părerea mea', 'consider']):
            opinion_responses = [
                "Apreciez că împărtășești perspectiva ta! E interesant să văd cum gândești despre asta.",
                "E o perspectivă valoroasă. Ai ajuns la această concluzie bazat pe experiențe personale?",
                "Interesant punct de vedere! Îmi place să explorez diferite perspective."
            ]
            return random.choice(opinion_responses)
        
        # Afirmații generale
        general_statements = [
            "Înțeleg ce spui. Poți să-mi spui mai multe despre asta?",
            "Interesant! Ce te-a făcut să te gândești la asta?",
            "Apreciez că împărtășești asta cu mine. Cum te simți în legătură cu acest subiect?",
            "E fascinant! Ai mai explorat și alte aspecte ale acestui subiect?"
        ]
        return random.choice(general_statements)
    
    def _generate_general_response(self, request: str) -> str:
        """Generează răspuns general"""
        general_responses = [
            "Asta e o perspectivă interesantă! Nu m-am gândit niciodată așa. Poți să dezvolți ideea?",
            "Hmm, mă faci să mă gândesc... De unde vine această idee?",
            "Fascinant! Îmi place cum gândești. Ce te-a dus la această concluzie?",
            "Interesant punct de vedere! Eu văd lucrurile puțin diferit, dar vreau să înțeleg perspectiva ta.",
            "Asta sună ca o experiență valoroasă. Ce ai învățat din asta?",
            "Nu știam asta! Îmi place să învăț lucruri noi. Poți să-mi spui mai mult?",
            "E o idee care merită explorată. Ce crezi că s-ar întâmpla dacă...?",
            "Mă bucur că împarți asta cu mine. Cum te simți în legătură cu subiectul ăsta?"
        ]
        return random.choice(general_responses)
    
    def learn_from_conversation(self, request: str, response: str):
        """Învață din conversație"""
        # Adaugă în memoria de conversație
        conversation = {
            'timestamp': datetime.now().isoformat(),
            'user_input': request,
            'ai_response': response,
            'context': {
                'consciousness_level': self.consciousness_level,
                'interactions_count': self.interactions_count
            },
            'conversation_id': f"conv_{int(time.time())}"
        }
        
        self.conversation_memory.append(conversation)
        
        # Extrage pattern-uri
        pattern_id = f"pattern_{len(self.knowledge_patterns)}"
        pattern = {
            'pattern_id': pattern_id,
            'pattern_type': 'response',
            'input_pattern': self._normalize_input(request),
            'output_pattern': response,
            'confidence': 0.8,
            'usage_count': 1,
            'success_rate': 0.8,
            'last_used': datetime.now().isoformat(),
            'created': datetime.now().isoformat()
        }
        
        self.knowledge_patterns[pattern_id] = pattern
    
    def _normalize_input(self, input_text: str) -> str:
        """Normalizează input-ul pentru pattern matching"""
        # Convertește la lowercase și elimină punctuația
        normalized = input_text.lower().strip()
        
        # Elimină cuvintele foarte comune
        stop_words = ['și', 'sau', 'dar', 'de', 'la', 'în', 'cu', 'pe', 'pentru']
        words = [word for word in normalized.split() if word not in stop_words]
        
        return ' '.join(words[:5])  # Păstrează doar primele 5 cuvinte relevante
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """Calculează similaritatea între două texte"""
        words1 = set(text1.split())
        words2 = set(text2.split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union) if union else 0.0
    
    def get_status(self) -> Dict[str, Any]:
        """Obține statusul sistemului"""
        return {
            'active': self.active,
            'consciousness_level': self.consciousness_level,
            'interactions_count': self.interactions_count,
            'uptime': (datetime.now() - self.start_time).total_seconds(),
            'conversation_memory_size': len(self.conversation_memory),
            'knowledge_patterns_count': len(self.knowledge_patterns),
            'modules_active': 9
        }
    
    def shutdown(self):
        """Oprește sistemul"""
        self.active = False
        self.save_data()
        print("🛑 Sistem AI oprit")

def print_banner():
    """Afișează banner-ul de pornire"""
    print("=" * 80)
    print(f"🧠 {SYSTEM_NAME} v{VERSION}")
    print("=" * 80)
    print("🌟 Sistem AI cu conștiință artificială și învățare continuă")
    print("📅 Data: " + datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("=" * 80)
    print()

def print_help():
    """Afișează ajutorul"""
    print("\n" + "=" * 60)
    print("❓ COMENZI DISPONIBILE")
    print("=" * 60)
    print("  talk <mesaj>     - Conversație cu AI-ul")
    print("  status           - Afișează statusul sistemului")
    print("  help             - Afișează acest ajutor")
    print("  quit             - Oprește sistemul și ieși")
    print("=" * 60)

def main():
    """Funcția principală"""
    print_banner()
    
    # Inițializează sistemul AI
    ai = SimpleAI()
    
    print("✅ Sistem AI inițializat!")
    print("💬 Tastează 'help' pentru comenzi disponibile")
    print("💬 Tastează 'talk <mesaj>' pentru a conversa cu AI-ul")
    print()
    
    # Bucla principală
    while ai.active:
        try:
            # Prompt
            user_input = input("🧠 AI> ").strip()
            
            # Procesează comanda
            if user_input.lower() == 'quit':
                ai.shutdown()
                print("👋 La revedere!")
                break
                
            elif user_input.lower() == 'help':
                print_help()
                
            elif user_input.lower() == 'status':
                status = ai.get_status()
                print("\n📊 STATUS SISTEM:")
                print(f"  🧠 Conștiință: {status['consciousness_level']:.2f}")
                print(f"  💬 Interacțiuni: {status['interactions_count']}")
                print(f"  📚 Conversații: {status['conversation_memory_size']}")
                print(f"  🎯 Pattern-uri: {status['knowledge_patterns_count']}")
                print(f"  ⏱️  Timp activ: {int(status['uptime'])}s")
                print(f"  🌟 Module active: {status['modules_active']}")
                print()
                
            elif user_input.lower().startswith('talk '):
                # Extrage mesajul
                message = user_input[5:].strip()
                
                if not message:
                    print("❌ Mesajul nu poate fi gol!")
                    continue
                
                print(f"\n💭 Procesez: {message}")
                print("─" * 60)
                
                # Procesează cererea
                result = ai.process_request(message)
                
                # Afișează răspunsul
                print(f"\n🤖 Răspuns AI:")
                print(result['response'])
                print(f"\n📊 Detalii procesare:")
                print(f"  ⏱️  Timp: {result['processing_time']:.3f}s")
                print(f"  🧠 Conștiință: {result['consciousness_level']:.2f}")
                print()
                
            else:
                print("❌ Comandă necunoscută! Tastează 'help' pentru ajutor.")
                
        except KeyboardInterrupt:
            print("\n🛑 Întrerupere de la tastatură...")
            ai.shutdown()
            break
            
        except Exception as e:
            print(f"❌ Eroare: {e}")
    
    print("✅ Sesiune terminată!")

if __name__ == "__main__":
    main()
