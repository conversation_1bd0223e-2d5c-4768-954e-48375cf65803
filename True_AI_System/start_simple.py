#!/usr/bin/env python3
"""
START SIMPLE - Script de pornire simplificat pentru TRUE AI SYSTEM
Pornește sistemul AI stabil fără probleme de thread-uri
"""

import os
import sys
import subprocess
import time
import webbrowser
from datetime import datetime

def print_banner():
    """Afișează banner-ul"""
    print("=" * 80)
    print("🧠 TRUE AI SYSTEM - PORNIRE SIMPLIFICATĂ")
    print("=" * 80)
    print("🌟 Sistem AI cu conștiință artificială și învățare continuă")
    print("📅 Data: " + datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("=" * 80)
    print()

def check_dependencies():
    """Verifică dependențele"""
    print("🔍 Verificare dependențe...")
    
    try:
        import flask
        print("  ✅ Flask")
    except ImportError:
        print("  ❌ Flask - LIPSEȘTE")
        print("💡 Instalează cu: pip install flask --break-system-packages")
        return False
    
    print("✅ Toate dependențele sunt instalate!")
    return True

def main():
    """Funcția principală"""
    print_banner()
    
    # Verifică dependențele
    if not check_dependencies():
        print("❌ Dependențe lipsă! Opresc lansarea.")
        return
    
    print("🚀 Pornire TRUE AI SYSTEM...")
    print()
    
    # Schimbă în directorul corect
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    print("🎮 Alege modul de funcționare:")
    print("1. 🖥️  Terminal - Conversație în terminal")
    print("2. 🌐 Web - Interfață web (http://localhost:5001)")
    print("3. 🚀 Ambele - Terminal + Web")
    print()
    
    while True:
        try:
            choice = input("Alege opțiunea (1-3): ").strip()
            
            if choice == '1':
                print("\n🖥️  Pornire interfață terminal...")
                subprocess.run([sys.executable, 'simple_ai.py'])
                break
                
            elif choice == '2':
                print("\n🌐 Pornire interfață web...")
                print("🔗 Accesează: http://localhost:5001")
                
                # Pornește serverul web
                web_process = subprocess.Popen([sys.executable, 'simple_web.py'])
                
                # Așteaptă puțin și deschide browser-ul
                time.sleep(3)
                try:
                    webbrowser.open('http://localhost:5001')
                    print("🌐 Browser deschis automat!")
                except:
                    print("🔗 Deschide manual: http://localhost:5001")
                
                print("\n💡 Apasă Ctrl+C pentru a opri serverul")
                
                try:
                    web_process.wait()
                except KeyboardInterrupt:
                    print("\n🛑 Oprire server web...")
                    web_process.terminate()
                    web_process.wait()
                    print("✅ Server oprit!")
                
                break
                
            elif choice == '3':
                print("\n🚀 Pornire ambele interfețe...")
                
                # Pornește interfața web în background
                print("🌐 Pornire interfață web...")
                web_process = subprocess.Popen([sys.executable, 'simple_web.py'])
                
                # Așteaptă puțin
                time.sleep(3)
                
                # Deschide browser-ul
                try:
                    webbrowser.open('http://localhost:5001')
                    print("🌐 Browser deschis automat!")
                except:
                    print("🔗 Deschide manual: http://localhost:5001")
                
                print("🖥️  Pornire interfață terminal...")
                print("💡 Interfața web rulează pe http://localhost:5001")
                print()
                
                try:
                    # Pornește interfața terminal
                    subprocess.run([sys.executable, 'simple_ai.py'])
                except KeyboardInterrupt:
                    print("\n🛑 Oprire sisteme...")
                finally:
                    # Oprește serverul web
                    web_process.terminate()
                    web_process.wait()
                    print("✅ Toate sistemele oprite!")
                
                break
                
            else:
                print("❌ Opțiune invalidă! Alege 1, 2 sau 3.")
                
        except KeyboardInterrupt:
            print("\n🛑 Întrerupere de la tastatură...")
            break
        except Exception as e:
            print(f"❌ Eroare: {e}")
    
    print("\n👋 La revedere!")

if __name__ == "__main__":
    main()
