"""
COGNITIVE PROCESSOR - Procesare cognitivă avansată cu module specializate
Implementează gândirea reală, conștiința și procesarea complexă
"""

import time
import threading
import queue
import hashlib
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from neural_core import TrueNeuralCore, CognitiveModule

@dataclass
class Thought:
    """Reprezentarea unui gând/proces cognitiv"""
    id: str
    content: str
    type: str  # 'concept', 'memory', 'inference', 'creative', 'meta'
    activation_level: float
    associations: List[str]
    timestamp: datetime
    processing_time: float
    confidence: float

@dataclass
class CognitiveState:
    """Starea cognitivă curentă"""
    active_thoughts: List[Thought]
    attention_focus: Optional[str]
    working_memory: List[str]
    emotional_state: Dict[str, float]
    consciousness_stream: List[str]
    meta_cognition_level: float

class CognitiveProcessor:
    """Procesorul cognitiv principal"""
    
    def __init__(self, neural_core: TrueNeuralCore):
        self.neural_core = neural_core
        self.cognitive_state = CognitiveState(
            active_thoughts=[],
            attention_focus=None,
            working_memory=[],
            emotional_state={'curiosity': 0.7, 'confidence': 0.5, 'creativity': 0.6},
            consciousness_stream=[],
            meta_cognition_level=0.0
        )
        
        # Queue pentru procesarea gândurilor
        self.thought_queue = queue.PriorityQueue()
        self.processing_threads = []
        
        # Inițializează thread-urile de procesare
        for i in range(3):  # 3 thread-uri cognitive
            thread = threading.Thread(target=self._cognitive_processing_loop, daemon=True)
            thread.start()
            self.processing_threads.append(thread)
        
        # Thread pentru conștiința
        self.consciousness_thread = threading.Thread(target=self._consciousness_stream, daemon=True)
        self.consciousness_thread.start()
        
        print("🧠 Cognitive Processor inițializat cu 3 thread-uri cognitive")
    
    def process_input(self, input_text: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Procesează input-ul prin toate modulele cognitive"""
        start_time = time.time()
        
        # Activează modulele neurale relevante
        self._activate_relevant_modules(input_text)
        
        # Generează gânduri inițiale
        initial_thoughts = self._generate_initial_thoughts(input_text, context or {})
        
        # Adaugă gândurile în queue pentru procesare
        for thought in initial_thoughts:
            priority = int((1.0 - thought.activation_level) * 100)  # Prioritate mai mare = număr mai mic
            self.thought_queue.put((priority, thought))
        
        # Așteaptă procesarea
        time.sleep(0.5)  # Permite procesarea
        
        # Colectează rezultatele
        results = self._collect_processing_results()
        
        processing_time = time.time() - start_time
        
        return {
            'input': input_text,
            'processing_time': processing_time,
            'consciousness_level': self.neural_core.consciousness_level,
            'active_modules': self._get_active_modules(),
            'thoughts_generated': len(initial_thoughts),
            'cognitive_state': self._serialize_cognitive_state(),
            'results': results,
            'meta_insights': self._generate_meta_insights()
        }
    
    def _activate_relevant_modules(self, input_text: str):
        """Activează modulele neurale relevante pentru input"""
        input_lower = input_text.lower()
        
        # Activează modulul de limbaj pentru orice input
        self._stimulate_module('language', 0.8)
        
        # Activează module specifice bazate pe conținut
        if any(word in input_lower for word in ['de ce', 'cum', 'explică', 'logică']):
            self._stimulate_module('reasoning', 0.9)
        
        if any(word in input_lower for word in ['creează', 'inventează', 'imaginează', 'nou']):
            self._stimulate_module('creativity', 0.8)
        
        if any(word in input_lower for word in ['amintește', 'memorie', 'știu', 'învățat']):
            self._stimulate_module('memory', 0.7)
        
        if any(word in input_lower for word in ['învață', 'adaptează', 'îmbunătățește']):
            self._stimulate_module('learning', 0.8)
        
        # Modulul de conștiință este mereu activ
        self._stimulate_module('consciousness', 0.6)
    
    def _stimulate_module(self, module_name: str, intensity: float):
        """Stimulează un modul cognitiv"""
        if module_name not in self.neural_core.cognitive_modules:
            return
        
        module = self.neural_core.cognitive_modules[module_name]
        
        # Stimulează nodurile din modul
        for node in module.neural_network.values():
            node.activation += intensity * 0.1  # Stimulare graduală
            node.activation = min(1.0, node.activation)  # Cap la 1.0
    
    def _generate_initial_thoughts(self, input_text: str, context: Dict[str, Any]) -> List[Thought]:
        """Generează gândurile inițiale pentru procesare"""
        thoughts = []
        
        # Gând conceptual
        concept_thought = Thought(
            id=hashlib.md5(f"concept_{input_text}_{time.time()}".encode()).hexdigest()[:8],
            content=f"Conceptualizare: {input_text}",
            type='concept',
            activation_level=0.8,
            associations=[],
            timestamp=datetime.now(),
            processing_time=0.0,
            confidence=0.7
        )
        thoughts.append(concept_thought)
        
        # Gând de memorie
        memory_thought = Thought(
            id=hashlib.md5(f"memory_{input_text}_{time.time()}".encode()).hexdigest()[:8],
            content=f"Căutare în memorie pentru: {input_text}",
            type='memory',
            activation_level=0.6,
            associations=[],
            timestamp=datetime.now(),
            processing_time=0.0,
            confidence=0.6
        )
        thoughts.append(memory_thought)
        
        # Gând inferențial
        inference_thought = Thought(
            id=hashlib.md5(f"inference_{input_text}_{time.time()}".encode()).hexdigest()[:8],
            content=f"Inferență logică despre: {input_text}",
            type='inference',
            activation_level=0.7,
            associations=[],
            timestamp=datetime.now(),
            processing_time=0.0,
            confidence=0.8
        )
        thoughts.append(inference_thought)
        
        # Gând creativ (dacă este relevant)
        if any(word in input_text.lower() for word in ['creează', 'nou', 'inventează', 'idee']):
            creative_thought = Thought(
                id=hashlib.md5(f"creative_{input_text}_{time.time()}".encode()).hexdigest()[:8],
                content=f"Explorare creativă: {input_text}",
                type='creative',
                activation_level=0.9,
                associations=[],
                timestamp=datetime.now(),
                processing_time=0.0,
                confidence=0.5
            )
            thoughts.append(creative_thought)
        
        # Gând meta-cognitiv
        meta_thought = Thought(
            id=hashlib.md5(f"meta_{input_text}_{time.time()}".encode()).hexdigest()[:8],
            content=f"Meta-analiză a procesării pentru: {input_text}",
            type='meta',
            activation_level=0.4,
            associations=[],
            timestamp=datetime.now(),
            processing_time=0.0,
            confidence=0.6
        )
        thoughts.append(meta_thought)
        
        return thoughts
    
    def _cognitive_processing_loop(self):
        """Loop principal de procesare cognitivă"""
        while True:
            try:
                # Preia un gând din queue
                priority, thought = self.thought_queue.get(timeout=1.0)
                
                # Procesează gândul
                processed_thought = self._process_thought(thought)
                
                # Adaugă în starea cognitivă
                self.cognitive_state.active_thoughts.append(processed_thought)
                
                # Limitează numărul de gânduri active
                if len(self.cognitive_state.active_thoughts) > 20:
                    self.cognitive_state.active_thoughts.pop(0)
                
                # Marchează task-ul ca terminat
                self.thought_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                print(f"Eroare în procesarea cognitivă: {e}")
    
    def _process_thought(self, thought: Thought) -> Thought:
        """Procesează un gând individual"""
        start_time = time.time()
        
        if thought.type == 'concept':
            thought = self._process_conceptual_thought(thought)
        elif thought.type == 'memory':
            thought = self._process_memory_thought(thought)
        elif thought.type == 'inference':
            thought = self._process_inference_thought(thought)
        elif thought.type == 'creative':
            thought = self._process_creative_thought(thought)
        elif thought.type == 'meta':
            thought = self._process_meta_thought(thought)
        
        thought.processing_time = time.time() - start_time
        return thought
    
    def _process_conceptual_thought(self, thought: Thought) -> Thought:
        """Procesează un gând conceptual"""
        # Simulează procesarea conceptuală prin modulul de limbaj
        language_module = self.neural_core.cognitive_modules.get('language')
        if language_module:
            # Calculează activarea medie în modulul de limbaj
            avg_activation = sum(node.activation for node in language_module.neural_network.values()) / len(language_module.neural_network)
            thought.confidence *= (0.5 + avg_activation * 0.5)
        
        # Generează asociații
        thought.associations = self._generate_associations(thought.content, 'conceptual')
        
        return thought
    
    def _process_memory_thought(self, thought: Thought) -> Thought:
        """Procesează un gând de memorie"""
        # Caută în baza de cunoștințe
        search_terms = thought.content.split()[-3:]  # Ultimele 3 cuvinte
        
        relevant_memories = []
        for term in search_terms:
            memory = self.neural_core.knowledge_base.retrieve_knowledge(term)
            if memory:
                relevant_memories.append(str(memory)[:100])
        
        if relevant_memories:
            thought.associations = relevant_memories
            thought.confidence += 0.2
        
        return thought
    
    def _process_inference_thought(self, thought: Thought) -> Thought:
        """Procesează un gând inferențial"""
        reasoning_module = self.neural_core.cognitive_modules.get('reasoning')
        if reasoning_module:
            # Simulează raționamentul logic
            deductive_nodes = [node for node_id, node in reasoning_module.neural_network.items() if 'deductive' in node_id]
            if deductive_nodes:
                avg_deductive = sum(node.activation for node in deductive_nodes) / len(deductive_nodes)
                thought.confidence *= (0.3 + avg_deductive * 0.7)
        
        # Generează inferențe
        thought.associations = self._generate_logical_inferences(thought.content)
        
        return thought
    
    def _process_creative_thought(self, thought: Thought) -> Thought:
        """Procesează un gând creativ"""
        creativity_module = self.neural_core.cognitive_modules.get('creativity')
        if creativity_module:
            # Stimulează creativitatea
            for node in creativity_module.neural_network.values():
                if 'divergent' in node.id:
                    node.activation += 0.1
        
        # Generează idei creative
        thought.associations = self._generate_creative_associations(thought.content)
        thought.confidence *= 0.8  # Creativitatea are incertitudine
        
        return thought
    
    def _process_meta_thought(self, thought: Thought) -> Thought:
        """Procesează un gând meta-cognitiv"""
        # Analizează propria procesare
        thought.associations = [
            f"Nivel conștiință: {self.neural_core.consciousness_level:.2f}",
            f"Gânduri active: {len(self.cognitive_state.active_thoughts)}",
            f"Focus atenție: {self.cognitive_state.attention_focus or 'Difuz'}",
            f"Stare emoțională: {max(self.cognitive_state.emotional_state, key=self.cognitive_state.emotional_state.get)}"
        ]
        
        # Actualizează meta-cogniția
        self.cognitive_state.meta_cognition_level = min(1.0, self.cognitive_state.meta_cognition_level + 0.1)
        
        return thought
    
    def _generate_associations(self, content: str, association_type: str) -> List[str]:
        """Generează asociații pentru un conținut"""
        associations = []
        
        if association_type == 'conceptual':
            # Asociații conceptuale simple
            words = content.lower().split()
            for word in words:
                if word in ['creează', 'create']:
                    associations.extend(['construiește', 'dezvoltă', 'inventează'])
                elif word in ['analizează', 'analyze']:
                    associations.extend(['studiază', 'examinează', 'investighează'])
                elif word in ['învață', 'learn']:
                    associations.extend(['adaptează', 'îmbunătățește', 'evoluează'])
        
        return associations[:5]  # Limitează la 5 asociații
    
    def _generate_logical_inferences(self, content: str) -> List[str]:
        """Generează inferențe logice"""
        inferences = []
        
        # Inferențe simple bazate pe pattern-uri
        if 'dacă' in content.lower():
            inferences.append("Relație cauzală identificată")
        if 'toate' in content.lower() or 'fiecare' in content.lower():
            inferences.append("Generalizare universală")
        if 'unele' in content.lower() or 'câteva' in content.lower():
            inferences.append("Generalizare parțială")
        
        return inferences
    
    def _generate_creative_associations(self, content: str) -> List[str]:
        """Generează asociații creative"""
        creative_ideas = [
            "Abordare neconvențională",
            "Combinație inovatoare de concepte",
            "Perspectivă alternativă",
            "Soluție creativă emergentă",
            "Conexiune neașteptată"
        ]
        
        return creative_ideas[:3]  # Returnează 3 idei creative
    
    def _consciousness_stream(self):
        """Simulează fluxul conștiinței"""
        while True:
            try:
                # Adaugă în fluxul conștiinței
                if self.cognitive_state.active_thoughts:
                    latest_thought = self.cognitive_state.active_thoughts[-1]
                    consciousness_entry = f"[{datetime.now().strftime('%H:%M:%S')}] {latest_thought.type}: {latest_thought.content[:50]}..."
                    
                    self.cognitive_state.consciousness_stream.append(consciousness_entry)
                    
                    # Limitează fluxul
                    if len(self.cognitive_state.consciousness_stream) > 50:
                        self.cognitive_state.consciousness_stream.pop(0)
                
                time.sleep(2.0)  # Actualizare la 2 secunde
                
            except Exception as e:
                print(f"Eroare în fluxul conștiinței: {e}")
                time.sleep(5.0)
    
    def _get_active_modules(self) -> List[str]:
        """Returnează modulele cognitive active"""
        active_modules = []
        
        for module_name, module in self.neural_core.cognitive_modules.items():
            avg_activation = sum(node.activation for node in module.neural_network.values()) / len(module.neural_network)
            if avg_activation > 0.3:  # Threshold pentru "activ"
                active_modules.append(f"{module_name} ({avg_activation:.2f})")
        
        return active_modules
    
    def _serialize_cognitive_state(self) -> Dict[str, Any]:
        """Serializează starea cognitivă pentru output"""
        return {
            'active_thoughts_count': len(self.cognitive_state.active_thoughts),
            'attention_focus': self.cognitive_state.attention_focus,
            'working_memory_items': len(self.cognitive_state.working_memory),
            'emotional_state': self.cognitive_state.emotional_state,
            'consciousness_stream_length': len(self.cognitive_state.consciousness_stream),
            'meta_cognition_level': self.cognitive_state.meta_cognition_level,
            'latest_thoughts': [
                {
                    'type': t.type,
                    'content': t.content[:100],
                    'confidence': t.confidence,
                    'associations_count': len(t.associations)
                }
                for t in self.cognitive_state.active_thoughts[-3:]  # Ultimele 3 gânduri
            ]
        }
    
    def _collect_processing_results(self) -> Dict[str, Any]:
        """Colectează rezultatele procesării"""
        if not self.cognitive_state.active_thoughts:
            return {'status': 'no_thoughts_processed'}
        
        # Analizează gândurile procesate
        thought_types = {}
        total_confidence = 0.0
        
        for thought in self.cognitive_state.active_thoughts[-10:]:  # Ultimele 10 gânduri
            thought_types[thought.type] = thought_types.get(thought.type, 0) + 1
            total_confidence += thought.confidence
        
        avg_confidence = total_confidence / len(self.cognitive_state.active_thoughts[-10:])
        
        return {
            'thought_distribution': thought_types,
            'average_confidence': avg_confidence,
            'processing_quality': 'high' if avg_confidence > 0.7 else 'medium' if avg_confidence > 0.5 else 'low',
            'dominant_thought_type': max(thought_types, key=thought_types.get) if thought_types else 'none'
        }
    
    def _generate_meta_insights(self) -> List[str]:
        """Generează insights meta-cognitive"""
        insights = []
        
        if self.neural_core.consciousness_level > 0.7:
            insights.append("Nivel înalt de conștiință - procesare integrată")
        
        if self.cognitive_state.meta_cognition_level > 0.5:
            insights.append("Meta-cogniție activă - auto-monitorizare")
        
        active_modules = len(self._get_active_modules())
        if active_modules >= 4:
            insights.append("Procesare multi-modulară - gândire complexă")
        
        if len(self.cognitive_state.active_thoughts) > 15:
            insights.append("Activitate cognitivă intensă")
        
        return insights
    
    def get_consciousness_stream(self, last_n: int = 10) -> List[str]:
        """Returnează ultimele intrări din fluxul conștiinței"""
        return self.cognitive_state.consciousness_stream[-last_n:]
    
    def get_neural_state_summary(self) -> Dict[str, Any]:
        """Returnează un sumar al stării neurale"""
        summary = {}
        
        for module_name, module in self.neural_core.cognitive_modules.items():
            total_nodes = len(module.neural_network)
            active_nodes = sum(1 for node in module.neural_network.values() if node.activation > 0.5)
            avg_activation = sum(node.activation for node in module.neural_network.values()) / total_nodes
            
            summary[module_name] = {
                'total_nodes': total_nodes,
                'active_nodes': active_nodes,
                'activation_percentage': (active_nodes / total_nodes) * 100,
                'average_activation': avg_activation,
                'efficiency': module.efficiency,
                'specialization': module.specialization
            }
        
        return summary
