#!/usr/bin/env python3
"""
LFM2 GGUF WEB INTERFACE - Interfață web pentru sistemul LFM2-1.2B GGUF
Interfață web avansată cu monitorizare în timp real
"""

from flask import Flask, render_template, request, jsonify
from flask_socketio import Socket<PERSON>, emit
import time
import threading
import os
from datetime import datetime
from lfm2_gguf_system import LFM2GGUFSystem

app = Flask(__name__)
app.config['SECRET_KEY'] = 'lfm2_gguf_secret_key'
socketio = SocketIO(app, cors_allowed_origins="*")

# Instanța globală a sistemului AI real
ai_system = None
status_thread = None

def initialize_gguf_ai():
    """Inițializează sistemul AI GGUF"""
    global ai_system
    if ai_system is None:
        print("🚀 Inițializare sistem AI cu LFM2-1.2B GGUF...")
        ai_system = LFM2GGUFSystem()
    return ai_system

def broadcast_status():
    """Transmite statusul în timp real"""
    while True:
        try:
            if ai_system:
                status = ai_system.get_system_status()
                
                # Verifică progresul descărcării
                if not status['model_loaded'] and os.path.exists(ai_system.model_manager.model_path):
                    file_size = os.path.getsize(ai_system.model_manager.model_path)
                    expected_size = 730893248  # 731 MB
                    progress = min(100, (file_size / expected_size) * 100)
                    status['download_progress'] = progress
                    ai_system.model_manager.loading_progress = int(progress)
                
                socketio.emit('status_update', status)
            time.sleep(2)  # Update la fiecare 2 secunde
        except Exception as e:
            print(f"Eroare broadcast status: {e}")
            time.sleep(10)

@app.route('/')
def index():
    """Pagina principală"""
    return render_template('lfm2_gguf_chat.html')

@app.route('/api/chat', methods=['POST'])
def chat():
    """Endpoint pentru chat cu modelul REAL LFM2-1.2B GGUF"""
    try:
        data = request.get_json()
        message = data.get('message', '').strip()
        
        if not message:
            return jsonify({'error': 'Mesajul nu poate fi gol'}), 400
        
        # Inițializează sistemul dacă nu e inițializat
        ai = initialize_gguf_ai()
        
        # Procesează cu modelul REAL LFM2-1.2B GGUF
        result = ai.process_conversation(message)
        
        # Transmite mesajul în timp real
        socketio.emit('new_message', {
            'user_message': message,
            'ai_response': result['response'],
            'timestamp': datetime.now().isoformat(),
            'quality_score': result.get('quality_score', 0),
            'learning_value': result.get('learning_value', 0),
            'processing_time': result.get('processing_time', 0),
            'model_status': result.get('model_status', 'unknown')
        })
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/status')
def status():
    """Status sistem real GGUF"""
    try:
        ai = initialize_gguf_ai()
        status = ai.get_system_status()
        
        # Adaugă informații despre descărcare
        if not status['model_loaded'] and os.path.exists(ai.model_manager.model_path):
            file_size = os.path.getsize(ai.model_manager.model_path)
            expected_size = 730893248  # 731 MB
            progress = min(100, (file_size / expected_size) * 100)
            status['download_progress'] = progress
            status['downloaded_mb'] = file_size / (1024 * 1024)
            status['total_mb'] = expected_size / (1024 * 1024)
        
        return jsonify(status)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/model/info')
def model_info():
    """Informații despre modelul real LFM2-1.2B GGUF"""
    try:
        ai = initialize_gguf_ai()
        
        model_info = {
            'model_name': 'LiquidAI/LFM2-1.2B',
            'model_format': 'GGUF (Q4_K_M)',
            'model_type': 'Hybrid Liquid Model',
            'parameters': '1.17B',
            'quantization': 'Q4_K_M (4-bit)',
            'file_size': '731 MB',
            'context_length': '32,768 tokens',
            'architecture': 'Multiplicative gates + short convolutions',
            'inference_engine': 'llama.cpp',
            'is_loaded': ai.model_manager.is_loaded,
            'loading_progress': ai.model_manager.loading_progress,
            'model_file': ai.model_manager.model_path,
            'supported_languages': ['English', 'Arabic', 'Chinese', 'French', 'German', 'Japanese', 'Korean', 'Spanish'],
            'features': [
                'Fast CPU inference',
                'GPU acceleration support',
                'Memory efficient',
                'High quality responses',
                'Long context support'
            ]
        }
        
        # Verifică dacă fișierul există și mărimea
        if os.path.exists(ai.model_manager.model_path):
            file_size = os.path.getsize(ai.model_manager.model_path)
            model_info['downloaded_mb'] = file_size / (1024 * 1024)
            model_info['download_complete'] = file_size >= 730893248 * 0.95  # 95% din mărimea așteptată
        else:
            model_info['downloaded_mb'] = 0
            model_info['download_complete'] = False
        
        return jsonify(model_info)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/conversations')
def conversations():
    """Istoricul conversațiilor reale"""
    try:
        ai = initialize_gguf_ai()
        
        # Ultimele 30 de conversații
        recent_conversations = ai.conversation_history[-30:]
        
        conversations_data = []
        for i, conv in enumerate(recent_conversations):
            conversations_data.append({
                'id': i,
                'timestamp': conv.timestamp,
                'user_input': conv.user_input,
                'ai_response': conv.ai_response,
                'quality_score': conv.quality_score,
                'learning_value': conv.learning_value,
                'processing_time': conv.processing_time
            })
        
        return jsonify({
            'conversations': conversations_data,
            'total_count': len(ai.conversation_history)
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/learning/stats')
def learning_stats():
    """Statistici învățare continuă"""
    try:
        ai = initialize_gguf_ai()
        
        if not ai.learning_engine:
            return jsonify({
                'learning_active': False,
                'training_sessions': 0,
                'high_quality_conversations': 0,
                'learning_data_size': 0
            })
        
        # Calculează conversațiile de calitate înaltă
        high_quality_convs = [
            conv for conv in ai.conversation_history 
            if conv.quality_score > 0.7
        ]
        
        return jsonify({
            'learning_active': True,
            'training_sessions': ai.learning_engine.training_sessions,
            'high_quality_conversations': len(high_quality_convs),
            'learning_data_size': len(ai.learning_engine.learning_data),
            'conversation_buffer_size': len(ai.learning_engine.conversation_buffer),
            'is_training': ai.learning_engine.is_training,
            'average_quality': sum(conv.quality_score for conv in ai.conversation_history) / max(len(ai.conversation_history), 1),
            'average_learning_value': sum(conv.learning_value for conv in ai.conversation_history) / max(len(ai.conversation_history), 1),
            'average_processing_time': sum(conv.processing_time for conv in ai.conversation_history) / max(len(ai.conversation_history), 1)
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/download/progress')
def download_progress():
    """Progresul descărcării modelului"""
    try:
        ai = initialize_gguf_ai()
        
        if os.path.exists(ai.model_manager.model_path):
            file_size = os.path.getsize(ai.model_manager.model_path)
            expected_size = 730893248  # 731 MB
            progress = min(100, (file_size / expected_size) * 100)
            
            return jsonify({
                'downloading': not ai.model_manager.is_loaded,
                'progress': progress,
                'downloaded_mb': file_size / (1024 * 1024),
                'total_mb': expected_size / (1024 * 1024),
                'eta_minutes': ((expected_size - file_size) / (1024 * 1024)) / 0.5 if progress < 100 else 0  # Estimare la 0.5 MB/s
            })
        else:
            return jsonify({
                'downloading': True,
                'progress': 0,
                'downloaded_mb': 0,
                'total_mb': 731,
                'eta_minutes': 25  # Estimare inițială
            })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@socketio.on('connect')
def handle_connect():
    """Client conectat"""
    print(f"Client conectat: {request.sid}")
    
    # Trimite statusul inițial
    try:
        ai = initialize_gguf_ai()
        status = ai.get_system_status()
        emit('status_update', status)
    except Exception as e:
        print(f"Eroare trimitere status inițial: {e}")

@socketio.on('disconnect')
def handle_disconnect():
    """Client deconectat"""
    print(f"Client deconectat: {request.sid}")

@socketio.on('request_download_progress')
def handle_download_progress_request():
    """Cerere progres descărcare"""
    try:
        ai = initialize_gguf_ai()
        
        if os.path.exists(ai.model_manager.model_path):
            file_size = os.path.getsize(ai.model_manager.model_path)
            expected_size = 730893248  # 731 MB
            progress = min(100, (file_size / expected_size) * 100)
            
            emit('download_progress_update', {
                'progress': progress,
                'downloaded_mb': file_size / (1024 * 1024),
                'total_mb': expected_size / (1024 * 1024),
                'is_complete': progress >= 95
            })
        else:
            emit('download_progress_update', {
                'progress': 0,
                'downloaded_mb': 0,
                'total_mb': 731,
                'is_complete': False
            })
    except Exception as e:
        emit('error', {'message': str(e)})

def start_background_services():
    """Pornește serviciile de background"""
    global status_thread
    
    if status_thread is None:
        status_thread = threading.Thread(target=broadcast_status, daemon=True)
        status_thread.start()

if __name__ == '__main__':
    print("🌐 INTERFAȚĂ WEB REALĂ pentru LFM2-1.2B GGUF")
    print("=" * 60)
    print("🔗 Accesează: http://localhost:5005")
    print("🧠 Model real: LiquidAI/LFM2-1.2B (GGUF)")
    print("⚡ Inferență rapidă cu llama.cpp")
    print("🎓 Învățare continuă 24/7 activă")
    print("📊 Monitorizare descărcare în timp real")
    print("🚀 Arhitectură de înaltă performanță")
    print("=" * 60)
    print()
    
    # Inițializează sistemul AI real
    initialize_gguf_ai()
    
    # Pornește serviciile de background
    start_background_services()
    
    # Pornește serverul
    socketio.run(app, host='0.0.0.0', port=5005, debug=False)
