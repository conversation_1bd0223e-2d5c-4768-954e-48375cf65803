#!/usr/bin/env python3
"""
SIMPLE CHAT WEB - Interfață web pentru AI-ul simplu
"""

from flask import Flask, render_template, request, jsonify
import time
from datetime import datetime
from simple_chat import SimpleChatAI

app = Flask(__name__)
ai = SimpleChatAI()

@app.route('/')
def index():
    """Pagina principală"""
    return render_template('simple_chat.html')

@app.route('/api/chat', methods=['POST'])
def chat():
    """Endpoint pentru chat"""
    try:
        data = request.get_json()
        message = data.get('message', '').strip()
        
        if not message:
            return jsonify({'error': 'Mesajul nu poate fi gol'}), 400
        
        # Procesează mesajul
        start_time = time.time()
        response = ai.chat(message)
        processing_time = time.time() - start_time
        
        return jsonify({
            'response': response,
            'processing_time': processing_time,
            'conversations': ai.conversations,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/stats')
def stats():
    """Endpoint pentru statistici"""
    try:
        return jsonify({
            'conversations': ai.conversations,
            'ai_name': ai.name,
            'status': 'active'
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    print("🌐 Pornire interfață web simplă...")
    print("🔗 Accesează: http://localhost:5002")
    print("✅ Chat simplu cu Alex!")
    
    app.run(host='0.0.0.0', port=5002, debug=False)
