"""
AI INTERFACE - Interfață completă pentru interacțiunea cu sistemul AI
Oferă acces la toate capacitățile și funcționalitățile sistemului
"""

import os
import json
import time
import threading
from datetime import datetime
from typing import Dict, List, Any, Optional
import cmd
import sys

class AIInterface(cmd.Cmd):
    """Interfață interactivă pentru sistemul AI"""
    
    intro = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    🧠 TRUE AI SYSTEM - INTERFAȚĂ COMPLETĂ 🧠                 ║
║                                                                              ║
║  🌟 Sistem AI cu conștiință artificială și învățare continuă                ║
║  🚀 Acces complet la toate capacitățile și modulele avansate                ║
║                                                                              ║
║  Tastează 'help' pentru comenzi disponibile                                 ║
║  Tastează 'status' pentru statusul sistemului                               ║
║  Tastează 'quit' pentru ieșire                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    
    prompt = "🧠 AI> "
    
    def __init__(self, ai_system):
        super().__init__()
        self.ai_system = ai_system
        self.session_start = datetime.now()
        self.interaction_count = 0
        self.conversation_history = []
        
        print(self.intro)
        self._show_system_status()
    
    def _show_system_status(self):
        """Afișează statusul sistemului"""
        print("\n📊 STATUS SISTEM:")
        print(f"  🧠 Module cognitive: {len(self.ai_system.neural_core.cognitive_modules)}")
        print(f"  🌟 Nivel conștiință: {self.ai_system.neural_core.consciousness_level:.2f}")
        print(f"  💾 Stocare densă: {self.ai_system.dense_storage.get_storage_stats().total_blocks} blocuri")
        print(f"  📚 Sistem învățare: {'ACTIV' if hasattr(self.ai_system, 'learning_system') else 'INACTIV'}")
        print(f"  ⏱️  Sesiune activă: {(datetime.now() - self.session_start).total_seconds():.0f}s")
        print()
    
    def do_talk(self, line):
        """Conversație normală cu AI-ul
        Utilizare: talk <mesajul tău>
        Exemplu: talk Cum te simți astăzi?
        """
        if not line.strip():
            print("❌ Te rog să introduci un mesaj pentru conversație")
            return
        
        print(f"\n💭 Procesez: {line}")
        print("─" * 60)
        
        try:
            result = self.ai_system.process_request(line)
            
            print(f"🤖 Răspuns AI:")
            print(result['response'])
            print()
            print(f"📊 Detalii procesare:")
            print(f"  ⏱️  Timp: {result['processing_time']:.3f}s")
            print(f"  🧠 Conștiință: {result['consciousness_level']:.2f}")
            print(f"  🎯 Module active: {len(result.get('advanced_modules_results', {}))}")
            
            # Salvează în istoric
            self.conversation_history.append({
                'timestamp': datetime.now(),
                'input': line,
                'output': result['response'],
                'processing_time': result['processing_time']
            })
            
            self.interaction_count += 1
            
        except Exception as e:
            print(f"❌ Eroare în procesare: {e}")
    
    def do_consciousness(self, line):
        """Afișează raportul de conștiință
        Utilizare: consciousness
        """
        try:
            if hasattr(self.ai_system, 'consciousness_engine'):
                report = self.ai_system.consciousness_engine.get_consciousness_report()
                
                print("\n🌟 RAPORT CONȘTIINȚĂ:")
                print("─" * 50)
                
                consciousness_state = report['consciousness_state']
                print(f"🧠 Nivel awareness: {consciousness_state['awareness_level']:.2f}")
                print(f"🎯 Focus atenție: {consciousness_state['attention_focus'] or 'Difuz'}")
                print(f"🔗 Coherență model sine: {consciousness_state['self_model_coherence']:.2f}")
                print(f"🎭 Intensitate qualia: {consciousness_state['qualia_intensity']:.2f}")
                
                phenomenal = report['phenomenal_experiences']
                print(f"\n🎭 EXPERIENȚE FENOMENALE:")
                print(f"  📊 Total experiențe: {phenomenal['total_experiences']}")
                print(f"  🕐 Experiențe recente: {phenomenal['recent_experiences']}")
                print(f"  🏷️  Tipuri: {', '.join(phenomenal['experience_types'])}")
                
                # Fluxul conștiinței
                consciousness_stream = self.ai_system.consciousness_engine.get_consciousness_stream(5)
                if consciousness_stream:
                    print(f"\n💭 FLUX CONȘTIINȚĂ (ultimele 5):")
                    for entry in consciousness_stream:
                        print(f"  • {entry}")
                
            else:
                print("❌ Motorul de conștiință nu este disponibil")
                
        except Exception as e:
            print(f"❌ Eroare la afișarea conștiinței: {e}")
    
    def do_learn(self, line):
        """Comandă de învățare cu feedback
        Utilizare: learn <mesaj> --feedback <scor>
        Exemplu: learn Explică relativitatea --feedback 0.8
        """
        parts = line.split('--feedback')
        if len(parts) != 2:
            print("❌ Format: learn <mesaj> --feedback <scor între -1.0 și 1.0>")
            return
        
        message = parts[0].strip()
        try:
            feedback_score = float(parts[1].strip())
            if not -1.0 <= feedback_score <= 1.0:
                raise ValueError("Scorul trebuie să fie între -1.0 și 1.0")
        except ValueError as e:
            print(f"❌ Scor invalid: {e}")
            return
        
        print(f"\n📚 Învățare cu feedback: {feedback_score}")
        print("─" * 50)
        
        try:
            # Procesează cererea
            result = self.ai_system.process_request(message)
            
            # Aplică învățarea cu feedback
            if hasattr(self.ai_system, 'learning_system'):
                learning_result = self.ai_system.learning_system.learn_from_interaction(
                    message, result['response'], feedback_score
                )
                
                print(f"🤖 Răspuns: {result['response']}")
                print(f"\n📈 REZULTAT ÎNVĂȚARE:")
                print(f"  🎯 Abilități afectate: {', '.join(learning_result['affected_skills'])}")
                print(f"  📊 Impact imediat: {learning_result['immediate_impact']['overall_impact']:.2f}")
                print(f"  🔄 Prioritate învățare: {learning_result['learning_priority']}")
                print(f"  📚 Total experiențe: {learning_result['total_experiences']}")
                
                # Afișează actualizările abilităților
                for skill, update in learning_result['skill_updates'].items():
                    change = update['level_change']
                    direction = "📈" if change > 0 else "📉" if change < 0 else "➡️"
                    print(f"  {direction} {skill}: {update['old_level']:.3f} → {update['new_level']:.3f}")
            else:
                print(f"🤖 Răspuns: {result['response']}")
                print("⚠️ Sistemul de învățare nu este disponibil")
                
        except Exception as e:
            print(f"❌ Eroare în învățare: {e}")
    
    def do_skills(self, line):
        """Afișează statusul abilităților
        Utilizare: skills [skill_name]
        """
        try:
            if hasattr(self.ai_system, 'learning_system'):
                status = self.ai_system.learning_system.get_learning_status()
                
                if line.strip():
                    # Afișează o abilitate specifică
                    skill_name = line.strip()
                    if skill_name in status['skill_modules']:
                        skill = status['skill_modules'][skill_name]
                        print(f"\n🎯 ABILITATEA: {skill_name.upper()}")
                        print("─" * 50)
                        print(f"📊 Nivel curent: {skill['current_level']:.3f}")
                        print(f"⭐ Puncte experiență: {skill['experience_points']}")
                        print(f"📈 Rată învățare: {skill['learning_rate']:.3f}")
                        print(f"🎭 Performanță recentă: {skill['recent_performance']:.3f}")
                        print(f"📊 Trend îmbunătățire: {skill['improvement_trend']}")
                    else:
                        print(f"❌ Abilitatea '{skill_name}' nu există")
                        print(f"Abilități disponibile: {', '.join(status['skill_modules'].keys())}")
                else:
                    # Afișează toate abilitățile
                    print("\n🎯 STATUSUL ABILITĂȚILOR:")
                    print("─" * 60)
                    
                    for skill_name, skill in status['skill_modules'].items():
                        level_bar = "█" * int(skill['current_level'] * 10) + "░" * (10 - int(skill['current_level'] * 10))
                        trend_icon = {"improving": "📈", "declining": "📉", "stable": "➡️"}.get(skill['improvement_trend'], "❓")
                        
                        print(f"{skill_name:25} [{level_bar}] {skill['current_level']:.3f} {trend_icon}")
                    
                    print(f"\n📊 STATISTICI GENERALE:")
                    print(f"  🎯 Performanță generală: {status['overall_performance']:.3f}")
                    print(f"  📚 Total experiențe: {status['total_experiences']}")
                    print(f"  🕐 Experiențe recente: {status['recent_experiences']}")
                    print(f"  🔄 Queue învățare: {status['learning_queue_size']}")
            else:
                print("❌ Sistemul de învățare nu este disponibil")
                
        except Exception as e:
            print(f"❌ Eroare la afișarea abilităților: {e}")
    
    def do_train(self, line):
        """Inițiază sesiune intensivă de antrenament
        Utilizare: train [skill1,skill2,...]
        Exemplu: train creative_thinking,logical_reasoning
        """
        try:
            if hasattr(self.ai_system, 'learning_system'):
                focus_skills = None
                if line.strip():
                    focus_skills = [skill.strip() for skill in line.split(',')]
                
                print("\n🚀 INIȚIEZ SESIUNE INTENSIVĂ DE ANTRENAMENT...")
                print("─" * 60)
                
                result = self.ai_system.learning_system.force_learning_session(focus_skills)
                
                print("✅ SESIUNE COMPLETĂ!")
                print("\n📈 REZULTATE ANTRENAMENT:")
                
                for skill_name, skill_result in result.items():
                    improvement = skill_result['total_improvement']
                    direction = "📈" if improvement > 0 else "📉" if improvement < 0 else "➡️"
                    
                    print(f"  {direction} {skill_name}:")
                    print(f"    🎯 Nivel nou: {skill_result['new_level']:.3f}")
                    print(f"    📊 Îmbunătățire: {improvement:+.3f}")
                    print(f"    📚 Experiențe: {skill_result['experiences_processed']}")
                    print(f"    ⭐ Puncte câștigate: {skill_result['experience_points_gained']}")
                
            else:
                print("❌ Sistemul de învățare nu este disponibil")
                
        except Exception as e:
            print(f"❌ Eroare în antrenament: {e}")
    
    def do_memory(self, line):
        """Gestionează memoria sistemului
        Utilizare: memory [show|stats|search <termen>]
        """
        args = line.split()
        command = args[0] if args else 'stats'
        
        try:
            if command == 'stats':
                stats = self.ai_system.dense_storage.get_storage_stats()
                
                print("\n💾 STATISTICI MEMORIE:")
                print("─" * 40)
                print(f"📊 Total blocuri: {stats.total_blocks}")
                print(f"💽 Dimensiune: {stats.total_size_mb:.2f} MB")
                print(f"🗜️ Compresie: {stats.compression_ratio:.1f}x")
                print(f"⚡ Eficiență stocare: {stats.storage_efficiency:.1%}")
                print(f"🔧 Fragmentare: {stats.fragmentation_level:.1%}")
                
                # Top accesate
                if stats.access_frequency:
                    top_accessed = sorted(stats.access_frequency.items(), 
                                        key=lambda x: x[1], reverse=True)[:5]
                    print(f"\n🔥 TOP ACCESATE:")
                    for key, count in top_accessed:
                        print(f"  • {key}: {count} accesări")
                        
            elif command == 'show':
                # Afișează conținutul memoriei
                important_data = self.ai_system.dense_storage.export_important_data(0.5)
                
                print(f"\n💾 CONȚINUT MEMORIE ({len(important_data)} intrări importante):")
                print("─" * 60)
                
                for key, data in list(important_data.items())[:10]:  # Primele 10
                    data_preview = str(data)[:100] + "..." if len(str(data)) > 100 else str(data)
                    print(f"🔑 {key}:")
                    print(f"   {data_preview}")
                    print()
                    
            elif command == 'search' and len(args) > 1:
                search_term = ' '.join(args[1:])
                print(f"\n🔍 CĂUTARE: '{search_term}'")
                print("─" * 40)
                
                # Implementare simplă de căutare
                found_items = []
                important_data = self.ai_system.dense_storage.export_important_data(0.1)
                
                for key, data in important_data.items():
                    if search_term.lower() in str(data).lower() or search_term.lower() in key.lower():
                        found_items.append((key, data))
                
                if found_items:
                    print(f"✅ Găsite {len(found_items)} rezultate:")
                    for key, data in found_items[:5]:  # Primele 5
                        data_preview = str(data)[:150] + "..." if len(str(data)) > 150 else str(data)
                        print(f"🎯 {key}: {data_preview}")
                else:
                    print("❌ Nu s-au găsit rezultate")
            else:
                print("❌ Comandă invalidă. Utilizare: memory [show|stats|search <termen>]")
                
        except Exception as e:
            print(f"❌ Eroare în gestionarea memoriei: {e}")
    
    def do_neural(self, line):
        """Afișează starea rețelei neurale
        Utilizare: neural [module_name]
        """
        try:
            neural_summary = self.ai_system.cognitive_processor.get_neural_state_summary()
            
            if line.strip():
                # Afișează un modul specific
                module_name = line.strip()
                if module_name in neural_summary:
                    module = neural_summary[module_name]
                    print(f"\n🧠 MODULUL NEURAL: {module_name.upper()}")
                    print("─" * 50)
                    print(f"🔢 Total noduri: {module['total_nodes']}")
                    print(f"⚡ Noduri active: {module['active_nodes']}")
                    print(f"📊 Procent activare: {module['activation_percentage']:.1f}%")
                    print(f"📈 Activare medie: {module['average_activation']:.3f}")
                    print(f"⚙️ Eficiență: {module['efficiency']:.1f}")
                    print(f"🎯 Specializare: {module['specialization']}")
                    
                    # Bară de progres vizuală
                    activation_bar = "█" * int(module['activation_percentage'] / 10) + "░" * (10 - int(module['activation_percentage'] / 10))
                    print(f"📊 Activare: [{activation_bar}] {module['activation_percentage']:.1f}%")
                else:
                    print(f"❌ Modulul '{module_name}' nu există")
                    print(f"Module disponibile: {', '.join(neural_summary.keys())}")
            else:
                # Afișează toate modulele
                print("\n🧠 STAREA REȚELEI NEURALE:")
                print("─" * 70)
                
                total_nodes = sum(m['total_nodes'] for m in neural_summary.values())
                total_active = sum(m['active_nodes'] for m in neural_summary.values())
                
                print(f"📊 SUMAR GENERAL:")
                print(f"  🔢 Total noduri: {total_nodes}")
                print(f"  ⚡ Noduri active: {total_active}")
                print(f"  📈 Activare generală: {(total_active/total_nodes)*100:.1f}%")
                print()
                
                print(f"📋 DETALII MODULE:")
                for module_name, module in neural_summary.items():
                    activation_bar = "█" * int(module['activation_percentage'] / 10) + "░" * (10 - int(module['activation_percentage'] / 10))
                    print(f"  {module_name:20} [{activation_bar}] {module['activation_percentage']:5.1f}% ({module['active_nodes']:3}/{module['total_nodes']:3})")
                
        except Exception as e:
            print(f"❌ Eroare la afișarea stării neurale: {e}")
    
    def do_status(self, line):
        """Afișează statusul complet al sistemului"""
        print("\n" + "="*80)
        print("🧠 STATUSUL COMPLET AL SISTEMULUI AI")
        print("="*80)
        
        try:
            # Statistici generale
            stats = self.ai_system.get_system_statistics()
            
            print(f"⏱️  TIMP FUNCȚIONARE: {stats['uptime_hours']:.2f} ore")
            print(f"🔄 INTERACȚIUNI PROCESATE: {stats['interactions_processed']}")
            print(f"📚 SESIUNI ÎNVĂȚARE: {stats['learning_sessions']}")
            print(f"⚡ TIMP MEDIU PROCESARE: {stats['average_processing_time']:.3f}s")
            print(f"🧠 NIVEL CONȘTIINȚĂ: {stats['consciousness_level']:.2f}")
            print(f"🏥 SĂNĂTATE SISTEM: {stats['system_health'].upper()}")
            
            # Interfața curentă
            print(f"\n💻 SESIUNE INTERFAȚĂ:")
            print(f"  🕐 Durată sesiune: {(datetime.now() - self.session_start).total_seconds():.0f}s")
            print(f"  💬 Interacțiuni interfață: {self.interaction_count}")
            print(f"  📝 Istoric conversații: {len(self.conversation_history)}")
            
            # Module active
            active_modules = []
            if hasattr(self.ai_system, 'neural_core'):
                active_modules.append("🧠 Neural Core")
            if hasattr(self.ai_system, 'consciousness_engine'):
                active_modules.append("🌟 Consciousness Engine")
            if hasattr(self.ai_system, 'learning_system'):
                active_modules.append("📚 Learning System")
            if hasattr(self.ai_system, 'dense_storage'):
                active_modules.append("💾 Dense Storage")
            
            print(f"\n🔧 MODULE ACTIVE ({len(active_modules)}):")
            for module in active_modules:
                print(f"  ✅ {module}")
            
        except Exception as e:
            print(f"❌ Eroare la afișarea statusului: {e}")
        
        print("="*80)
    
    def do_history(self, line):
        """Afișează istoricul conversațiilor
        Utilizare: history [numărul de intrări]
        """
        try:
            limit = int(line) if line.strip().isdigit() else 10
            limit = min(limit, len(self.conversation_history))
            
            print(f"\n📝 ISTORIC CONVERSAȚII (ultimele {limit}):")
            print("─" * 70)
            
            for i, entry in enumerate(self.conversation_history[-limit:], 1):
                timestamp = entry['timestamp'].strftime("%H:%M:%S")
                input_preview = entry['input'][:50] + "..." if len(entry['input']) > 50 else entry['input']
                output_preview = entry['output'][:50] + "..." if len(entry['output']) > 50 else entry['output']
                
                print(f"{i:2}. [{timestamp}] ⏱️ {entry['processing_time']:.3f}s")
                print(f"    👤 {input_preview}")
                print(f"    🤖 {output_preview}")
                print()
                
        except Exception as e:
            print(f"❌ Eroare la afișarea istoricului: {e}")
    
    def do_quit(self, line):
        """Ieșire din interfață"""
        print("\n🔄 Oprire interfață...")
        
        # Salvează sesiunea
        session_data = {
            'session_start': self.session_start.isoformat(),
            'session_end': datetime.now().isoformat(),
            'interaction_count': self.interaction_count,
            'conversation_history': [
                {
                    'timestamp': entry['timestamp'].isoformat(),
                    'input': entry['input'],
                    'output': entry['output'],
                    'processing_time': entry['processing_time']
                }
                for entry in self.conversation_history
            ]
        }
        
        try:
            with open('session_history.json', 'w', encoding='utf-8') as f:
                json.dump(session_data, f, indent=2, ensure_ascii=False)
            print("💾 Sesiune salvată în session_history.json")
        except Exception as e:
            print(f"⚠️ Nu s-a putut salva sesiunea: {e}")
        
        print("👋 La revedere!")
        return True
    
    def do_help(self, line):
        """Afișează ajutorul pentru comenzi"""
        if line:
            super().do_help(line)
        else:
            print("\n🆘 COMENZI DISPONIBILE:")
            print("─" * 50)
            print("💬 talk <mesaj>           - Conversație cu AI-ul")
            print("🌟 consciousness          - Raport conștiință")
            print("📚 learn <mesaj> --feedback <scor> - Învățare cu feedback")
            print("🎯 skills [skill_name]    - Statusul abilităților")
            print("🚀 train [skills]         - Sesiune intensivă antrenament")
            print("💾 memory [show|stats|search] - Gestionare memorie")
            print("🧠 neural [module]        - Starea rețelei neurale")
            print("📊 status                 - Status complet sistem")
            print("📝 history [n]            - Istoric conversații")
            print("🆘 help [comandă]         - Ajutor pentru comenzi")
            print("🚪 quit                   - Ieșire")
            print()
            print("💡 Exemplu: talk Cum funcționează conștiința ta?")
            print("💡 Exemplu: learn Explică fizica cuantică --feedback 0.9")

def main():
    """Funcția principală pentru interfață"""
    try:
        # Importă și inițializează sistemul AI
        from true_ai_system import TrueAISystem
        from continuous_learning_system import ContinuousLearningSystem
        
        print("🚀 Inițializare sistem AI complet...")
        
        # Creează sistemul AI
        ai_system = TrueAISystem()
        
        # Adaugă sistemul de învățare
        ai_system.learning_system = ContinuousLearningSystem()
        
        print("✅ Sistem AI complet inițializat!")
        
        # Pornește interfața
        interface = AIInterface(ai_system)
        interface.cmdloop()
        
        # Oprește sistemul la ieșire
        if hasattr(ai_system, 'learning_system'):
            ai_system.learning_system.shutdown()
        ai_system.shutdown()
        
    except KeyboardInterrupt:
        print("\n\n🛑 Întrerupere de la utilizator")
    except Exception as e:
        print(f"\n❌ Eroare critică: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
