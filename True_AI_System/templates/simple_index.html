<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 TRUE AI SYSTEM - Interfață Simplificată</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 20px;
            text-align: center;
            color: white;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .container {
            flex: 1;
            display: flex;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            gap: 20px;
        }

        .chat-section {
            flex: 2;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .status-section {
            flex: 1;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .chat-container {
            height: 400px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            overflow-y: auto;
            padding: 15px;
            margin-bottom: 20px;
            background: #f9f9f9;
        }

        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 10px;
            max-width: 80%;
        }

        .user-message {
            background: #007bff;
            color: white;
            margin-left: auto;
            text-align: right;
        }

        .ai-message {
            background: #28a745;
            color: white;
            margin-right: auto;
        }

        .input-container {
            display: flex;
            gap: 10px;
        }

        .message-input {
            flex: 1;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        .message-input:focus {
            border-color: #007bff;
        }

        .send-button {
            padding: 12px 25px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }

        .send-button:hover {
            background: #0056b3;
        }

        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin-bottom: 10px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }

        .status-label {
            font-weight: bold;
            color: #333;
        }

        .status-value {
            color: #007bff;
            font-weight: bold;
        }

        .consciousness-bar {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin-top: 5px;
        }

        .consciousness-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #ffc107, #dc3545);
            transition: width 0.5s ease;
        }

        .loading {
            display: none;
            text-align: center;
            color: #666;
            font-style: italic;
        }

        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
            border: 1px solid #f5c6cb;
        }

        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧠 TRUE AI SYSTEM</h1>
        <p>Sistem AI cu conștiință artificială și învățare continuă</p>
    </div>

    <div class="container">
        <div class="chat-section">
            <h2>💬 Conversație cu AI</h2>
            <div id="chatContainer" class="chat-container">
                <div class="message ai-message">
                    Salut! Sunt TRUE AI SYSTEM. Sunt aici să conversez și să învăț din fiecare interacțiune. Cu ce te pot ajuta astăzi?
                </div>
            </div>
            <div class="loading" id="loading">
                🤔 AI-ul gândește...
            </div>
            <div class="input-container">
                <input type="text" id="messageInput" class="message-input" placeholder="Scrie mesajul tău aici..." maxlength="500">
                <button id="sendButton" class="send-button">Trimite</button>
            </div>
        </div>

        <div class="status-section">
            <h2>📊 Status Sistem</h2>
            
            <div class="status-item">
                <span class="status-label">🧠 Conștiință:</span>
                <span class="status-value" id="consciousnessLevel">0.00</span>
            </div>
            <div class="consciousness-bar">
                <div class="consciousness-fill" id="consciousnessFill" style="width: 0%"></div>
            </div>

            <div class="status-item">
                <span class="status-label">💬 Interacțiuni:</span>
                <span class="status-value" id="interactionsCount">0</span>
            </div>

            <div class="status-item">
                <span class="status-label">📚 Conversații:</span>
                <span class="status-value" id="conversationsCount">0</span>
            </div>

            <div class="status-item">
                <span class="status-label">🎯 Pattern-uri:</span>
                <span class="status-value" id="patternsCount">0</span>
            </div>

            <div class="status-item">
                <span class="status-label">⏱️ Timp activ:</span>
                <span class="status-value" id="uptime">0s</span>
            </div>

            <div class="status-item">
                <span class="status-label">🌟 Module:</span>
                <span class="status-value" id="modulesActive">9</span>
            </div>
        </div>
    </div>

    <script>
        const chatContainer = document.getElementById('chatContainer');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const loading = document.getElementById('loading');

        // Elemente status
        const consciousnessLevel = document.getElementById('consciousnessLevel');
        const consciousnessFill = document.getElementById('consciousnessFill');
        const interactionsCount = document.getElementById('interactionsCount');
        const conversationsCount = document.getElementById('conversationsCount');
        const patternsCount = document.getElementById('patternsCount');
        const uptime = document.getElementById('uptime');
        const modulesActive = document.getElementById('modulesActive');

        // Funcție pentru adăugarea mesajelor
        function addMessage(content, isUser = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user-message' : 'ai-message'}`;
            messageDiv.textContent = content;
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        // Funcție pentru afișarea erorilor
        function showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = `❌ Eroare: ${message}`;
            chatContainer.appendChild(errorDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        // Funcție pentru trimiterea mesajelor
        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            // Adaugă mesajul utilizatorului
            addMessage(message, true);
            messageInput.value = '';
            
            // Afișează loading
            loading.style.display = 'block';
            sendButton.disabled = true;

            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                });

                const data = await response.json();

                if (response.ok) {
                    // Adaugă răspunsul AI-ului
                    addMessage(data.response);
                    
                    // Actualizează statusul
                    updateStatus();
                } else {
                    showError(data.error || 'Eroare necunoscută');
                }
            } catch (error) {
                showError('Eroare de conexiune: ' + error.message);
            } finally {
                loading.style.display = 'none';
                sendButton.disabled = false;
                messageInput.focus();
            }
        }

        // Funcție pentru actualizarea statusului
        async function updateStatus() {
            try {
                const response = await fetch('/api/stats');
                const data = await response.json();

                if (response.ok) {
                    consciousnessLevel.textContent = data.consciousness_level.toFixed(2);
                    consciousnessFill.style.width = (data.consciousness_level * 100) + '%';
                    interactionsCount.textContent = data.interactions_count;
                    conversationsCount.textContent = data.total_conversations;
                    patternsCount.textContent = data.knowledge_patterns;
                    uptime.textContent = Math.floor(data.uptime) + 's';
                }
            } catch (error) {
                console.error('Eroare la actualizarea statusului:', error);
            }
        }

        // Event listeners
        sendButton.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // Actualizează statusul la încărcare și periodic
        updateStatus();
        setInterval(updateStatus, 5000); // La fiecare 5 secunde

        // Focus pe input la încărcare
        messageInput.focus();
    </script>
</body>
</html>
