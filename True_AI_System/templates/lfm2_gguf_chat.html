<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Integrated AI System - Dashboard Complet</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 20px;
            text-align: center;
            color: white;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .download-progress {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 15px;
            margin: 15px auto;
            max-width: 600px;
            display: none;
        }

        .progress-bar {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 20px;
            height: 20px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            background: linear-gradient(90deg, #00b894, #00cec9);
            height: 100%;
            border-radius: 20px;
            transition: width 0.3s ease;
            width: 0%;
        }

        .progress-text {
            text-align: center;
            font-weight: bold;
            margin-top: 10px;
        }

        .container {
            display: grid;
            grid-template-columns: 1fr 350px;
            gap: 20px;
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
            height: calc(100vh - 120px);
        }

        .chat-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            display: flex;
            flex-direction: column;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .status-panel, .model-panel, .learning-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .panel-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 5px;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .status-label {
            font-weight: 500;
            color: #555;
        }

        .status-value {
            font-weight: bold;
            color: #667eea;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }

        .status-online {
            background: #00b894;
            animation: pulse 2s infinite;
        }

        .status-loading {
            background: #fdcb6e;
            animation: pulse 2s infinite;
        }

        .status-offline {
            background: #e17055;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            margin-bottom: 15px;
            background: #f9f9f9;
        }

        .message {
            margin-bottom: 15px;
            padding: 12px 15px;
            border-radius: 15px;
            max-width: 85%;
            word-wrap: break-word;
            position: relative;
        }

        .user-message {
            background: #667eea;
            color: white;
            margin-left: auto;
            border-bottom-right-radius: 5px;
        }

        .ai-message {
            background: #00b894;
            color: white;
            margin-right: auto;
            border-bottom-left-radius: 5px;
        }

        .message-meta {
            font-size: 0.8em;
            opacity: 0.8;
            margin-top: 5px;
        }

        .input-area {
            display: flex;
            gap: 10px;
        }

        .message-input {
            flex: 1;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        .message-input:focus {
            border-color: #667eea;
        }

        .send-button {
            padding: 15px 25px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: background 0.3s;
        }

        .send-button:hover {
            background: #5a67d8;
        }

        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .loading {
            display: none;
            text-align: center;
            color: #666;
            font-style: italic;
            margin-bottom: 10px;
        }

        .model-config {
            font-size: 0.9em;
            line-height: 1.6;
        }

        .config-item {
            display: flex;
            justify-content: space-between;
            padding: 4px 0;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 3px 0;
            color: #666;
        }

        .feature-list li:before {
            content: "✓ ";
            color: #00b894;
            font-weight: bold;
        }

        .metric-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 10px;
        }

        .metric-item {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .metric-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #667eea;
        }

        .metric-label {
            font-size: 0.8em;
            color: #666;
            margin-top: 5px;
        }

        @media (max-width: 1024px) {
            .container {
                grid-template-columns: 1fr;
                grid-template-rows: auto 1fr;
            }
            
            .sidebar {
                flex-direction: row;
                overflow-x: auto;
            }
            
            .status-panel, .model-panel, .learning-panel {
                min-width: 300px;
            }
        }

        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
            z-index: 1000;
        }

        .connected {
            background: #00b894;
            color: white;
        }

        .disconnected {
            background: #e17055;
            color: white;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧠 LFM2-1.2B GGUF</h1>
        <p>Sistem AI Real cu Model Liquid AI • Inferență Rapidă • Învățare Continuă</p>
        
        <div class="download-progress" id="downloadProgress">
            <div>📥 Descărcare model LFM2-1.2B...</div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-text" id="progressText">0% - 0 MB / 731 MB</div>
        </div>
    </div>

    <div class="connection-status" id="connectionStatus">
        🔄 Conectare...
    </div>

    <div class="container">
        <div class="chat-section">
            <div class="chat-messages" id="chatMessages">
                <div class="message ai-message">
                    Salut! Sunt alimentat de modelul LFM2-1.2B real de la Liquid AI în format GGUF. 
                    Ofer conversații naturale și inteligente cu învățare continuă. 
                    Cum te pot ajuta astăzi? 🚀
                </div>
            </div>

            <div class="loading" id="loading">
                🧠 LFM2 gândește...
            </div>

            <div class="input-area">
                <input type="text" id="messageInput" class="message-input" 
                       placeholder="Scrie mesajul tău aici - LFM2 va răspunde natural..." maxlength="1000">
                <button id="sendButton" class="send-button">Trimite</button>
            </div>
        </div>

        <div class="sidebar">
            <div class="status-panel">
                <div class="panel-title">🔧 Status Sistem</div>
                <div class="status-item">
                    <span class="status-label">Model Status</span>
                    <span class="status-value" id="modelStatus">Încărcare...</span>
                    <div class="status-indicator status-loading" id="modelIndicator"></div>
                </div>
                <div class="status-item">
                    <span class="status-label">Conversații</span>
                    <span class="status-value" id="totalConversations">0</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Învățare</span>
                    <span class="status-value" id="learningActive">Inactivă</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Antrenament</span>
                    <span class="status-value" id="trainingStatus">Inactiv</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Uptime</span>
                    <span class="status-value" id="uptime">0s</span>
                </div>
            </div>

            <div class="model-panel">
                <div class="panel-title">🤖 Model LFM2-1.2B</div>
                <div class="model-config">
                    <div class="config-item">
                        <span>Model:</span>
                        <span>LFM2-1.2B</span>
                    </div>
                    <div class="config-item">
                        <span>Format:</span>
                        <span>GGUF (Q4_K_M)</span>
                    </div>
                    <div class="config-item">
                        <span>Parametri:</span>
                        <span>1.17B</span>
                    </div>
                    <div class="config-item">
                        <span>Context:</span>
                        <span>32K tokens</span>
                    </div>
                    <div class="config-item">
                        <span>Engine:</span>
                        <span>llama.cpp</span>
                    </div>
                </div>
                
                <div style="margin-top: 15px;">
                    <strong>Caracteristici:</strong>
                    <ul class="feature-list">
                        <li>Inferență rapidă CPU</li>
                        <li>Suport GPU</li>
                        <li>Memorie eficientă</li>
                        <li>Context lung</li>
                        <li>Multilingv</li>
                    </ul>
                </div>
            </div>

            <div class="learning-panel">
                <div class="panel-title">🎓 Învățare</div>
                <div class="metric-grid">
                    <div class="metric-item">
                        <div class="metric-value" id="avgQuality">0.00</div>
                        <div class="metric-label">Calitate Medie</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="trainingSessions">0</div>
                        <div class="metric-label">Sesiuni Antrenament</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="highQualityConvs">0</div>
                        <div class="metric-label">Conversații Calitate</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="avgProcessingTime">0.0s</div>
                        <div class="metric-label">Timp Procesare</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Socket.IO
        const socket = io();
        
        // DOM elements
        const chatMessages = document.getElementById('chatMessages');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const loading = document.getElementById('loading');
        const connectionStatus = document.getElementById('connectionStatus');
        const downloadProgress = document.getElementById('downloadProgress');
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        
        // Status elements
        const modelStatus = document.getElementById('modelStatus');
        const modelIndicator = document.getElementById('modelIndicator');
        const totalConversations = document.getElementById('totalConversations');
        const learningActive = document.getElementById('learningActive');
        const trainingStatus = document.getElementById('trainingStatus');
        const uptime = document.getElementById('uptime');
        
        // Learning elements
        const avgQuality = document.getElementById('avgQuality');
        const trainingSessions = document.getElementById('trainingSessions');
        const highQualityConvs = document.getElementById('highQualityConvs');
        const avgProcessingTime = document.getElementById('avgProcessingTime');
        
        // Socket event handlers
        socket.on('connect', function() {
            connectionStatus.textContent = '🟢 Conectat';
            connectionStatus.className = 'connection-status connected';
            console.log('Conectat la server');
        });
        
        socket.on('disconnect', function() {
            connectionStatus.textContent = '🔴 Deconectat';
            connectionStatus.className = 'connection-status disconnected';
            console.log('Deconectat de la server');
        });
        
        socket.on('status_update', function(status) {
            updateSystemStatus(status);
        });
        
        socket.on('download_progress_update', function(data) {
            updateDownloadProgress(data);
        });
        
        socket.on('new_message', function(data) {
            console.log('Mesaj nou primit:', data);
        });
        
        // Update system status
        function updateSystemStatus(status) {
            // Model status
            if (status.model_loaded) {
                modelStatus.textContent = 'Activ';
                modelIndicator.className = 'status-indicator status-online';
                downloadProgress.style.display = 'none';
            } else {
                modelStatus.textContent = 'Încărcare';
                modelIndicator.className = 'status-indicator status-loading';
                
                // Show download progress if available
                if (status.loading_progress !== undefined) {
                    downloadProgress.style.display = 'block';
                    updateDownloadProgress({
                        progress: status.loading_progress,
                        downloaded_mb: status.downloaded_mb || 0,
                        total_mb: 731,
                        is_complete: false
                    });
                }
            }
            
            // Other status items
            totalConversations.textContent = status.total_conversations || 0;
            learningActive.textContent = status.learning_active ? 'Activă' : 'Inactivă';
            trainingStatus.textContent = status.training_in_progress ? 'Activ' : 'Inactiv';
            
            // Format uptime
            const uptimeSeconds = status.uptime_seconds || 0;
            const hours = Math.floor(uptimeSeconds / 3600);
            const minutes = Math.floor((uptimeSeconds % 3600) / 60);
            const seconds = Math.floor(uptimeSeconds % 60);
            uptime.textContent = `${hours}h ${minutes}m ${seconds}s`;
        }
        
        // Update download progress
        function updateDownloadProgress(data) {
            if (data.progress !== undefined) {
                progressFill.style.width = data.progress + '%';
                progressText.textContent = `${data.progress.toFixed(1)}% - ${data.downloaded_mb.toFixed(1)} MB / ${data.total_mb} MB`;
                
                if (data.is_complete || data.progress >= 95) {
                    downloadProgress.style.display = 'none';
                }
            }
        }
        
        // Add message to chat
        function addMessage(content, isUser = false, meta = null) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user-message' : 'ai-message'}`;
            
            const contentDiv = document.createElement('div');
            contentDiv.textContent = content;
            messageDiv.appendChild(contentDiv);
            
            if (meta) {
                const metaDiv = document.createElement('div');
                metaDiv.className = 'message-meta';
                metaDiv.textContent = meta;
                messageDiv.appendChild(metaDiv);
            }
            
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        // Send message
        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;
            
            // Add user message
            addMessage(message, true);
            messageInput.value = '';
            
            // Show loading
            loading.style.display = 'block';
            sendButton.disabled = true;
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    // Add AI response
                    const meta = `⏱️ ${data.processing_time?.toFixed(3)}s | 📊 Calitate: ${data.quality_score?.toFixed(2)} | 🎓 Învățare: ${data.learning_value?.toFixed(2)}`;
                    addMessage(data.response, false, meta);
                    
                    // Update learning stats
                    updateLearningStats();
                } else {
                    addMessage('❌ Eroare: ' + (data.error || 'Ceva nu a mers bine'), false);
                }
            } catch (error) {
                addMessage('❌ Eroare de conexiune: ' + error.message, false);
            } finally {
                loading.style.display = 'none';
                sendButton.disabled = false;
                messageInput.focus();
            }
        }
        
        // Update learning stats
        async function updateLearningStats() {
            try {
                const response = await fetch('/api/learning/stats');
                const data = await response.json();
                
                if (response.ok) {
                    avgQuality.textContent = data.average_quality?.toFixed(2) || '0.00';
                    trainingSessions.textContent = data.training_sessions || 0;
                    highQualityConvs.textContent = data.high_quality_conversations || 0;
                    avgProcessingTime.textContent = (data.average_processing_time?.toFixed(1) || '0.0') + 's';
                }
            } catch (error) {
                console.error('Eroare actualizare statistici învățare:', error);
            }
        }
        
        // Event listeners
        sendButton.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            messageInput.focus();
            updateLearningStats();
            
            // Request download progress periodically
            setInterval(() => {
                socket.emit('request_download_progress');
            }, 5000);
            
            // Update learning stats periodically
            setInterval(updateLearningStats, 30000);
        });
    </script>
</body>
</html>
