<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 Advanced AI System - LFM2-1.2B</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 20px;
            text-align: center;
            color: white;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .container {
            display: grid;
            grid-template-columns: 1fr 350px;
            gap: 20px;
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
            height: calc(100vh - 120px);
        }

        .chat-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            display: flex;
            flex-direction: column;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .status-panel, .analytics-panel, .model-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .panel-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 5px;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .status-label {
            font-weight: 500;
            color: #555;
        }

        .status-value {
            font-weight: bold;
            color: #667eea;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }

        .status-online {
            background: #00b894;
            animation: pulse 2s infinite;
        }

        .status-loading {
            background: #fdcb6e;
            animation: pulse 2s infinite;
        }

        .status-offline {
            background: #e17055;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            margin-bottom: 15px;
            background: #f9f9f9;
        }

        .message {
            margin-bottom: 15px;
            padding: 12px 15px;
            border-radius: 15px;
            max-width: 85%;
            word-wrap: break-word;
            position: relative;
        }

        .user-message {
            background: #667eea;
            color: white;
            margin-left: auto;
            border-bottom-right-radius: 5px;
        }

        .ai-message {
            background: #00b894;
            color: white;
            margin-right: auto;
            border-bottom-left-radius: 5px;
        }

        .message-meta {
            font-size: 0.8em;
            opacity: 0.8;
            margin-top: 5px;
        }

        .input-area {
            display: flex;
            gap: 10px;
        }

        .message-input {
            flex: 1;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        .message-input:focus {
            border-color: #667eea;
        }

        .send-button {
            padding: 15px 25px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: background 0.3s;
        }

        .send-button:hover {
            background: #5a67d8;
        }

        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .loading {
            display: none;
            text-align: center;
            color: #666;
            font-style: italic;
            margin-bottom: 10px;
        }

        .chart-container {
            height: 150px;
            margin-top: 10px;
        }

        .metric-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 10px;
        }

        .metric-item {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .metric-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #667eea;
        }

        .metric-label {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }

        .model-config {
            font-size: 0.9em;
            line-height: 1.6;
        }

        .config-item {
            display: flex;
            justify-content: space-between;
            padding: 4px 0;
        }

        @media (max-width: 1024px) {
            .container {
                grid-template-columns: 1fr;
                grid-template-rows: auto 1fr;
            }
            
            .sidebar {
                flex-direction: row;
                overflow-x: auto;
            }
            
            .status-panel, .analytics-panel, .model-panel {
                min-width: 300px;
            }
        }

        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
            z-index: 1000;
        }

        .connected {
            background: #00b894;
            color: white;
        }

        .disconnected {
            background: #e17055;
            color: white;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧠 Advanced AI System</h1>
        <p>Powered by LFM2-1.2B with Continuous Learning & Real-time Analytics</p>
    </div>

    <div class="connection-status" id="connectionStatus">
        🔄 Connecting...
    </div>

    <div class="container">
        <div class="chat-section">
            <div class="chat-messages" id="chatMessages">
                <div class="message ai-message">
                    Welcome to the Advanced AI System! I'm powered by the LFM2-1.2B model with continuous learning capabilities. 
                    I can engage in sophisticated conversations and learn from our interactions. How can I help you today? 🚀
                </div>
            </div>

            <div class="loading" id="loading">
                🧠 AI is thinking...
            </div>

            <div class="input-area">
                <input type="text" id="messageInput" class="message-input" 
                       placeholder="Ask me anything - I'll learn and improve from our conversation..." maxlength="1000">
                <button id="sendButton" class="send-button">Send</button>
            </div>
        </div>

        <div class="sidebar">
            <div class="status-panel">
                <div class="panel-title">🔧 System Status</div>
                <div class="status-item">
                    <span class="status-label">Model Status</span>
                    <span class="status-value" id="modelStatus">Loading...</span>
                    <div class="status-indicator status-loading" id="modelIndicator"></div>
                </div>
                <div class="status-item">
                    <span class="status-label">Conversations</span>
                    <span class="status-value" id="totalConversations">0</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Learning Active</span>
                    <span class="status-value" id="learningActive">Yes</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Training</span>
                    <span class="status-value" id="trainingStatus">Idle</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Uptime</span>
                    <span class="status-value" id="uptime">0s</span>
                </div>
            </div>

            <div class="analytics-panel">
                <div class="panel-title">📊 Analytics</div>
                <div class="metric-grid">
                    <div class="metric-item">
                        <div class="metric-value" id="avgQuality">0.00</div>
                        <div class="metric-label">Avg Quality</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="avgLearning">0.00</div>
                        <div class="metric-label">Learning Value</div>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="qualityChart"></canvas>
                </div>
            </div>

            <div class="model-panel">
                <div class="panel-title">🤖 Model Info</div>
                <div class="model-config">
                    <div class="config-item">
                        <span>Model:</span>
                        <span id="modelName">LFM2-1.2B</span>
                    </div>
                    <div class="config-item">
                        <span>Device:</span>
                        <span id="modelDevice">Unknown</span>
                    </div>
                    <div class="config-item">
                        <span>Temperature:</span>
                        <span id="modelTemp">0.3</span>
                    </div>
                    <div class="config-item">
                        <span>Context:</span>
                        <span id="contextLength">32K</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Socket.IO
        const socket = io();
        
        // DOM elements
        const chatMessages = document.getElementById('chatMessages');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const loading = document.getElementById('loading');
        const connectionStatus = document.getElementById('connectionStatus');
        
        // Status elements
        const modelStatus = document.getElementById('modelStatus');
        const modelIndicator = document.getElementById('modelIndicator');
        const totalConversations = document.getElementById('totalConversations');
        const learningActive = document.getElementById('learningActive');
        const trainingStatus = document.getElementById('trainingStatus');
        const uptime = document.getElementById('uptime');
        
        // Analytics elements
        const avgQuality = document.getElementById('avgQuality');
        const avgLearning = document.getElementById('avgLearning');
        
        // Model info elements
        const modelName = document.getElementById('modelName');
        const modelDevice = document.getElementById('modelDevice');
        const modelTemp = document.getElementById('modelTemp');
        const contextLength = document.getElementById('contextLength');
        
        // Chart
        let qualityChart;
        
        // Initialize chart
        function initChart() {
            const ctx = document.getElementById('qualityChart').getContext('2d');
            qualityChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Quality Score',
                        data: [],
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 1,
                            display: false
                        },
                        x: {
                            display: false
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }
        
        // Socket event handlers
        socket.on('connect', function() {
            connectionStatus.textContent = '🟢 Connected';
            connectionStatus.className = 'connection-status connected';
            console.log('Connected to server');
        });
        
        socket.on('disconnect', function() {
            connectionStatus.textContent = '🔴 Disconnected';
            connectionStatus.className = 'connection-status disconnected';
            console.log('Disconnected from server');
        });
        
        socket.on('status_update', function(status) {
            updateSystemStatus(status);
        });
        
        socket.on('new_message', function(data) {
            // Message already added by sendMessage function
            console.log('New message broadcast:', data);
        });
        
        // Update system status
        function updateSystemStatus(status) {
            // Model status
            if (status.model_loaded) {
                modelStatus.textContent = 'Ready';
                modelIndicator.className = 'status-indicator status-online';
            } else {
                modelStatus.textContent = 'Loading';
                modelIndicator.className = 'status-indicator status-loading';
            }
            
            // Other status items
            totalConversations.textContent = status.total_conversations || 0;
            learningActive.textContent = status.learning_active ? 'Yes' : 'No';
            trainingStatus.textContent = status.training_in_progress ? 'Active' : 'Idle';
            
            // Format uptime
            const uptimeSeconds = status.uptime_seconds || 0;
            const hours = Math.floor(uptimeSeconds / 3600);
            const minutes = Math.floor((uptimeSeconds % 3600) / 60);
            const seconds = Math.floor(uptimeSeconds % 60);
            uptime.textContent = `${hours}h ${minutes}m ${seconds}s`;
            
            // Device info
            if (status.device) {
                modelDevice.textContent = status.device;
            }
        }
        
        // Add message to chat
        function addMessage(content, isUser = false, meta = null) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user-message' : 'ai-message'}`;
            
            const contentDiv = document.createElement('div');
            contentDiv.textContent = content;
            messageDiv.appendChild(contentDiv);
            
            if (meta) {
                const metaDiv = document.createElement('div');
                metaDiv.className = 'message-meta';
                metaDiv.textContent = meta;
                messageDiv.appendChild(metaDiv);
            }
            
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        // Send message
        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;
            
            // Add user message
            addMessage(message, true);
            messageInput.value = '';
            
            // Show loading
            loading.style.display = 'block';
            sendButton.disabled = true;
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    // Add AI response
                    const meta = `⏱️ ${data.processing_time?.toFixed(3)}s | 📊 Quality: ${data.quality_score?.toFixed(2)} | 🎓 Learning: ${data.learning_value?.toFixed(2)}`;
                    addMessage(data.response, false, meta);
                    
                    // Update analytics
                    updateAnalytics();
                } else {
                    addMessage('❌ Error: ' + (data.error || 'Something went wrong'), false);
                }
            } catch (error) {
                addMessage('❌ Connection error: ' + error.message, false);
            } finally {
                loading.style.display = 'none';
                sendButton.disabled = false;
                messageInput.focus();
            }
        }
        
        // Update analytics
        async function updateAnalytics() {
            try {
                const response = await fetch('/api/analytics');
                const data = await response.json();
                
                if (response.ok) {
                    avgQuality.textContent = data.average_quality?.toFixed(2) || '0.00';
                    avgLearning.textContent = data.average_learning_value?.toFixed(2) || '0.00';
                    
                    // Update chart
                    if (qualityChart && data.quality_trend) {
                        qualityChart.data.labels = data.quality_trend.map((_, i) => i + 1);
                        qualityChart.data.datasets[0].data = data.quality_trend;
                        qualityChart.update();
                    }
                }
            } catch (error) {
                console.error('Analytics update error:', error);
            }
        }
        
        // Load model info
        async function loadModelInfo() {
            try {
                const response = await fetch('/api/model/info');
                const data = await response.json();
                
                if (response.ok) {
                    modelName.textContent = data.model_name?.split('/').pop() || 'LFM2-1.2B';
                    modelDevice.textContent = data.device || 'Unknown';
                    modelTemp.textContent = data.temperature || '0.3';
                    contextLength.textContent = `${(data.context_length / 1000).toFixed(0)}K` || '32K';
                }
            } catch (error) {
                console.error('Model info load error:', error);
            }
        }
        
        // Event listeners
        sendButton.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            initChart();
            loadModelInfo();
            updateAnalytics();
            messageInput.focus();
            
            // Periodic updates
            setInterval(updateAnalytics, 30000); // Every 30 seconds
        });
    </script>
</body>
</html>
