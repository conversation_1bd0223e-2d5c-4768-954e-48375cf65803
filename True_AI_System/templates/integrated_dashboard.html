<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Integrated AI System - Dashboard Complet</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 20px;
            text-align: center;
            color: white;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .system-status {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .status-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #00b894;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .main-container {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 20px;
            padding: 20px;
            max-width: 1600px;
            margin: 0 auto;
            height: calc(100vh - 140px);
        }

        .chat-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            display: flex;
            flex-direction: column;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 15px;
            overflow-y: auto;
        }

        .panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .panel-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 5px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            margin-bottom: 15px;
            background: #f9f9f9;
        }

        .message {
            margin-bottom: 15px;
            padding: 12px 15px;
            border-radius: 15px;
            max-width: 85%;
            word-wrap: break-word;
            position: relative;
        }

        .user-message {
            background: #667eea;
            color: white;
            margin-left: auto;
            border-bottom-right-radius: 5px;
        }

        .ai-message {
            background: #00b894;
            color: white;
            margin-right: auto;
            border-bottom-left-radius: 5px;
        }

        .message-meta {
            font-size: 0.8em;
            opacity: 0.8;
            margin-top: 5px;
        }

        .input-area {
            display: flex;
            gap: 10px;
        }

        .message-input {
            flex: 1;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        .message-input:focus {
            border-color: #667eea;
        }

        .send-button {
            padding: 15px 25px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: background 0.3s;
        }

        .send-button:hover {
            background: #5a67d8;
        }

        .metric-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 10px;
        }

        .metric-item {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .metric-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #667eea;
        }

        .metric-label {
            font-size: 0.8em;
            color: #666;
            margin-top: 5px;
        }

        .control-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .control-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9em;
            font-weight: bold;
            transition: all 0.3s;
        }

        .control-btn.start {
            background: #00b894;
            color: white;
        }

        .control-btn.stop {
            background: #e17055;
            color: white;
        }

        .control-btn.info {
            background: #0984e3;
            color: white;
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00b894, #00cec9);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .component-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .component-status:last-child {
            border-bottom: none;
        }

        .component-name {
            font-weight: 500;
            color: #555;
        }

        .component-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #00b894;
        }

        .component-indicator.stopped {
            background: #e17055;
        }

        .component-indicator.loading {
            background: #fdcb6e;
            animation: pulse 2s infinite;
        }

        .chart-container {
            height: 200px;
            margin-top: 15px;
        }

        .loading {
            display: none;
            text-align: center;
            color: #666;
            font-style: italic;
            margin-bottom: 10px;
        }

        @media (max-width: 1200px) {
            .main-container {
                grid-template-columns: 1fr;
                grid-template-rows: 1fr auto;
            }
            
            .sidebar {
                flex-direction: row;
                overflow-x: auto;
                gap: 10px;
            }
            
            .panel {
                min-width: 300px;
            }
        }

        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
            z-index: 1000;
        }

        .connected {
            background: #00b894;
            color: white;
        }

        .disconnected {
            background: #e17055;
            color: white;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Integrated AI System</h1>
        <p>Sistem AI Complet cu LFM2-1.2B • Antrenament Continuu • Învățare 24/7 • Dashboard Avansat</p>
        
        <div class="system-status">
            <div class="status-badge">
                <div class="status-indicator" id="modelIndicator"></div>
                <span id="modelStatus">Încărcare...</span>
            </div>
            <div class="status-badge">
                <div class="status-indicator" id="trainingIndicator"></div>
                <span id="trainingStatus">Antrenament</span>
            </div>
            <div class="status-badge">
                <div class="status-indicator" id="learningIndicator"></div>
                <span id="learningStatus">Învățare</span>
            </div>
            <div class="status-badge">
                <div class="status-indicator" id="systemIndicator"></div>
                <span id="systemStatus">Sistem</span>
            </div>
        </div>
    </div>

    <div class="connection-status" id="connectionStatus">
        🔄 Conectare...
    </div>

    <div class="main-container">
        <div class="chat-section">
            <div class="chat-messages" id="chatMessages">
                <div class="message ai-message">
                    🚀 Salut! Sunt sistemul AI integrat cu LFM2-1.2B real! 
                    Am antrenament continuu, învățare 24/7, procesare cognitivă și motor de conștiință activ.
                    Cum te pot ajuta astăzi? 🧠✨
                </div>
            </div>

            <div class="loading" id="loading">
                🧠 Sistemul AI procesează cu toate componentele...
            </div>

            <div class="input-area">
                <input type="text" id="messageInput" class="message-input" 
                       placeholder="Scrie mesajul tău aici - sistemul complet va răspunde..." maxlength="1000">
                <button id="sendButton" class="send-button">Trimite</button>
            </div>
        </div>

        <div class="sidebar">
            <div class="panel">
                <div class="panel-title">
                    🔧 Control Sistem
                </div>
                <div class="control-buttons">
                    <button class="control-btn start" onclick="executeCommand('start_training')">▶️ Start Antrenament</button>
                    <button class="control-btn stop" onclick="executeCommand('stop_training')">⏹️ Stop Antrenament</button>
                    <button class="control-btn start" onclick="executeCommand('start_learning')">📚 Start Învățare</button>
                    <button class="control-btn stop" onclick="executeCommand('stop_learning')">📚 Stop Învățare</button>
                    <button class="control-btn info" onclick="executeCommand('save_data')">💾 Salvează Date</button>
                </div>
            </div>

            <div class="panel">
                <div class="panel-title">
                    📊 Componente Sistem
                </div>
                <div id="componentsStatus">
                    <div class="component-status">
                        <span class="component-name">Model LFM2-1.2B</span>
                        <div class="component-indicator loading" id="lfm2Indicator"></div>
                    </div>
                    <div class="component-status">
                        <span class="component-name">Antrenament Continuu</span>
                        <div class="component-indicator loading" id="trainingEngineIndicator"></div>
                    </div>
                    <div class="component-status">
                        <span class="component-name">Învățare Continuă</span>
                        <div class="component-indicator loading" id="learningSystemIndicator"></div>
                    </div>
                    <div class="component-status">
                        <span class="component-name">Procesare Cognitivă</span>
                        <div class="component-indicator loading" id="cognitiveIndicator"></div>
                    </div>
                    <div class="component-status">
                        <span class="component-name">Motor Conștiință</span>
                        <div class="component-indicator loading" id="consciousnessIndicator"></div>
                    </div>
                </div>
            </div>

            <div class="panel">
                <div class="panel-title">
                    📈 Statistici Live
                </div>
                <div class="metric-grid">
                    <div class="metric-item">
                        <div class="metric-value" id="totalConversations">0</div>
                        <div class="metric-label">Conversații Totale</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="trainingExercises">0</div>
                        <div class="metric-label">Exerciții Antrenament</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="avgResponseTime">0.0s</div>
                        <div class="metric-label">Timp Răspuns Mediu</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="systemUptime">0h</div>
                        <div class="metric-label">Uptime Sistem</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="modelAccuracy">0.0%</div>
                        <div class="metric-label">Acuratețe Model</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="learningProgress">0.0%</div>
                        <div class="metric-label">Progres Învățare</div>
                    </div>
                </div>
            </div>

            <div class="panel">
                <div class="panel-title">
                    🧠 Model LFM2-1.2B
                </div>
                <div style="font-size: 0.9em; line-height: 1.6;">
                    <div style="display: flex; justify-content: space-between; padding: 4px 0;">
                        <span>Model:</span>
                        <span>LFM2-1.2B GGUF</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; padding: 4px 0;">
                        <span>Parametri:</span>
                        <span>1.17B</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; padding: 4px 0;">
                        <span>Context:</span>
                        <span>32K tokens</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; padding: 4px 0;">
                        <span>Engine:</span>
                        <span>llama.cpp</span>
                    </div>
                </div>
                
                <div style="margin-top: 15px;">
                    <strong>Caracteristici Active:</strong>
                    <ul style="list-style: none; padding: 0; margin-top: 10px;">
                        <li style="padding: 3px 0; color: #666;">✓ Inferență în timp real</li>
                        <li style="padding: 3px 0; color: #666;">✓ Antrenament continuu</li>
                        <li style="padding: 3px 0; color: #666;">✓ Învățare adaptivă</li>
                        <li style="padding: 3px 0; color: #666;">✓ Procesare cognitivă</li>
                        <li style="padding: 3px 0; color: #666;">✓ Simulare conștiință</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Socket.IO
        const socket = io();
        
        // DOM elements
        const chatMessages = document.getElementById('chatMessages');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const loading = document.getElementById('loading');
        const connectionStatus = document.getElementById('connectionStatus');
        
        // Socket event handlers
        socket.on('connect', function() {
            connectionStatus.textContent = '🟢 Conectat';
            connectionStatus.className = 'connection-status connected';
            console.log('Conectat la server');
        });
        
        socket.on('disconnect', function() {
            connectionStatus.textContent = '🔴 Deconectat';
            connectionStatus.className = 'connection-status disconnected';
            console.log('Deconectat de la server');
        });
        
        socket.on('system_status_update', function(status) {
            updateSystemStatus(status);
        });
        
        socket.on('new_message', function(data) {
            console.log('Mesaj nou primit:', data);
        });
        
        socket.on('command_result', function(result) {
            console.log('Rezultat comandă:', result);
            if (result.success) {
                addMessage(`✅ ${result.message}`, false);
            } else {
                addMessage(`❌ ${result.message}`, false);
            }
        });
        
        // Update system status
        function updateSystemStatus(status) {
            if (!status) return;
            
            // Update system info
            const systemInfo = status.system_info || {};
            const statistics = status.statistics || {};
            const components = status.components || {};
            
            // Update header indicators
            updateIndicator('modelIndicator', 'modelStatus', 
                systemInfo.is_running ? 'Activ' : 'Inactiv',
                systemInfo.is_running);
            
            // Update statistics
            document.getElementById('totalConversations').textContent = statistics.total_conversations || 0;
            document.getElementById('trainingExercises').textContent = statistics.total_training_exercises || 0;
            document.getElementById('avgResponseTime').textContent = (statistics.average_response_time || 0).toFixed(3) + 's';
            
            // Update uptime
            const uptimeHours = Math.floor((statistics.uptime_seconds || 0) / 3600);
            const uptimeMinutes = Math.floor(((statistics.uptime_seconds || 0) % 3600) / 60);
            document.getElementById('systemUptime').textContent = `${uptimeHours}h ${uptimeMinutes}m`;
            
            document.getElementById('modelAccuracy').textContent = ((statistics.model_accuracy || 0) * 100).toFixed(1) + '%';
            document.getElementById('learningProgress').textContent = ((statistics.learning_progress || 0) * 100).toFixed(1) + '%';
            
            // Update component indicators
            updateComponentIndicators(components);
        }
        
        function updateIndicator(indicatorId, statusId, statusText, isActive) {
            const indicator = document.getElementById(indicatorId);
            const status = document.getElementById(statusId);
            
            if (indicator && status) {
                status.textContent = statusText;
                indicator.className = `status-indicator ${isActive ? '' : 'loading'}`;
            }
        }
        
        function updateComponentIndicators(components) {
            const componentMap = {
                'lfm2_system': 'lfm2Indicator',
                'training_engine': 'trainingEngineIndicator',
                'learning_system': 'learningSystemIndicator',
                'cognitive_processor': 'cognitiveIndicator',
                'consciousness_engine': 'consciousnessIndicator'
            };
            
            Object.entries(componentMap).forEach(([component, indicatorId]) => {
                const indicator = document.getElementById(indicatorId);
                if (indicator) {
                    const status = components[component];
                    if (status === 'running') {
                        indicator.className = 'component-indicator';
                    } else if (status === 'stopped') {
                        indicator.className = 'component-indicator stopped';
                    } else {
                        indicator.className = 'component-indicator loading';
                    }
                }
            });
        }
        
        // Add message to chat
        function addMessage(content, isUser = false, meta = null) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user-message' : 'ai-message'}`;
            
            const contentDiv = document.createElement('div');
            contentDiv.textContent = content;
            messageDiv.appendChild(contentDiv);
            
            if (meta) {
                const metaDiv = document.createElement('div');
                metaDiv.className = 'message-meta';
                metaDiv.textContent = meta;
                messageDiv.appendChild(metaDiv);
            }
            
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        // Send message
        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;
            
            // Add user message
            addMessage(message, true);
            messageInput.value = '';
            
            // Show loading
            loading.style.display = 'block';
            sendButton.disabled = true;
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    // Add AI response
                    const components = data.components_used ? data.components_used.join(', ') : 'N/A';
                    const meta = `⏱️ ${data.processing_time?.toFixed(3)}s | 🔧 Componente: ${components}`;
                    addMessage(data.response, false, meta);
                } else {
                    addMessage('❌ Eroare: ' + (data.error || 'Ceva nu a mers bine'), false);
                }
            } catch (error) {
                addMessage('❌ Eroare de conexiune: ' + error.message, false);
            } finally {
                loading.style.display = 'none';
                sendButton.disabled = false;
                messageInput.focus();
            }
        }
        
        // Execute system command
        function executeCommand(command) {
            socket.emit('execute_command', { command: command });
        }
        
        // Event listeners
        sendButton.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            messageInput.focus();
            
            // Request initial status
            socket.emit('request_system_status');
            
            // Update status periodically
            setInterval(() => {
                socket.emit('request_system_status');
            }, 10000);
        });
    </script>
</body>
</html>
