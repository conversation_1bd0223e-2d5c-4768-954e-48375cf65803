<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 Chat cu Alex - AI Simplu</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 20px;
            text-align: center;
            color: white;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .container {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .chat-box {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            width: 100%;
            max-width: 600px;
            height: 600px;
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e0e0e0;
        }

        .chat-header h2 {
            color: #333;
            font-size: 1.8em;
            margin-bottom: 5px;
        }

        .chat-header p {
            color: #666;
            font-size: 1em;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            margin-bottom: 20px;
            background: #f9f9f9;
        }

        .message {
            margin-bottom: 15px;
            padding: 12px 15px;
            border-radius: 15px;
            max-width: 85%;
            word-wrap: break-word;
        }

        .user-message {
            background: #74b9ff;
            color: white;
            margin-left: auto;
            text-align: right;
            border-bottom-right-radius: 5px;
        }

        .ai-message {
            background: #00b894;
            color: white;
            margin-right: auto;
            border-bottom-left-radius: 5px;
        }

        .input-area {
            display: flex;
            gap: 10px;
        }

        .message-input {
            flex: 1;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        .message-input:focus {
            border-color: #74b9ff;
        }

        .send-button {
            padding: 15px 25px;
            background: #74b9ff;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: background 0.3s;
        }

        .send-button:hover {
            background: #0984e3;
        }

        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .loading {
            display: none;
            text-align: center;
            color: #666;
            font-style: italic;
            margin-bottom: 10px;
        }

        .stats {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            padding: 10px 15px;
            border-radius: 10px;
            font-size: 0.9em;
            color: #333;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .chat-box {
                height: 80vh;
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .stats {
                position: static;
                margin-bottom: 20px;
                text-align: center;
            }
        }

        /* Scrollbar styling */
        .chat-messages::-webkit-scrollbar {
            width: 8px;
        }

        .chat-messages::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        .chat-messages::-webkit-scrollbar-thumb {
            background: #74b9ff;
            border-radius: 10px;
        }

        .chat-messages::-webkit-scrollbar-thumb:hover {
            background: #0984e3;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 Chat cu Alex</h1>
        <p>AI simplu și prietenos pentru conversații naturale</p>
    </div>

    <div class="stats" id="stats">
        💬 Conversații: <span id="conversationCount">0</span>
    </div>

    <div class="container">
        <div class="chat-box">
            <div class="chat-header">
                <h2>💬 Conversație cu Alex</h2>
                <p>Scrie orice - Alex va răspunde natural!</p>
            </div>

            <div class="chat-messages" id="chatMessages">
                <div class="message ai-message">
                    Salut! Sunt Alex, un AI simplu și prietenos. Îmi place să conversez! Cu ce te pot ajuta astăzi? 😊
                </div>
            </div>

            <div class="loading" id="loading">
                🤔 Alex gândește...
            </div>

            <div class="input-area">
                <input type="text" id="messageInput" class="message-input" placeholder="Scrie mesajul tău aici..." maxlength="500">
                <button id="sendButton" class="send-button">Trimite</button>
            </div>
        </div>
    </div>

    <script>
        const chatMessages = document.getElementById('chatMessages');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const loading = document.getElementById('loading');
        const conversationCount = document.getElementById('conversationCount');

        // Funcție pentru adăugarea mesajelor
        function addMessage(content, isUser = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user-message' : 'ai-message'}`;
            messageDiv.textContent = content;
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Funcție pentru trimiterea mesajelor
        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            // Adaugă mesajul utilizatorului
            addMessage(message, true);
            messageInput.value = '';
            
            // Afișează loading
            loading.style.display = 'block';
            sendButton.disabled = true;

            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                });

                const data = await response.json();

                if (response.ok) {
                    // Adaugă răspunsul lui Alex
                    addMessage(data.response);
                    
                    // Actualizează statisticile
                    conversationCount.textContent = data.conversations;
                } else {
                    addMessage('❌ Eroare: ' + (data.error || 'Ceva nu a mers bine'));
                }
            } catch (error) {
                addMessage('❌ Eroare de conexiune: ' + error.message);
            } finally {
                loading.style.display = 'none';
                sendButton.disabled = false;
                messageInput.focus();
            }
        }

        // Event listeners
        sendButton.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // Actualizează statisticile la încărcare
        async function updateStats() {
            try {
                const response = await fetch('/api/stats');
                const data = await response.json();
                if (response.ok) {
                    conversationCount.textContent = data.conversations;
                }
            } catch (error) {
                console.error('Eroare la actualizarea statisticilor:', error);
            }
        }

        // Focus pe input la încărcare
        messageInput.focus();
        updateStats();
    </script>
</body>
</html>
