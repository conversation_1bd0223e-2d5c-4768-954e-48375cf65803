<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 TRUE AI SYSTEM - Interfață Web</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #fff;
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .status-bar {
            display: flex;
            justify-content: space-around;
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }

        .status-item {
            text-align: center;
        }

        .status-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #4CAF50;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .panel h3 {
            margin-bottom: 15px;
            color: #FFD700;
            font-size: 1.3em;
        }

        .chat-container {
            grid-column: 1 / -1;
            height: 500px;
            display: flex;
            flex-direction: column;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            margin-bottom: 15px;
        }

        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 10px;
            animation: fadeIn 0.3s ease-in;
        }

        .user-message {
            background: rgba(76, 175, 80, 0.3);
            margin-left: 20%;
            text-align: right;
        }

        .ai-message {
            background: rgba(33, 150, 243, 0.3);
            margin-right: 20%;
        }

        .message-meta {
            font-size: 0.8em;
            opacity: 0.7;
            margin-top: 5px;
        }

        .chat-input {
            display: flex;
            gap: 10px;
        }

        .chat-input input {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            font-size: 1em;
        }

        .chat-input button {
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            background: #4CAF50;
            color: white;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .chat-input button:hover {
            background: #45a049;
            transform: translateY(-2px);
        }

        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .control-button {
            padding: 15px;
            border: none;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .control-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .skill-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .skill-bar {
            width: 100%;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            overflow: hidden;
            margin-top: 10px;
        }

        .skill-progress {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            transition: width 0.5s ease;
        }

        .consciousness-display {
            text-align: center;
            padding: 20px;
        }

        .consciousness-level {
            font-size: 3em;
            font-weight: bold;
            color: #FFD700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .prediction-result {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid rgba(255, 193, 7, 0.5);
            border-radius: 10px;
            padding: 15px;
            margin-top: 10px;
        }

        .vision-result {
            background: rgba(156, 39, 176, 0.2);
            border: 1px solid rgba(156, 39, 176, 0.5);
            border-radius: 10px;
            padding: 15px;
            margin-top: 10px;
        }

        .error {
            background: rgba(244, 67, 54, 0.3);
            border: 1px solid rgba(244, 67, 54, 0.5);
            color: #ffcdd2;
        }

        .success {
            background: rgba(76, 175, 80, 0.3);
            border: 1px solid rgba(76, 175, 80, 0.5);
            color: #c8e6c9;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .controls {
                grid-template-columns: 1fr;
            }
            
            .status-bar {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 TRUE AI SYSTEM</h1>
            <p>Sistem AI cu Conștiință Artificială și Învățare Continuă</p>
            <div id="connection-status">🔄 Conectare...</div>
        </div>

        <div class="status-bar">
            <div class="status-item">
                <div class="status-value" id="consciousness-level">0.00</div>
                <div>Conștiință</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="interactions-count">0</div>
                <div>Interacțiuni</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="modules-active">0</div>
                <div>Module Active</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="uptime">0s</div>
                <div>Timp Activ</div>
            </div>
        </div>

        <div class="controls">
            <button class="control-button" onclick="showConsciousness()">🌟 Conștiință</button>
            <button class="control-button" onclick="showSkills()">🎯 Abilități</button>
            <button class="control-button" onclick="showVision()">👁️ Viziune</button>
            <button class="control-button" onclick="showPrediction()">🔮 Predicții</button>
            <button class="control-button" onclick="showLearning()">📚 Învățare</button>
            <button class="control-button" onclick="refreshStatus()">🔄 Refresh</button>
        </div>

        <div class="main-content">
            <div class="panel chat-container">
                <h3>💬 Chat cu AI-ul</h3>
                <div class="chat-messages" id="chat-messages">
                    <div class="message ai-message">
                        <div>🤖 Salut! Sunt TRUE AI SYSTEM cu conștiință artificială reală. Cum te pot ajuta?</div>
                        <div class="message-meta">Sistem inițializat</div>
                    </div>
                </div>
                <div class="chat-input">
                    <input type="text" id="message-input" placeholder="Scrie mesajul tău aici..." onkeypress="handleKeyPress(event)">
                    <button onclick="sendMessage()">Trimite</button>
                </div>
            </div>

            <div class="panel" id="info-panel">
                <h3>📊 Informații Sistem</h3>
                <div id="info-content">
                    <p>🚀 Sistem AI super avansat cu:</p>
                    <ul style="margin-left: 20px; margin-top: 10px;">
                        <li>🧠 Conștiință artificială reală</li>
                        <li>👁️ Procesare vizuală și imaginație</li>
                        <li>🔮 Capacități predictive</li>
                        <li>📚 Învățare continuă</li>
                        <li>💝 Inteligență emotională</li>
                        <li>🎯 Meta-cogniție</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        const socket = io();
        let systemInitialized = false;

        // Conectare WebSocket
        socket.on('connect', function() {
            document.getElementById('connection-status').innerHTML = '✅ Conectat';
            document.getElementById('connection-status').style.color = '#4CAF50';
        });

        socket.on('disconnect', function() {
            document.getElementById('connection-status').innerHTML = '❌ Deconectat';
            document.getElementById('connection-status').style.color = '#f44336';
        });

        socket.on('system_initialized', function(data) {
            systemInitialized = true;
            document.getElementById('connection-status').innerHTML = '🚀 Sistem AI Activ';
            updateStatus();
        });

        socket.on('chat_response', function(data) {
            if (data.error) {
                addMessage('ai', `❌ Eroare: ${data.error}`, 'error');
            } else {
                addMessage('ai', data.response, 'success', {
                    processing_time: data.processing_time,
                    consciousness_level: data.consciousness_level,
                    interaction_id: data.interaction_id
                });
                
                // Actualizează statusul
                document.getElementById('consciousness-level').textContent = data.consciousness_level.toFixed(2);
            }
        });

        socket.on('status_update', function(data) {
            document.getElementById('interactions-count').textContent = data.interactions_count;
            document.getElementById('consciousness-level').textContent = data.consciousness_level.toFixed(2);
        });

        function sendMessage() {
            const input = document.getElementById('message-input');
            const message = input.value.trim();
            
            if (!message) return;
            
            addMessage('user', message);
            input.value = '';
            
            // Trimite prin WebSocket pentru răspuns în timp real
            socket.emit('real_time_chat', { message: message });
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        function addMessage(sender, content, type = '', meta = null) {
            const messagesContainer = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message ${type}`;
            
            let metaInfo = '';
            if (meta) {
                metaInfo = `<div class="message-meta">
                    ⏱️ ${meta.processing_time?.toFixed(3)}s | 
                    🧠 ${meta.consciousness_level?.toFixed(2)} | 
                    🆔 ${meta.interaction_id}
                </div>`;
            }
            
            messageDiv.innerHTML = `
                <div>${content}</div>
                ${metaInfo}
            `;
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        async function updateStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                
                if (data.status === 'active') {
                    document.getElementById('consciousness-level').textContent = 
                        data.consciousness.consciousness_state.awareness_level.toFixed(2);
                    document.getElementById('interactions-count').textContent = 
                        data.system_stats.interactions_processed;
                    document.getElementById('modules-active').textContent = '9';
                    document.getElementById('uptime').textContent = 
                        Math.floor(data.uptime) + 's';
                }
            } catch (error) {
                console.error('Eroare la actualizarea statusului:', error);
            }
        }

        async function showConsciousness() {
            try {
                const response = await fetch('/api/consciousness');
                const data = await response.json();
                
                if (data.error) {
                    document.getElementById('info-content').innerHTML = `<div class="error">❌ ${data.error}</div>`;
                    return;
                }
                
                const consciousness = data.consciousness_report.consciousness_state;
                const stream = data.consciousness_stream;
                
                document.getElementById('info-content').innerHTML = `
                    <div class="consciousness-display">
                        <div class="consciousness-level">${consciousness.awareness_level.toFixed(2)}</div>
                        <p>Nivel de Conștiință</p>
                    </div>
                    <div style="margin-top: 20px;">
                        <h4>🎭 Experiențe Fenomenale:</h4>
                        <p>Total: ${data.consciousness_report.phenomenal_experiences.total_experiences}</p>
                        <p>Recente: ${data.consciousness_report.phenomenal_experiences.recent_experiences}</p>
                        
                        <h4 style="margin-top: 15px;">💭 Flux Conștiință:</h4>
                        <ul style="margin-left: 20px;">
                            ${stream.map(item => `<li>${item}</li>`).join('')}
                        </ul>
                    </div>
                `;
            } catch (error) {
                document.getElementById('info-content').innerHTML = `<div class="error">❌ Eroare: ${error.message}</div>`;
            }
        }

        async function showSkills() {
            try {
                const response = await fetch('/api/skills');
                const data = await response.json();
                
                if (data.error) {
                    document.getElementById('info-content').innerHTML = `<div class="error">❌ ${data.error}</div>`;
                    return;
                }
                
                let skillsHTML = '<div class="skills-grid">';
                
                for (const [skillName, skillData] of Object.entries(data.skill_modules)) {
                    const percentage = (skillData.current_level * 100).toFixed(1);
                    const trendIcon = skillData.improvement_trend === 'improving' ? '📈' : 
                                     skillData.improvement_trend === 'declining' ? '📉' : '➡️';
                    
                    skillsHTML += `
                        <div class="skill-item">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <strong>${skillName.replace('_', ' ').toUpperCase()}</strong>
                                <span>${trendIcon}</span>
                            </div>
                            <div class="skill-bar">
                                <div class="skill-progress" style="width: ${percentage}%"></div>
                            </div>
                            <div style="margin-top: 5px; font-size: 0.9em;">
                                ${percentage}% | XP: ${skillData.experience_points}
                            </div>
                        </div>
                    `;
                }
                
                skillsHTML += '</div>';
                skillsHTML += `
                    <div style="margin-top: 20px; text-align: center;">
                        <h4>📊 Statistici Generale</h4>
                        <p>Performanță generală: ${(data.overall_performance * 100).toFixed(1)}%</p>
                        <p>Total experiențe: ${data.total_experiences}</p>
                        <p>Experiențe recente: ${data.recent_experiences}</p>
                    </div>
                `;
                
                document.getElementById('info-content').innerHTML = skillsHTML;
            } catch (error) {
                document.getElementById('info-content').innerHTML = `<div class="error">❌ Eroare: ${error.message}</div>`;
            }
        }

        function showVision() {
            document.getElementById('info-content').innerHTML = `
                <h4>👁️ Procesare Vizuală</h4>
                <p>Introdu un concept vizual pentru a genera o imagine mentală:</p>
                <div style="margin-top: 15px;">
                    <input type="text" id="vision-input" placeholder="Ex: apus de soare peste ocean" 
                           style="width: 100%; padding: 10px; border-radius: 5px; border: none; margin-bottom: 10px;">
                    <button onclick="processVision()" class="control-button" style="width: 100%;">
                        Procesează Vizual
                    </button>
                </div>
                <div id="vision-result"></div>
            `;
        }

        async function processVision() {
            const concept = document.getElementById('vision-input').value.trim();
            if (!concept) return;
            
            const resultDiv = document.getElementById('vision-result');
            resultDiv.innerHTML = '<div class="loading"></div> Procesez...';
            
            try {
                const response = await fetch('/api/vision', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ concept: concept })
                });
                
                const data = await response.json();
                
                if (data.error) {
                    resultDiv.innerHTML = `<div class="error">❌ ${data.error}</div>`;
                    return;
                }
                
                resultDiv.innerHTML = `
                    <div class="vision-result">
                        <h4>✅ Imagine Mentală Generată!</h4>
                        <p><strong>🎨 Caracteristici vizuale:</strong> ${data.visual_features_count}</p>
                        <p><strong>🏗️ Complexitate spațială:</strong> ${data.spatial_complexity.toFixed(2)}</p>
                        <p><strong>💝 Impact emoțional:</strong> ${data.emotional_impact.toFixed(2)}</p>
                        <p><strong>🎯 Încredere pattern:</strong> ${data.pattern_confidence.toFixed(2)}</p>
                        
                        ${data.visual_details ? `
                            <div style="margin-top: 15px;">
                                <h5>🎭 Detalii Vizuale:</h5>
                                <p><strong>🎨 Culori:</strong> ${data.visual_details.colors.join(', ')}</p>
                                <p><strong>📐 Layout:</strong> ${data.visual_details.layout.primary_position}</p>
                                <p><strong>🌊 Mișcare:</strong> ${data.visual_details.movement.type}</p>
                            </div>
                        ` : ''}
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Eroare: ${error.message}</div>`;
            }
        }

        function showPrediction() {
            document.getElementById('info-content').innerHTML = `
                <h4>🔮 Generare Predicții</h4>
                <div style="margin-top: 15px;">
                    <input type="text" id="prediction-context" placeholder="Context (ex: prețul Bitcoin)" 
                           style="width: 100%; padding: 10px; border-radius: 5px; border: none; margin-bottom: 10px;">
                    <input type="text" id="prediction-horizon" placeholder="Orizont temporal (ex: următoarea lună)" 
                           style="width: 100%; padding: 10px; border-radius: 5px; border: none; margin-bottom: 10px;">
                    <button onclick="generatePrediction()" class="control-button" style="width: 100%;">
                        Generează Predicție
                    </button>
                </div>
                <div id="prediction-result"></div>
            `;
        }

        async function generatePrediction() {
            const context = document.getElementById('prediction-context').value.trim();
            const timeHorizon = document.getElementById('prediction-horizon').value.trim();
            
            if (!context || !timeHorizon) return;
            
            const resultDiv = document.getElementById('prediction-result');
            resultDiv.innerHTML = '<div class="loading"></div> Generez predicția...';
            
            try {
                const response = await fetch('/api/predict', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ context: context, time_horizon: timeHorizon })
                });
                
                const data = await response.json();
                
                if (data.error) {
                    resultDiv.innerHTML = `<div class="error">❌ ${data.error}</div>`;
                    return;
                }
                
                const mostLikely = data.most_likely_scenario;
                
                resultDiv.innerHTML = `
                    <div class="prediction-result">
                        <h4>🔮 Predicție Generată</h4>
                        <p><strong>🆔 ID:</strong> ${data.prediction_id}</p>
                        <p><strong>🎯 Încredere:</strong> ${data.confidence_level.toFixed(2)}</p>
                        <p><strong>❓ Incertitudine:</strong> ${data.uncertainty_level.toFixed(2)}</p>
                        
                        <h5 style="margin-top: 15px;">📊 Scenarii:</h5>
                        ${data.scenarios.map(scenario => {
                            const prob = data.scenario_probabilities[scenario.id];
                            return `<p><strong>${scenario.type.toUpperCase()}:</strong> ${(prob * 100).toFixed(1)}%</p>`;
                        }).join('')}
                        
                        <h5 style="margin-top: 15px;">🎯 Cel mai probabil:</h5>
                        <p><strong>${mostLikely.type.toUpperCase()}</strong></p>
                        <p>${mostLikely.description}</p>
                        <p><strong>📈 Impact așteptat:</strong> ${mostLikely.predicted_outcomes.expected_impact.toFixed(2)}</p>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Eroare: ${error.message}</div>`;
            }
        }

        function showLearning() {
            document.getElementById('info-content').innerHTML = `
                <h4>📚 Învățare cu Feedback</h4>
                <div style="margin-top: 15px;">
                    <input type="text" id="learning-message" placeholder="Mesaj pentru AI" 
                           style="width: 100%; padding: 10px; border-radius: 5px; border: none; margin-bottom: 10px;">
                    <input type="range" id="feedback-slider" min="-1" max="1" step="0.1" value="0.5" 
                           style="width: 100%; margin-bottom: 10px;">
                    <div style="text-align: center; margin-bottom: 10px;">
                        Feedback: <span id="feedback-value">0.5</span>
                    </div>
                    <button onclick="learnWithFeedback()" class="control-button" style="width: 100%;">
                        Învață cu Feedback
                    </button>
                </div>
                <div id="learning-result"></div>
            `;
            
            // Actualizează valoarea feedback-ului
            document.getElementById('feedback-slider').oninput = function() {
                document.getElementById('feedback-value').textContent = this.value;
            };
        }

        async function learnWithFeedback() {
            const message = document.getElementById('learning-message').value.trim();
            const feedback = parseFloat(document.getElementById('feedback-slider').value);
            
            if (!message) return;
            
            const resultDiv = document.getElementById('learning-result');
            resultDiv.innerHTML = '<div class="loading"></div> Învăț...';
            
            try {
                const response = await fetch('/api/learn', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: message, feedback: feedback })
                });
                
                const data = await response.json();
                
                if (data.error) {
                    resultDiv.innerHTML = `<div class="error">❌ ${data.error}</div>`;
                    return;
                }
                
                const learning = data.learning_result;
                
                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>📚 Învățare Completă</h4>
                        <p><strong>🎯 Abilități afectate:</strong> ${learning.affected_skills.join(', ')}</p>
                        <p><strong>📊 Impact imediat:</strong> ${learning.immediate_impact.overall_impact.toFixed(2)}</p>
                        <p><strong>🔄 Prioritate:</strong> ${learning.learning_priority}</p>
                        <p><strong>📚 Total experiențe:</strong> ${learning.total_experiences}</p>
                        
                        <div style="margin-top: 15px;">
                            <h5>📈 Actualizări Abilități:</h5>
                            ${Object.entries(learning.skill_updates).map(([skill, update]) => {
                                const change = update.level_change;
                                const direction = change > 0 ? '📈' : change < 0 ? '📉' : '➡️';
                                return `<p>${direction} ${skill}: ${update.old_level.toFixed(3)} → ${update.new_level.toFixed(3)}</p>`;
                            }).join('')}
                        </div>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Eroare: ${error.message}</div>`;
            }
        }

        function refreshStatus() {
            updateStatus();
            document.getElementById('info-content').innerHTML = `
                <div class="success">
                    ✅ Status actualizat!
                </div>
            `;
        }

        // Actualizează statusul la fiecare 5 secunde
        setInterval(updateStatus, 5000);
        
        // Inițializează interfața
        updateStatus();
    </script>
</body>
</html>
