#!/usr/bin/env python3
"""
START SIMPLE CHAT - Pornire AI simplu pentru conversații
"""

import os
import sys
import subprocess
import time
import webbrowser
from datetime import datetime

def print_banner():
    """Afișează banner-ul"""
    print("=" * 60)
    print("🤖 SIMPLE CHAT AI - Alex")
    print("=" * 60)
    print("💬 AI simplu pentru conversații naturale")
    print("🌟 Fără complicații - doar conversație")
    print("📅 Data: " + datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("=" * 60)
    print()

def main():
    """Funcția principală"""
    print_banner()
    
    # Schimbă în directorul corect
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    print("🎮 Alege modul de conversație:")
    print("1. 🖥️  Terminal - Chat în terminal")
    print("2. 🌐 Web - Chat în browser (http://localhost:5002)")
    print("3. 🚀 Ambele - Terminal + Web")
    print()
    
    while True:
        try:
            choice = input("Alege opțiunea (1-3): ").strip()
            
            if choice == '1':
                print("\n🖥️  Pornire chat în terminal...")
                subprocess.run([sys.executable, 'simple_chat.py'])
                break
                
            elif choice == '2':
                print("\n🌐 Pornire chat web...")
                print("🔗 Accesează: http://localhost:5002")
                
                # Pornește serverul web
                web_process = subprocess.Popen([sys.executable, 'simple_chat_web.py'])
                
                # Așteaptă puțin și deschide browser-ul
                time.sleep(3)
                try:
                    webbrowser.open('http://localhost:5002')
                    print("🌐 Browser deschis automat!")
                except:
                    print("🔗 Deschide manual: http://localhost:5002")
                
                print("\n💡 Apasă Ctrl+C pentru a opri serverul")
                
                try:
                    web_process.wait()
                except KeyboardInterrupt:
                    print("\n🛑 Oprire server web...")
                    web_process.terminate()
                    web_process.wait()
                    print("✅ Server oprit!")
                
                break
                
            elif choice == '3':
                print("\n🚀 Pornire ambele interfețe...")
                
                # Pornește interfața web în background
                print("🌐 Pornire chat web...")
                web_process = subprocess.Popen([sys.executable, 'simple_chat_web.py'])
                
                # Așteaptă puțin
                time.sleep(3)
                
                # Deschide browser-ul
                try:
                    webbrowser.open('http://localhost:5002')
                    print("🌐 Browser deschis automat!")
                except:
                    print("🔗 Deschide manual: http://localhost:5002")
                
                print("🖥️  Pornire chat în terminal...")
                print("💡 Chat-ul web rulează pe http://localhost:5002")
                print()
                
                try:
                    # Pornește chat-ul în terminal
                    subprocess.run([sys.executable, 'simple_chat.py'])
                except KeyboardInterrupt:
                    print("\n🛑 Oprire sisteme...")
                finally:
                    # Oprește serverul web
                    web_process.terminate()
                    web_process.wait()
                    print("✅ Toate sistemele oprite!")
                
                break
                
            else:
                print("❌ Opțiune invalidă! Alege 1, 2 sau 3.")
                
        except KeyboardInterrupt:
            print("\n🛑 Întrerupere de la tastatură...")
            break
        except Exception as e:
            print(f"❌ Eroare: {e}")
    
    print("\n👋 La revedere!")

if __name__ == "__main__":
    main()
