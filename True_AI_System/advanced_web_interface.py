#!/usr/bin/env python3
"""
ADVANCED WEB INTERFACE - Web interface for the sophisticated AI system
Features: Real-time status, model monitoring, conversation analytics
"""

from flask import Flask, render_template, request, jsonify, Response
from flask_socketio import Socket<PERSON>, emit
import json
import time
import threading
from datetime import datetime
from advanced_ai_system import get_ai_system

app = Flask(__name__)
app.config['SECRET_KEY'] = 'advanced_ai_secret_key'
socketio = SocketIO(app, cors_allowed_origins="*")

# Global variables
ai_system = None
status_update_thread = None

def initialize_ai_system():
    """Initialize AI system"""
    global ai_system
    if ai_system is None:
        ai_system = get_ai_system()
    return ai_system

def broadcast_status_updates():
    """Broadcast system status updates to all connected clients"""
    while True:
        try:
            if ai_system:
                status = ai_system.get_system_status()
                socketio.emit('status_update', status)
            time.sleep(5)  # Update every 5 seconds
        except Exception as e:
            print(f"Status broadcast error: {e}")
            time.sleep(10)

@app.route('/')
def index():
    """Main page"""
    return render_template('advanced_chat.html')

@app.route('/api/chat', methods=['POST'])
def chat():
    """Chat endpoint"""
    try:
        data = request.get_json()
        message = data.get('message', '').strip()
        
        if not message:
            return jsonify({'error': 'Message cannot be empty'}), 400
        
        # Initialize AI system if needed
        ai = initialize_ai_system()
        
        # Process conversation
        result = ai.process_conversation(message)
        
        # Emit real-time update
        socketio.emit('new_message', {
            'user_message': message,
            'ai_response': result['response'],
            'timestamp': result.get('timestamp', datetime.now().isoformat()),
            'quality_score': result.get('quality_score', 0),
            'processing_time': result.get('processing_time', 0)
        })
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/status')
def status():
    """System status endpoint"""
    try:
        ai = initialize_ai_system()
        return jsonify(ai.get_system_status())
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/conversations')
def conversations():
    """Get conversation history"""
    try:
        ai = initialize_ai_system()
        
        # Get recent conversations
        recent_conversations = ai.conversation_history[-50:]  # Last 50 conversations
        
        conversations_data = []
        for i, conv in enumerate(recent_conversations):
            conversations_data.append({
                'id': i,
                'timestamp': conv.timestamp,
                'user_input': conv.user_input,
                'ai_response': conv.ai_response,
                'quality_score': conv.quality_score,
                'learning_value': conv.learning_value
            })
        
        return jsonify({
            'conversations': conversations_data,
            'total_count': len(ai.conversation_history)
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/analytics')
def analytics():
    """Get conversation analytics"""
    try:
        ai = initialize_ai_system()
        
        if not ai.conversation_history:
            return jsonify({
                'total_conversations': 0,
                'average_quality': 0,
                'average_learning_value': 0,
                'conversations_per_hour': 0,
                'quality_trend': [],
                'learning_trend': []
            })
        
        # Calculate analytics
        total_conversations = len(ai.conversation_history)
        
        quality_scores = [conv.quality_score for conv in ai.conversation_history]
        learning_values = [conv.learning_value for conv in ai.conversation_history]
        
        average_quality = sum(quality_scores) / len(quality_scores)
        average_learning_value = sum(learning_values) / len(learning_values)
        
        # Calculate conversations per hour
        if total_conversations > 1:
            first_conv = datetime.fromisoformat(ai.conversation_history[0].timestamp)
            last_conv = datetime.fromisoformat(ai.conversation_history[-1].timestamp)
            time_diff = (last_conv - first_conv).total_seconds() / 3600  # hours
            conversations_per_hour = total_conversations / max(time_diff, 1)
        else:
            conversations_per_hour = 0
        
        # Get trends (last 20 conversations)
        recent_convs = ai.conversation_history[-20:]
        quality_trend = [conv.quality_score for conv in recent_convs]
        learning_trend = [conv.learning_value for conv in recent_convs]
        
        return jsonify({
            'total_conversations': total_conversations,
            'average_quality': round(average_quality, 3),
            'average_learning_value': round(average_learning_value, 3),
            'conversations_per_hour': round(conversations_per_hour, 2),
            'quality_trend': quality_trend,
            'learning_trend': learning_trend
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/model/info')
def model_info():
    """Get model information"""
    try:
        ai = initialize_ai_system()
        
        return jsonify({
            'model_name': ai.config.model_name,
            'model_loaded': ai.system_stats['model_loaded'],
            'device': str(ai.model_manager.device) if ai.model_manager.device else 'unknown',
            'torch_dtype': ai.config.torch_dtype,
            'temperature': ai.config.temperature,
            'min_p': ai.config.min_p,
            'repetition_penalty': ai.config.repetition_penalty,
            'max_new_tokens': ai.config.max_new_tokens,
            'context_length': ai.config.context_length
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    print(f"Client connected: {request.sid}")
    
    # Send initial status
    try:
        ai = initialize_ai_system()
        status = ai.get_system_status()
        emit('status_update', status)
    except Exception as e:
        print(f"Error sending initial status: {e}")

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    print(f"Client disconnected: {request.sid}")

@socketio.on('request_status')
def handle_status_request():
    """Handle status request from client"""
    try:
        ai = initialize_ai_system()
        status = ai.get_system_status()
        emit('status_update', status)
    except Exception as e:
        emit('error', {'message': str(e)})

def start_background_tasks():
    """Start background tasks"""
    global status_update_thread
    
    if status_update_thread is None:
        status_update_thread = threading.Thread(target=broadcast_status_updates, daemon=True)
        status_update_thread.start()

if __name__ == '__main__':
    print("🌐 Starting Advanced AI Web Interface...")
    print("🔗 Access at: http://localhost:5003")
    print("🧠 LFM2-1.2B Model Integration")
    print("📊 Real-time Analytics & Monitoring")
    print("🎓 Continuous Learning Dashboard")
    print()
    
    # Initialize AI system
    initialize_ai_system()
    
    # Start background tasks
    start_background_tasks()
    
    # Run the application
    socketio.run(app, host='0.0.0.0', port=5003, debug=False)
