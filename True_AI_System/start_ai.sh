#!/bin/bash

# TRUE AI SYSTEM - Script de pornire rapidă
# Porne<PERSON><PERSON> toate sistemele AI într-o singură comandă

echo "🚀 TRUE AI SYSTEM - PORNIRE AUTOMATĂ"
echo "===================================="

# Schimbă în directorul corect
cd "$(dirname "$0")"

# Verifică dacă Python3 este instalat
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 nu este instalat!"
    exit 1
fi

echo "✅ Python3 găsit"

# Verifică dependențele
echo "🔍 Verificare dependențe..."

# Instalează dependențele dacă lipsesc
python3 -c "import flask" 2>/dev/null || {
    echo "📦 Instalare Flask..."
    pip install flask flask-socketio numpy --break-system-packages
}

echo "✅ Dependențe verificate"

# Pornește sistemul complet
echo "🚀 Pornire TRUE AI SYSTEM..."
python3 start_all_systems.py

echo "👋 TRUE AI SYSTEM oprit!"
