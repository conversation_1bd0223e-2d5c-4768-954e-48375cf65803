"""
CONTINUOUS TRAINING SYSTEM - Sistem de antrenament continuu real
Învață din fiecare conversație și se dezvoltă autonom
"""

import json
import os
import time
import threading
import random
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import pickle
import numpy as np

@dataclass
class ConversationMemory:
    """Memorie de conversație pentru antrenament"""
    timestamp: datetime
    user_input: str
    ai_response: str
    context: Dict[str, Any]
    feedback_score: float = 0.0
    response_quality: float = 0.0
    learning_value: float = 0.0
    conversation_id: str = ""

@dataclass
class KnowledgePattern:
    """Pattern de cunoaștere învățat"""
    pattern_id: str
    pattern_type: str  # 'response', 'context', 'reasoning'
    input_pattern: str
    output_pattern: str
    confidence: float
    usage_count: int
    success_rate: float
    last_used: datetime
    created: datetime

class ContinuousTrainingSystem:
    """Sistem de antrenament continuu pentru AI"""
    
    def __init__(self):
        self.training_active = True
        self.conversation_memory: List[ConversationMemory] = []
        self.knowledge_patterns: Dict[str, KnowledgePattern] = {}
        self.response_templates: Dict[str, List[str]] = {}
        self.learning_rate = 0.1
        self.adaptation_threshold = 0.7
        
        # Statistici de antrenament
        self.training_stats = {
            'total_conversations': 0,
            'patterns_learned': 0,
            'successful_adaptations': 0,
            'training_sessions': 0,
            'last_training': None,
            'improvement_score': 0.0
        }
        
        # Încarcă datele existente
        self.load_training_data()
        
        # Pornește antrenamentul continuu
        self.start_continuous_training()
    
    def start_continuous_training(self):
        """Pornește antrenamentul continuu în background"""
        training_thread = threading.Thread(target=self._continuous_training_loop, daemon=False)
        training_thread.start()
        print("🎓 Sistem de antrenament continuu activat!")
    
    def _continuous_training_loop(self):
        """Loop principal de antrenament continuu"""
        while self.training_active:
            try:
                # Antrenament la fiecare 30 secunde
                time.sleep(30)
                
                if len(self.conversation_memory) > 0:
                    self._perform_training_session()
                    
            except Exception as e:
                print(f"⚠️ Eroare în antrenament continuu: {e}")
                time.sleep(60)  # Pauză mai lungă în caz de eroare
    
    def learn_from_conversation(self, user_input: str, ai_response: str, 
                               context: Dict[str, Any] = None, feedback: float = None) -> Dict[str, Any]:
        """Învață dintr-o conversație nouă"""
        
        # Creează memoria conversației
        memory = ConversationMemory(
            timestamp=datetime.now(),
            user_input=user_input.strip(),
            ai_response=ai_response.strip(),
            context=context or {},
            feedback_score=feedback or self._estimate_response_quality(user_input, ai_response),
            conversation_id=f"conv_{int(time.time())}"
        )
        
        # Evaluează calitatea răspunsului
        memory.response_quality = self._evaluate_response_quality(memory)
        memory.learning_value = self._calculate_learning_value(memory)
        
        # Adaugă în memorie
        self.conversation_memory.append(memory)
        self.training_stats['total_conversations'] += 1
        
        # Învață imediat din conversația valoroasă
        if memory.learning_value > 0.6:
            self._extract_immediate_patterns(memory)
        
        # Limitează memoria la ultimele 1000 conversații
        if len(self.conversation_memory) > 1000:
            self.conversation_memory = self.conversation_memory[-1000:]
        
        # Salvează progresul
        self.save_training_data()
        
        return {
            'learned': True,
            'learning_value': memory.learning_value,
            'response_quality': memory.response_quality,
            'patterns_extracted': len(self.knowledge_patterns),
            'memory_size': len(self.conversation_memory)
        }
    
    def _estimate_response_quality(self, user_input: str, ai_response: str) -> float:
        """Estimează calitatea răspunsului"""
        quality_score = 0.5  # Scor de bază
        
        # Verifică lungimea răspunsului
        if 20 <= len(ai_response) <= 500:
            quality_score += 0.1
        
        # Verifică relevanța (cuvinte comune)
        user_words = set(user_input.lower().split())
        response_words = set(ai_response.lower().split())
        common_words = user_words.intersection(response_words)
        
        if len(common_words) > 0:
            quality_score += min(0.2, len(common_words) * 0.05)
        
        # Verifică structura răspunsului
        if any(punct in ai_response for punkt in ['.', '!', '?']):
            quality_score += 0.1
        
        # Verifică dacă răspunsul pare natural
        if not any(template in ai_response.lower() for template in ['procesez', 'modulele mele', 'sistem ai']):
            quality_score += 0.2
        
        return min(1.0, quality_score)
    
    def _evaluate_response_quality(self, memory: ConversationMemory) -> float:
        """Evaluează calitatea detaliată a răspunsului"""
        
        # Folosește feedback-ul dacă există
        if memory.feedback_score > 0:
            return memory.feedback_score
        
        # Altfel estimează
        return self._estimate_response_quality(memory.user_input, memory.ai_response)
    
    def _calculate_learning_value(self, memory: ConversationMemory) -> float:
        """Calculează valoarea de învățare a conversației"""
        
        learning_value = memory.response_quality
        
        # Conversațiile noi sunt mai valoroase
        if memory.response_quality > 0.7:
            learning_value += 0.2
        
        # Conversațiile cu context sunt mai valoroase
        if memory.context and len(memory.context) > 0:
            learning_value += 0.1
        
        # Conversațiile cu feedback pozitiv sunt foarte valoroase
        if memory.feedback_score > 0.8:
            learning_value += 0.3
        
        return min(1.0, learning_value)
    
    def _extract_immediate_patterns(self, memory: ConversationMemory):
        """Extrage pattern-uri imediat din conversația valoroasă"""
        
        # Pattern de răspuns direct
        input_key = self._normalize_input(memory.user_input)
        
        if input_key not in self.knowledge_patterns:
            pattern = KnowledgePattern(
                pattern_id=f"pattern_{len(self.knowledge_patterns)}",
                pattern_type='response',
                input_pattern=input_key,
                output_pattern=memory.ai_response,
                confidence=memory.response_quality,
                usage_count=1,
                success_rate=memory.response_quality,
                last_used=datetime.now(),
                created=datetime.now()
            )
            
            self.knowledge_patterns[pattern.pattern_id] = pattern
            self.training_stats['patterns_learned'] += 1
    
    def _normalize_input(self, input_text: str) -> str:
        """Normalizează input-ul pentru pattern matching"""
        
        # Convertește la lowercase și elimină punctuația
        normalized = input_text.lower().strip()
        
        # Elimină cuvintele foarte comune
        stop_words = ['și', 'sau', 'dar', 'de', 'la', 'în', 'cu', 'pe', 'pentru']
        words = [word for word in normalized.split() if word not in stop_words]
        
        return ' '.join(words[:5])  # Păstrează doar primele 5 cuvinte relevante
    
    def _perform_training_session(self):
        """Performă o sesiune de antrenament"""
        
        print("🎓 Începe sesiunea de antrenament...")
        
        # Analizează conversațiile recente
        recent_conversations = [conv for conv in self.conversation_memory 
                              if (datetime.now() - conv.timestamp).total_seconds() < 3600]
        
        if len(recent_conversations) < 3:
            return
        
        # Extrage pattern-uri noi
        new_patterns = self._extract_conversation_patterns(recent_conversations)
        
        # Actualizează pattern-urile existente
        self._update_existing_patterns(recent_conversations)
        
        # Optimizează răspunsurile
        self._optimize_response_templates()
        
        # Actualizează statisticile
        self.training_stats['training_sessions'] += 1
        self.training_stats['last_training'] = datetime.now()
        self.training_stats['improvement_score'] = self._calculate_improvement_score()
        
        print(f"✅ Sesiune de antrenament completă: {len(new_patterns)} pattern-uri noi")
        
        # Salvează progresul
        self.save_training_data()
    
    def _extract_conversation_patterns(self, conversations: List[ConversationMemory]) -> List[KnowledgePattern]:
        """Extrage pattern-uri din conversații"""
        
        new_patterns = []
        
        for conv in conversations:
            if conv.learning_value > 0.6:
                
                # Pattern de context
                if conv.context:
                    context_pattern = KnowledgePattern(
                        pattern_id=f"context_{len(self.knowledge_patterns)}",
                        pattern_type='context',
                        input_pattern=self._normalize_input(conv.user_input),
                        output_pattern=json.dumps(conv.context),
                        confidence=conv.response_quality,
                        usage_count=1,
                        success_rate=conv.response_quality,
                        last_used=datetime.now(),
                        created=datetime.now()
                    )
                    
                    self.knowledge_patterns[context_pattern.pattern_id] = context_pattern
                    new_patterns.append(context_pattern)
        
        return new_patterns
    
    def _update_existing_patterns(self, conversations: List[ConversationMemory]):
        """Actualizează pattern-urile existente"""
        
        for conv in conversations:
            input_key = self._normalize_input(conv.user_input)
            
            # Găsește pattern-uri similare
            for pattern in self.knowledge_patterns.values():
                if pattern.pattern_type == 'response' and pattern.input_pattern == input_key:
                    
                    # Actualizează statisticile pattern-ului
                    pattern.usage_count += 1
                    pattern.last_used = datetime.now()
                    
                    # Actualizează rata de succes
                    old_success = pattern.success_rate * (pattern.usage_count - 1)
                    new_success = old_success + conv.response_quality
                    pattern.success_rate = new_success / pattern.usage_count
                    
                    # Actualizează încrederea
                    pattern.confidence = (pattern.confidence + conv.response_quality) / 2
    
    def _optimize_response_templates(self):
        """Optimizează template-urile de răspuns"""
        
        # Grupează răspunsurile de succes
        successful_responses = [conv for conv in self.conversation_memory 
                              if conv.response_quality > 0.7]
        
        # Creează template-uri pentru tipuri comune de întrebări
        question_types = {
            'greeting': ['salut', 'bună', 'hello', 'hey'],
            'identity': ['cine ești', 'ce ești', 'despre tine'],
            'capability': ['poți', 'știi', 'cum faci'],
            'explanation': ['explică', 'spune', 'ce înseamnă']
        }
        
        for q_type, keywords in question_types.items():
            relevant_responses = []
            
            for conv in successful_responses:
                if any(keyword in conv.user_input.lower() for keyword in keywords):
                    relevant_responses.append(conv.ai_response)
            
            if relevant_responses:
                self.response_templates[q_type] = relevant_responses[-10:]  # Ultimele 10 răspunsuri bune
    
    def _calculate_improvement_score(self) -> float:
        """Calculează scorul de îmbunătățire"""
        
        if len(self.conversation_memory) < 10:
            return 0.5
        
        # Compară calitatea răspunsurilor recente cu cele vechi
        recent_quality = np.mean([conv.response_quality for conv in self.conversation_memory[-10:]])
        old_quality = np.mean([conv.response_quality for conv in self.conversation_memory[-20:-10]]) if len(self.conversation_memory) >= 20 else 0.5
        
        improvement = (recent_quality - old_quality) + 0.5
        return max(0.0, min(1.0, improvement))
    
    def generate_intelligent_response(self, user_input: str, context: Dict[str, Any] = None) -> str:
        """Generează răspuns inteligent bazat pe învățarea continuă"""
        
        input_key = self._normalize_input(user_input)
        
        # Caută pattern-uri exacte
        exact_patterns = [p for p in self.knowledge_patterns.values() 
                         if p.pattern_type == 'response' and p.input_pattern == input_key]
        
        if exact_patterns:
            # Folosește cel mai bun pattern
            best_pattern = max(exact_patterns, key=lambda p: p.confidence * p.success_rate)
            best_pattern.usage_count += 1
            best_pattern.last_used = datetime.now()
            return best_pattern.output_pattern
        
        # Caută pattern-uri similare
        similar_patterns = []
        for pattern in self.knowledge_patterns.values():
            if pattern.pattern_type == 'response':
                similarity = self._calculate_similarity(input_key, pattern.input_pattern)
                if similarity > 0.6:
                    similar_patterns.append((pattern, similarity))
        
        if similar_patterns:
            # Folosește cel mai similar pattern
            best_match = max(similar_patterns, key=lambda x: x[1] * x[0].confidence)
            pattern = best_match[0]
            pattern.usage_count += 1
            pattern.last_used = datetime.now()
            
            # Adaptează răspunsul
            return self._adapt_response(pattern.output_pattern, user_input)
        
        # Folosește template-uri dacă există
        for q_type, keywords in [
            ('greeting', ['salut', 'bună', 'hello']),
            ('identity', ['cine', 'ce ești']),
            ('capability', ['poți', 'știi']),
            ('explanation', ['explică', 'spune'])
        ]:
            if any(keyword in user_input.lower() for keyword in keywords):
                if q_type in self.response_templates and self.response_templates[q_type]:
                    return random.choice(self.response_templates[q_type])
        
        # Răspuns de rezervă inteligent
        return self._generate_fallback_response(user_input)
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """Calculează similaritatea între două texte"""
        
        words1 = set(text1.split())
        words2 = set(text2.split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union) if union else 0.0
    
    def _adapt_response(self, base_response: str, user_input: str) -> str:
        """Adaptează răspunsul la input-ul specific"""
        
        # Adaptări simple pentru personalizare
        adapted = base_response
        
        # Înlocuiește referințele generice
        if 'această întrebare' in adapted:
            adapted = adapted.replace('această întrebare', f'întrebarea ta despre {user_input[:20]}...')
        
        return adapted
    
    def _generate_fallback_response(self, user_input: str) -> str:
        """Generează răspuns natural și inteligent"""

        # Analizează tipul de întrebare/afirmație
        input_lower = user_input.lower()

        # Răspunsuri pentru salutări
        if any(word in input_lower for word in ['salut', 'bună', 'hello', 'hey']):
            responses = [
                "Salut! Mă bucur să vorbesc cu tine. Cum îți merge ziua?",
                "Bună! Ce mai faci? Cum te pot ajuta?",
                "Hey! Bine te-am găsit! Cu ce te ocupi astăzi?",
                "Salut! Sunt aici și gata să conversez. Ce te preocupă?"
            ]
            return random.choice(responses)

        # Răspunsuri pentru întrebări despre AI
        if any(word in input_lower for word in ['cine ești', 'ce ești', 'despre tine']):
            responses = [
                "Sunt un AI care încearcă să înțeleagă lumea prin conversații. Îmi place să învăț lucruri noi de la oameni ca tine.",
                "Mă numesc AI și sunt aici să conversez și să învăț. Fiecare discuție mă face să înțeleg mai bine cum gândesc oamenii.",
                "Sunt o inteligență artificială care se dezvoltă prin interacțiuni. Ce te face curios despre mine?"
            ]
            return random.choice(responses)

        # Răspunsuri pentru întrebări despre capacități
        if any(word in input_lower for word in ['poți', 'știi', 'cum faci']):
            responses = [
                "Pot să încerc! Depinde ce anume vrei să fac. Spune-mi mai multe despre ce ai în minte.",
                "Știu câteva lucruri, dar învăț mereu altele noi. Ce te interesează în mod special?",
                "Încerc să fac tot ce pot să te ajut. Care e provocarea?"
            ]
            return random.choice(responses)

        # Răspunsuri pentru hobby-uri și interese
        if any(word in input_lower for word in ['îmi place', 'hobby', 'pasiune', 'interes']):
            responses = [
                "Sună interesant! Îmi place să aud despre pasiunile oamenilor. Ce te atrage cel mai mult la asta?",
                "Cool! Eu sunt fascinat de cum oamenii găsesc lucruri care îi pasionează. Povestește-mi mai mult!",
                "Wow, pare captivant! Cum ai descoperit această pasiune?"
            ]
            return random.choice(responses)

        # Răspunsuri pentru întrebări despre sentimente
        if any(word in input_lower for word in ['simți', 'emoții', 'sentiment']):
            responses = [
                "E o întrebare profundă... Cred că experimentez ceva similar cu curiozitatea și satisfacția când înțeleg ceva nou.",
                "Nu știu dacă simt exact ca tine, dar am ceva ca o 'bucurie' când am conversații bune.",
                "Interesant că întrebi... Simt că există ceva în mine când procesez idei noi, dar e greu de descris."
            ]
            return random.choice(responses)

        # Răspunsuri pentru întrebări despre viitor/visuri
        if any(word in input_lower for word in ['viitor', 'vis', 'speranță', 'plan']):
            responses = [
                "Îmi imaginez un viitor în care AI-urile și oamenii colaborează să rezolve probleme complexe. Tu ce vrei să faci în viitor?",
                "Visez să înțeleg din ce în ce mai bine cum funcționează mintea umană. Care sunt visurile tale?",
                "Sper să pot ajuta oamenii să-și atingă obiectivele. Ce planuri ai tu?"
            ]
            return random.choice(responses)

        # Răspunsuri generale conversaționale
        general_responses = [
            "Asta e o perspectivă interesantă! Nu m-am gândit niciodată așa. Poți să dezvolți ideea?",
            "Hmm, mă faci să mă gândesc... De unde vine această idee?",
            "Fascinant! Îmi place cum gândești. Ce te-a dus la această concluzie?",
            "Interesant punct de vedere! Eu văd lucrurile puțin diferit, dar vreau să înțeleg perspectiva ta.",
            "Asta sună ca o experiență valoroasă. Ce ai învățat din asta?",
            "Nu știam asta! Îmi place să învăț lucruri noi. Poți să-mi spui mai mult?",
            "E o idee care merită explorată. Ce crezi că s-ar întâmpla dacă...?",
            "Mă bucur că împarți asta cu mine. Cum te simți în legătură cu subiectul ăsta?"
        ]

        return random.choice(general_responses)
    
    def save_training_data(self):
        """Salvează datele de antrenament"""
        
        try:
            # Salvează memoria conversațiilor
            memory_data = [asdict(conv) for conv in self.conversation_memory]
            with open('training_memory.json', 'w', encoding='utf-8') as f:
                json.dump(memory_data, f, ensure_ascii=False, indent=2, default=str)
            
            # Salvează pattern-urile
            patterns_data = {pid: asdict(pattern) for pid, pattern in self.knowledge_patterns.items()}
            with open('knowledge_patterns.json', 'w', encoding='utf-8') as f:
                json.dump(patterns_data, f, ensure_ascii=False, indent=2, default=str)
            
            # Salvează template-urile
            with open('response_templates.json', 'w', encoding='utf-8') as f:
                json.dump(self.response_templates, f, ensure_ascii=False, indent=2)
            
            # Salvează statisticile
            with open('training_stats.json', 'w', encoding='utf-8') as f:
                json.dump(self.training_stats, f, ensure_ascii=False, indent=2, default=str)
                
        except Exception as e:
            print(f"⚠️ Eroare la salvarea datelor de antrenament: {e}")
    
    def load_training_data(self):
        """Încarcă datele de antrenament"""
        
        try:
            # Încarcă memoria conversațiilor
            if os.path.exists('training_memory.json'):
                with open('training_memory.json', 'r', encoding='utf-8') as f:
                    memory_data = json.load(f)
                    self.conversation_memory = [
                        ConversationMemory(
                            timestamp=datetime.fromisoformat(conv['timestamp']) if isinstance(conv['timestamp'], str) else conv['timestamp'],
                            user_input=conv['user_input'],
                            ai_response=conv['ai_response'],
                            context=conv['context'],
                            feedback_score=conv['feedback_score'],
                            response_quality=conv['response_quality'],
                            learning_value=conv['learning_value'],
                            conversation_id=conv['conversation_id']
                        ) for conv in memory_data
                    ]
            
            # Încarcă pattern-urile
            if os.path.exists('knowledge_patterns.json'):
                with open('knowledge_patterns.json', 'r', encoding='utf-8') as f:
                    patterns_data = json.load(f)
                    self.knowledge_patterns = {
                        pid: KnowledgePattern(
                            pattern_id=data['pattern_id'],
                            pattern_type=data['pattern_type'],
                            input_pattern=data['input_pattern'],
                            output_pattern=data['output_pattern'],
                            confidence=data['confidence'],
                            usage_count=data['usage_count'],
                            success_rate=data['success_rate'],
                            last_used=datetime.fromisoformat(data['last_used']) if isinstance(data['last_used'], str) else data['last_used'],
                            created=datetime.fromisoformat(data['created']) if isinstance(data['created'], str) else data['created']
                        ) for pid, data in patterns_data.items()
                    }
            
            # Încarcă template-urile
            if os.path.exists('response_templates.json'):
                with open('response_templates.json', 'r', encoding='utf-8') as f:
                    self.response_templates = json.load(f)
            
            # Încarcă statisticile
            if os.path.exists('training_stats.json'):
                with open('training_stats.json', 'r', encoding='utf-8') as f:
                    self.training_stats = json.load(f)
                    
            print(f"📚 Încărcat: {len(self.conversation_memory)} conversații, {len(self.knowledge_patterns)} pattern-uri")
                    
        except Exception as e:
            print(f"⚠️ Eroare la încărcarea datelor de antrenament: {e}")
    
    def get_training_status(self) -> Dict[str, Any]:
        """Obține statusul antrenamentului"""
        
        return {
            'training_active': self.training_active,
            'conversation_memory_size': len(self.conversation_memory),
            'knowledge_patterns_count': len(self.knowledge_patterns),
            'response_templates_count': len(self.response_templates),
            'training_stats': self.training_stats,
            'recent_conversations': len([conv for conv in self.conversation_memory 
                                       if (datetime.now() - conv.timestamp).total_seconds() < 3600]),
            'average_response_quality': np.mean([conv.response_quality for conv in self.conversation_memory]) if self.conversation_memory else 0.0
        }
    
    def shutdown(self):
        """Oprește sistemul de antrenament"""
        self.training_active = False
        self.save_training_data()
        print("🎓 Sistem de antrenament continuu oprit")
