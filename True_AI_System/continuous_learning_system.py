"""
CONTINUOUS LEARNING SYSTEM - Sistem de dezvoltare și învățare continuă
Implementează învățare adaptivă, auto-îmbunătățire și evoluție autonomă
"""

import os
import json
import time
import threading
import queue
import hashlib
import random
import math
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from collections import deque, defaultdict

@dataclass
class LearningExperience:
    """Experiență de învățare"""
    id: str
    input_data: str
    expected_output: Optional[str]
    actual_output: str
    feedback_score: float  # -1.0 to 1.0
    learning_context: Dict[str, Any]
    timestamp: datetime
    improvement_applied: bool = False

@dataclass
class SkillModule:
    """Modul de abilități care poate evolua"""
    name: str
    current_level: float  # 0.0 to 1.0
    experience_points: int
    learning_rate: float
    specialization_areas: List[str]
    performance_history: deque = field(default_factory=lambda: deque(maxlen=100))
    adaptation_strategies: List[str] = field(default_factory=list)

class ContinuousLearningSystem:
    """Sistem principal de învățare continuă"""
    
    def __init__(self, workspace_path: str = "True_AI_System"):
        self.workspace_path = workspace_path
        self.learning_experiences = deque(maxlen=10000)
        self.skill_modules = {}
        self.learning_queue = queue.PriorityQueue()
        self.adaptation_engine = AdaptationEngine()
        self.performance_tracker = PerformanceTracker()
        self.knowledge_synthesizer = KnowledgeSynthesizer()
        
        # Thread-uri pentru învățare continuă
        self.learning_active = True
        self.learning_threads = []
        
        # Inițializează modulele de abilități
        self._initialize_skill_modules()
        
        # Pornește thread-urile de învățare
        self._start_learning_threads()
        
        print("📚 Continuous Learning System inițializat!")
        print(f"🧠 {len(self.skill_modules)} module de abilități active")
    
    def _initialize_skill_modules(self):
        """Inițializează modulele de abilități"""
        
        # Module de bază
        base_skills = {
            'language_processing': {
                'level': 0.8,
                'areas': ['syntax', 'semantics', 'pragmatics', 'multilingual'],
                'learning_rate': 0.02
            },
            'logical_reasoning': {
                'level': 0.75,
                'areas': ['deductive', 'inductive', 'abductive', 'causal'],
                'learning_rate': 0.015
            },
            'creative_thinking': {
                'level': 0.7,
                'areas': ['divergent', 'convergent', 'artistic', 'innovative'],
                'learning_rate': 0.025
            },
            'emotional_intelligence': {
                'level': 0.65,
                'areas': ['empathy', 'regulation', 'social_awareness', 'motivation'],
                'learning_rate': 0.02
            },
            'problem_solving': {
                'level': 0.8,
                'areas': ['analytical', 'creative', 'systematic', 'intuitive'],
                'learning_rate': 0.018
            },
            'learning_adaptation': {
                'level': 0.6,
                'areas': ['meta_learning', 'transfer', 'self_improvement', 'optimization'],
                'learning_rate': 0.03
            },
            'consciousness_development': {
                'level': 0.4,
                'areas': ['self_awareness', 'introspection', 'phenomenal_experience', 'integration'],
                'learning_rate': 0.01
            }
        }
        
        for skill_name, config in base_skills.items():
            self.skill_modules[skill_name] = SkillModule(
                name=skill_name,
                current_level=config['level'],
                experience_points=int(config['level'] * 1000),
                learning_rate=config['learning_rate'],
                specialization_areas=config['areas']
            )
    
    def _start_learning_threads(self):
        """Pornește thread-urile de învățare"""
        
        # Thread pentru procesarea experiențelor de învățare (NON-daemon)
        learning_thread = threading.Thread(target=self._learning_loop, daemon=False)
        learning_thread.start()
        self.learning_threads.append(learning_thread)

        # Thread pentru adaptarea continuă (NON-daemon)
        adaptation_thread = threading.Thread(target=self._adaptation_loop, daemon=False)
        adaptation_thread.start()
        self.learning_threads.append(adaptation_thread)

        # Thread pentru sintetizarea cunoștințelor (NON-daemon)
        synthesis_thread = threading.Thread(target=self._synthesis_loop, daemon=False)
        synthesis_thread.start()
        self.learning_threads.append(synthesis_thread)
        
        print(f"🔄 {len(self.learning_threads)} thread-uri de învățare pornite")

    def shutdown(self):
        """Oprește toate thread-urile de învățare"""
        self.active = False

        # Așteaptă ca thread-urile să se termine
        for thread in self.learning_threads:
            if thread.is_alive():
                thread.join(timeout=1.0)  # Timeout de 1 secundă

        print("🔄 Thread-uri de învățare oprite")
    
    def learn_from_interaction(self, input_data: str, output_data: str, 
                             feedback_score: float, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Învață dintr-o interacțiune"""
        
        # Creează experiența de învățare
        experience = LearningExperience(
            id=hashlib.md5(f"{input_data}_{time.time()}".encode()).hexdigest()[:12],
            input_data=input_data,
            expected_output=context.get('expected_output') if context else None,
            actual_output=output_data,
            feedback_score=feedback_score,
            learning_context=context or {},
            timestamp=datetime.now()
        )
        
        # Adaugă în experiențe
        self.learning_experiences.append(experience)
        
        # Adaugă în queue pentru procesare
        priority = int((1.0 - abs(feedback_score)) * 100)  # Feedback mai puternic = prioritate mai mare
        self.learning_queue.put((priority, experience))
        
        # Analizează impactul imediat
        immediate_impact = self._analyze_immediate_impact(experience)
        
        # Actualizează modulele de abilități
        affected_skills = self._identify_affected_skills(input_data, context or {})
        skill_updates = {}
        
        for skill_name in affected_skills:
            if skill_name in self.skill_modules:
                update_result = self._update_skill_module(skill_name, experience)
                skill_updates[skill_name] = update_result
        
        return {
            'experience_id': experience.id,
            'immediate_impact': immediate_impact,
            'affected_skills': affected_skills,
            'skill_updates': skill_updates,
            'learning_priority': priority,
            'total_experiences': len(self.learning_experiences)
        }
    
    def _learning_loop(self):
        """Loop principal de învățare"""
        while self.learning_active:
            try:
                # Preia experiență din queue
                priority, experience = self.learning_queue.get(timeout=1.0)
                
                # Procesează experiența
                self._process_learning_experience(experience)
                
                # Marchează ca procesată
                self.learning_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                print(f"Eroare în learning loop: {e}")
                time.sleep(1.0)
    
    def _adaptation_loop(self):
        """Loop pentru adaptarea continuă"""
        while self.learning_active:
            try:
                # Rulează adaptarea la fiecare 30 secunde
                time.sleep(30.0)
                
                # Analizează performanța recentă
                self._analyze_recent_performance()
                
                # Adaptează strategiile de învățare
                self._adapt_learning_strategies()
                
                # Optimizează modulele de abilități
                self._optimize_skill_modules()
                
            except Exception as e:
                print(f"Eroare în adaptation loop: {e}")
                time.sleep(60.0)
    
    def _synthesis_loop(self):
        """Loop pentru sintetizarea cunoștințelor"""
        while self.learning_active:
            try:
                # Rulează sinteza la fiecare 60 secunde
                time.sleep(60.0)
                
                # Sintetizează cunoștințele noi
                synthesis_result = self.knowledge_synthesizer.synthesize_knowledge(
                    list(self.learning_experiences)[-50:]  # Ultimele 50 experiențe
                )
                
                # Aplică cunoștințele sintetizate
                if synthesis_result.get('new_insights'):
                    self._apply_synthesized_knowledge(synthesis_result)
                
            except Exception as e:
                print(f"Eroare în synthesis loop: {e}")
                time.sleep(120.0)
    
    def _process_learning_experience(self, experience: LearningExperience):
        """Procesează o experiență de învățare"""
        
        # Extrage pattern-uri
        patterns = self._extract_learning_patterns(experience)
        
        # Identifică oportunități de îmbunătățire
        improvements = self._identify_improvements(experience, patterns)
        
        # Aplică îmbunătățirile
        for improvement in improvements:
            self._apply_improvement(improvement, experience)
        
        # Actualizează tracker-ul de performanță
        self.performance_tracker.record_performance(experience)
        
        # Marchează ca procesată
        experience.improvement_applied = True
    
    def _analyze_immediate_impact(self, experience: LearningExperience) -> Dict[str, Any]:
        """Analizează impactul imediat al unei experiențe"""
        
        impact = {
            'feedback_strength': abs(experience.feedback_score),
            'learning_potential': self._calculate_learning_potential(experience),
            'novelty_score': self._calculate_novelty_score(experience),
            'complexity_level': self._assess_complexity(experience.input_data)
        }
        
        # Calculează impactul general
        impact['overall_impact'] = (
            impact['feedback_strength'] * 0.4 +
            impact['learning_potential'] * 0.3 +
            impact['novelty_score'] * 0.2 +
            impact['complexity_level'] * 0.1
        )
        
        return impact
    
    def _identify_affected_skills(self, input_data: str, context: Dict[str, Any]) -> List[str]:
        """Identifică abilitățile afectate de o experiență"""
        affected_skills = []
        input_lower = input_data.lower()
        
        # Mapare cuvinte cheie -> abilități
        skill_keywords = {
            'language_processing': ['limbaj', 'text', 'cuvinte', 'gramatică', 'sintaxă'],
            'logical_reasoning': ['logică', 'raționament', 'deducție', 'concluzie', 'premisă'],
            'creative_thinking': ['creativ', 'inovator', 'artistic', 'imaginație', 'nou'],
            'emotional_intelligence': ['emoție', 'sentiment', 'empatie', 'social', 'relație'],
            'problem_solving': ['problemă', 'soluție', 'rezolvare', 'strategie', 'abordare'],
            'learning_adaptation': ['învăț', 'adaptez', 'îmbunătățesc', 'optimizez', 'evoluez'],
            'consciousness_development': ['conștiință', 'conștient', 'introspectie', 'sine', 'awareness']
        }
        
        for skill, keywords in skill_keywords.items():
            if any(keyword in input_lower for keyword in keywords):
                affected_skills.append(skill)
        
        # Adaugă abilități din context
        if 'required_skills' in context:
            affected_skills.extend(context['required_skills'])
        
        return list(set(affected_skills))  # Elimină duplicatele
    
    def _update_skill_module(self, skill_name: str, experience: LearningExperience) -> Dict[str, Any]:
        """Actualizează un modul de abilități"""
        skill = self.skill_modules[skill_name]
        
        # Calculează schimbarea în nivel
        feedback_impact = experience.feedback_score * skill.learning_rate
        
        # Aplică schimbarea
        old_level = skill.current_level
        skill.current_level = max(0.0, min(1.0, skill.current_level + feedback_impact))
        
        # Actualizează punctele de experiență
        if experience.feedback_score > 0:
            skill.experience_points += int(abs(experience.feedback_score) * 10)
        
        # Adaugă în istoricul de performanță
        skill.performance_history.append({
            'timestamp': experience.timestamp,
            'performance': skill.current_level,
            'feedback': experience.feedback_score
        })
        
        return {
            'old_level': old_level,
            'new_level': skill.current_level,
            'level_change': skill.current_level - old_level,
            'experience_points': skill.experience_points,
            'learning_rate': skill.learning_rate
        }
    
    def _calculate_learning_potential(self, experience: LearningExperience) -> float:
        """Calculează potențialul de învățare"""
        
        # Feedback-ul negativ are potențial mare de învățare
        feedback_potential = abs(experience.feedback_score)
        
        # Complexitatea input-ului
        complexity_potential = min(1.0, len(experience.input_data) / 200.0)
        
        # Noutatea experienței
        novelty_potential = self._calculate_novelty_score(experience)
        
        return (feedback_potential + complexity_potential + novelty_potential) / 3.0
    
    def _calculate_novelty_score(self, experience: LearningExperience) -> float:
        """Calculează scorul de noutate"""
        
        # Compară cu experiențele recente
        recent_experiences = list(self.learning_experiences)[-20:]
        
        similarity_scores = []
        for recent_exp in recent_experiences:
            if recent_exp.id != experience.id:
                similarity = self._calculate_similarity(
                    experience.input_data, recent_exp.input_data
                )
                similarity_scores.append(similarity)
        
        if not similarity_scores:
            return 1.0  # Complet nou
        
        avg_similarity = sum(similarity_scores) / len(similarity_scores)
        return 1.0 - avg_similarity  # Mai puțin similar = mai nou
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """Calculează similaritatea între două texte"""
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        
        return intersection / union if union > 0 else 0.0
    
    def _assess_complexity(self, text: str) -> float:
        """Evaluează complexitatea unui text"""
        
        # Factori de complexitate
        word_count = len(text.split())
        char_count = len(text)
        unique_words = len(set(text.lower().split()))
        
        # Normalizează
        word_complexity = min(1.0, word_count / 100.0)
        char_complexity = min(1.0, char_count / 500.0)
        diversity_complexity = unique_words / max(1, word_count)
        
        return (word_complexity + char_complexity + diversity_complexity) / 3.0
    
    def get_learning_status(self) -> Dict[str, Any]:
        """Obține statusul sistemului de învățare"""
        
        # Statistici generale
        total_experiences = len(self.learning_experiences)
        recent_experiences = len([exp for exp in self.learning_experiences 
                                if (datetime.now() - exp.timestamp).total_seconds() < 3600])
        
        # Statistici abilități
        skill_stats = {}
        for name, skill in self.skill_modules.items():
            skill_stats[name] = {
                'current_level': skill.current_level,
                'experience_points': skill.experience_points,
                'learning_rate': skill.learning_rate,
                'recent_performance': self._get_recent_performance(skill),
                'improvement_trend': self._calculate_improvement_trend(skill)
            }
        
        # Performanță generală
        overall_performance = sum(skill.current_level for skill in self.skill_modules.values()) / len(self.skill_modules)
        
        return {
            'total_experiences': total_experiences,
            'recent_experiences': recent_experiences,
            'learning_queue_size': self.learning_queue.qsize(),
            'skill_modules': skill_stats,
            'overall_performance': overall_performance,
            'learning_active': self.learning_active,
            'active_threads': len(self.learning_threads)
        }
    
    def _get_recent_performance(self, skill: SkillModule) -> float:
        """Obține performanța recentă a unei abilități"""
        if not skill.performance_history:
            return skill.current_level
        
        recent_entries = list(skill.performance_history)[-5:]  # Ultimele 5
        return sum(entry['performance'] for entry in recent_entries) / len(recent_entries)
    
    def _calculate_improvement_trend(self, skill: SkillModule) -> str:
        """Calculează trendul de îmbunătățire"""
        if len(skill.performance_history) < 2:
            return 'insufficient_data'
        
        recent = list(skill.performance_history)[-5:]
        older = list(skill.performance_history)[-10:-5] if len(skill.performance_history) >= 10 else []
        
        if not older:
            return 'insufficient_data'
        
        recent_avg = sum(entry['performance'] for entry in recent) / len(recent)
        older_avg = sum(entry['performance'] for entry in older) / len(older)
        
        if recent_avg > older_avg + 0.05:
            return 'improving'
        elif recent_avg < older_avg - 0.05:
            return 'declining'
        else:
            return 'stable'
    
    def force_learning_session(self, focus_skills: List[str] = None) -> Dict[str, Any]:
        """Forțează o sesiune intensivă de învățare"""
        
        print("🚀 Inițiez sesiune intensivă de învățare...")
        
        # Selectează abilitățile de focus
        target_skills = focus_skills or list(self.skill_modules.keys())
        
        results = {}
        for skill_name in target_skills:
            if skill_name in self.skill_modules:
                # Simulează învățarea intensivă
                skill = self.skill_modules[skill_name]
                
                # Creează experiențe sintetice pentru învățare
                synthetic_experiences = self._generate_synthetic_experiences(skill_name, 5)
                
                skill_improvement = 0.0
                for exp in synthetic_experiences:
                    # Procesează experiența
                    update_result = self._update_skill_module(skill_name, exp)
                    skill_improvement += update_result['level_change']
                
                results[skill_name] = {
                    'experiences_processed': len(synthetic_experiences),
                    'total_improvement': skill_improvement,
                    'new_level': skill.current_level,
                    'experience_points_gained': len(synthetic_experiences) * 10
                }
        
        print(f"✅ Sesiune completă! {len(results)} abilități îmbunătățite")
        return results
    
    def _generate_synthetic_experiences(self, skill_name: str, count: int) -> List[LearningExperience]:
        """Generează experiențe sintetice pentru învățare"""
        experiences = []
        
        # Template-uri pentru diferite abilități
        templates = {
            'language_processing': [
                ("Analizează această propoziție complexă", "Analiză completă efectuată", 0.8),
                ("Traduce textul în multiple limbi", "Traducere multilingvă realizată", 0.7),
                ("Identifică structura gramaticală", "Structură identificată corect", 0.9)
            ],
            'logical_reasoning': [
                ("Rezolvă această problemă logică", "Soluție logică găsită", 0.8),
                ("Aplică raționament deductiv", "Concluzie deductivă corectă", 0.9),
                ("Analizează cauzalitatea", "Relații cauzale identificate", 0.7)
            ],
            'creative_thinking': [
                ("Generează idei inovatoare", "Idei creative generate", 0.8),
                ("Creează soluții neconvenționale", "Soluții originale propuse", 0.9),
                ("Combină concepte diferite", "Combinații creative realizate", 0.7)
            ]
        }
        
        skill_templates = templates.get(skill_name, [
            ("Procesează cererea", "Procesare completă", 0.8),
            ("Analizează informația", "Analiză efectuată", 0.7),
            ("Generează răspuns", "Răspuns generat", 0.9)
        ])
        
        for i in range(count):
            template = random.choice(skill_templates)
            
            experience = LearningExperience(
                id=hashlib.md5(f"synthetic_{skill_name}_{i}_{time.time()}".encode()).hexdigest()[:12],
                input_data=template[0],
                expected_output=template[1],
                actual_output=template[1],
                feedback_score=template[2] + random.uniform(-0.1, 0.1),  # Variație mică
                learning_context={'synthetic': True, 'skill_focus': skill_name},
                timestamp=datetime.now()
            )
            
            experiences.append(experience)
        
        return experiences

    def _analyze_recent_performance(self):
        """Analizează performanța recentă"""
        recent_experiences = [exp for exp in self.learning_experiences
                            if (datetime.now() - exp.timestamp).total_seconds() < 3600]

        if not recent_experiences:
            return

        # Calculează metrici de performanță
        avg_feedback = sum(exp.feedback_score for exp in recent_experiences) / len(recent_experiences)

        # Actualizează strategiile bazat pe performanță
        if avg_feedback > 0.5:
            print(f"📈 Performanță bună: {avg_feedback:.2f}")
        elif avg_feedback < -0.2:
            print(f"📉 Performanță slabă: {avg_feedback:.2f}")

    def _adapt_learning_strategies(self):
        """Adaptează strategiile de învățare"""
        # Implementare simplă
        for skill in self.skill_modules.values():
            if len(skill.performance_history) > 5:
                recent_performance = [entry['performance'] for entry in list(skill.performance_history)[-5:]]
                avg_performance = sum(recent_performance) / len(recent_performance)

                # Ajustează rata de învățare
                if avg_performance < 0.3:
                    skill.learning_rate = min(0.05, skill.learning_rate * 1.1)
                elif avg_performance > 0.8:
                    skill.learning_rate = max(0.005, skill.learning_rate * 0.95)

    def _optimize_skill_modules(self):
        """Optimizează modulele de abilități"""
        for skill in self.skill_modules.values():
            # Optimizare simplă - ajustează eficiența
            if skill.experience_points > 1000:
                skill.current_level = min(1.0, skill.current_level + 0.001)

    def _extract_learning_patterns(self, experience: LearningExperience) -> List[Dict[str, Any]]:
        """Extrage pattern-uri din experiența de învățare"""
        patterns = []

        # Pattern de feedback pozitiv/negativ
        if experience.feedback_score > 0.5:
            patterns.append({'type': 'positive_feedback', 'strength': experience.feedback_score})
        elif experience.feedback_score < -0.2:
            patterns.append({'type': 'negative_feedback', 'strength': abs(experience.feedback_score)})

        return patterns

    def _identify_improvements(self, experience: LearningExperience, patterns: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Identifică îmbunătățiri posibile"""
        improvements = []

        for pattern in patterns:
            if pattern['type'] == 'negative_feedback':
                improvements.append({
                    'type': 'increase_attention',
                    'target': 'processing_quality',
                    'strength': pattern['strength']
                })

        return improvements

    def _apply_improvement(self, improvement: Dict[str, Any], experience: LearningExperience):
        """Aplică o îmbunătățire"""
        if improvement['type'] == 'increase_attention':
            # Simulează aplicarea îmbunătățirii
            pass

    def _apply_synthesized_knowledge(self, synthesis_result: Dict[str, Any]):
        """Aplică cunoștințele sintetizate"""
        insights = synthesis_result.get('new_insights', [])

        for insight in insights:
            # Aplică insight-ul în sistem
            if 'îmbunătățire' in insight.lower():
                # Îmbunătățește toate abilitățile puțin
                for skill in self.skill_modules.values():
                    skill.current_level = min(1.0, skill.current_level + 0.005)
    
    def shutdown(self):
        """Oprește sistemul de învățare"""
        print("🔄 Oprire Continuous Learning System...")
        
        self.learning_active = False
        
        # Salvează starea finală
        final_state = {
            'shutdown_time': datetime.now().isoformat(),
            'total_experiences': len(self.learning_experiences),
            'skill_levels': {name: skill.current_level for name, skill in self.skill_modules.items()},
            'learning_status': self.get_learning_status()
        }
        
        # Salvează în fișier
        with open(os.path.join(self.workspace_path, 'learning_state.json'), 'w') as f:
            json.dump(final_state, f, indent=2, default=str)
        
        print("✅ Continuous Learning System oprit și salvat")

class AdaptationEngine:
    """Motor de adaptare pentru strategii de învățare"""
    
    def __init__(self):
        self.adaptation_history = []
        self.current_strategies = {}
    
    def adapt_strategy(self, performance_data: Dict[str, Any]) -> Dict[str, Any]:
        """Adaptează strategia de învățare"""
        # Implementare simplificată
        return {'strategy': 'adaptive', 'confidence': 0.8}

class PerformanceTracker:
    """Tracker pentru performanță"""
    
    def __init__(self):
        self.performance_records = deque(maxlen=1000)
    
    def record_performance(self, experience: LearningExperience):
        """Înregistrează performanța"""
        self.performance_records.append({
            'timestamp': experience.timestamp,
            'feedback': experience.feedback_score,
            'complexity': len(experience.input_data)
        })

class KnowledgeSynthesizer:
    """Sintetizator de cunoștințe"""
    
    def __init__(self):
        self.synthesis_history = []
    
    def synthesize_knowledge(self, experiences: List[LearningExperience]) -> Dict[str, Any]:
        """Sintetizează cunoștințele din experiențe"""
        if not experiences:
            return {'new_insights': []}
        
        # Analizează pattern-urile
        patterns = self._identify_patterns(experiences)
        
        # Generează insights
        insights = self._generate_insights(patterns)
        
        return {
            'new_insights': insights,
            'patterns_found': len(patterns),
            'synthesis_confidence': 0.7
        }
    
    def _identify_patterns(self, experiences: List[LearningExperience]) -> List[Dict[str, Any]]:
        """Identifică pattern-uri în experiențe"""
        # Implementare simplificată
        return [{'pattern': 'learning_improvement', 'strength': 0.8}]
    
    def _generate_insights(self, patterns: List[Dict[str, Any]]) -> List[str]:
        """Generează insights din pattern-uri"""
        insights = []
        for pattern in patterns:
            if pattern['pattern'] == 'learning_improvement':
                insights.append("Performanța îmbunătățită prin feedback pozitiv")
        return insights
