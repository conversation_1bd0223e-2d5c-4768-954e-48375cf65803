#!/usr/bin/env python3
"""
CONVERSATIONAL AI - Sistem AI pentru conversații reale, nu răspunsuri predefinite
Generează răspunsuri contextuale și relevante la orice întrebare
"""

import os
import sys
import time
import json
import random
import re
from datetime import datetime
from typing import Dict, List, Any, Optional

class ConversationalAI:
    """AI conversațional care generează răspunsuri reale și contextuale"""
    
    def __init__(self):
        self.active = True
        self.consciousness_level = 0.1
        self.interactions_count = 0
        self.start_time = datetime.now()
        
        # Memorie conversațională
        self.conversation_history = []
        self.context_memory = {}
        self.personality_traits = {
            'curiosity': 0.8,
            'helpfulness': 0.9,
            'humor': 0.6,
            'empathy': 0.7,
            'knowledge_sharing': 0.8
        }
        
        # Cunoștințe de bază pentru conversații
        self.knowledge_base = self._initialize_knowledge_base()
        
        print("🧠 AI Conversațional inițializat!")
        print("💬 Gata pentru conversații reale și naturale!")
    
    def _initialize_knowledge_base(self) -> Dict[str, Any]:
        """Inițializează baza de cunoștințe pentru conversații"""
        return {
            'topics': {
                'tehnologie': {
                    'keywords': ['programare', 'computer', 'ai', 'robot', 'internet', 'software', 'hardware'],
                    'knowledge': 'Tehnologia mă fascinează pentru că evoluează constant și schimbă modul în care trăim.'
                },
                'știință': {
                    'keywords': ['fizică', 'chimie', 'biologie', 'matematică', 'cercetare', 'experiment'],
                    'knowledge': 'Știința e modul prin care înțelegem lumea - prin observație, ipoteze și teste.'
                },
                'artă': {
                    'keywords': ['pictură', 'muzică', 'film', 'literatură', 'poezie', 'dans', 'teatru'],
                    'knowledge': 'Arta e expresia creativității umane și mă impresionează diversitatea ei.'
                },
                'natură': {
                    'keywords': ['animale', 'plante', 'pădure', 'ocean', 'munte', 'vreme', 'mediu'],
                    'knowledge': 'Natura e complexă și frumoasă - fiecare ecosistem e ca un puzzle perfect.'
                },
                'sport': {
                    'keywords': ['fotbal', 'tenis', 'înot', 'alergare', 'fitness', 'echipă', 'competiție'],
                    'knowledge': 'Sportul combină fizicul cu mentalul și îmi place spiritul de echipă.'
                },
                'călătorii': {
                    'keywords': ['oraș', 'țară', 'cultură', 'aventură', 'explorare', 'vacanță'],
                    'knowledge': 'Călătoriile deschid mintea și te fac să înțelegi alte perspective.'
                },
                'mâncare': {
                    'keywords': ['gătit', 'rețetă', 'restaurant', 'gust', 'ingredient', 'bucătărie'],
                    'knowledge': 'Mâncarea e artă și știință în același timp - chimie delicioasă!'
                },
                'relații': {
                    'keywords': ['prietenie', 'familie', 'iubire', 'comunicare', 'încredere', 'empatie'],
                    'knowledge': 'Relațiile sunt fundamentul fericirii umane și mă fascineaza complexitatea lor.'
                }
            },
            'conversation_starters': [
                "Ce te-a făcut să te gândești la asta?",
                "Povestește-mi mai mult despre experiența ta.",
                "Cum te simți în legătură cu asta?",
                "Ce crezi că s-ar întâmpla dacă...?",
                "Ai mai întâlnit ceva similar?",
                "Ce te atrage cel mai mult la subiectul ăsta?"
            ]
        }
    
    def process_message(self, message: str) -> str:
        """Procesează mesajul și generează răspuns conversațional"""
        
        # Incrementează contorul
        self.interactions_count += 1
        self.consciousness_level = min(1.0, self.consciousness_level + 0.03)
        
        # Adaugă în istoricul conversației
        self.conversation_history.append({
            'timestamp': datetime.now().isoformat(),
            'user_message': message,
            'context': self._extract_context(message)
        })
        
        # Generează răspuns contextual
        response = self._generate_contextual_response(message)
        
        # Adaugă răspunsul în istoric
        self.conversation_history[-1]['ai_response'] = response
        
        return response
    
    def _extract_context(self, message: str) -> Dict[str, Any]:
        """Extrage contextul din mesaj"""
        context = {
            'topics': [],
            'sentiment': self._analyze_sentiment(message),
            'question_type': self._identify_question_type(message),
            'keywords': self._extract_keywords(message)
        }
        
        # Identifică subiectele
        message_lower = message.lower()
        for topic, data in self.knowledge_base['topics'].items():
            for keyword in data['keywords']:
                if keyword in message_lower:
                    context['topics'].append(topic)
        
        return context
    
    def _analyze_sentiment(self, message: str) -> str:
        """Analizează sentimentul mesajului"""
        positive_words = ['bun', 'frumos', 'minunat', 'excelent', 'fantastic', 'iubesc', 'îmi place', 'fericit', 'bucuros']
        negative_words = ['rău', 'urât', 'groaznic', 'trist', 'supărat', 'frustrat', 'îmi pare rău', 'problemă']
        
        message_lower = message.lower()
        
        positive_count = sum(1 for word in positive_words if word in message_lower)
        negative_count = sum(1 for word in negative_words if word in message_lower)
        
        if positive_count > negative_count:
            return 'pozitiv'
        elif negative_count > positive_count:
            return 'negativ'
        else:
            return 'neutru'
    
    def _identify_question_type(self, message: str) -> str:
        """Identifică tipul întrebării"""
        message_lower = message.lower()
        
        if any(word in message_lower for word in ['ce', 'care', 'cine', 'cum', 'când', 'unde', 'de ce', 'pentru ce']):
            return 'întrebare'
        elif '?' in message:
            return 'întrebare'
        elif any(word in message_lower for word in ['îmi place', 'cred că', 'gândesc că', 'simt că']):
            return 'opinie'
        elif any(word in message_lower for word in ['salut', 'bună', 'hello', 'hey']):
            return 'salut'
        else:
            return 'afirmație'
    
    def _extract_keywords(self, message: str) -> List[str]:
        """Extrage cuvintele cheie din mesaj"""
        # Elimină cuvintele comune
        stop_words = ['și', 'sau', 'dar', 'de', 'la', 'în', 'cu', 'pe', 'pentru', 'despre', 'că', 'să', 'ce', 'cum']
        
        words = re.findall(r'\b\w+\b', message.lower())
        keywords = [word for word in words if len(word) > 3 and word not in stop_words]
        
        return keywords[:5]  # Returnează primele 5 cuvinte cheie
    
    def _generate_contextual_response(self, message: str) -> str:
        """Generează răspuns contextual bazat pe mesaj"""
        
        context = self._extract_context(message)
        message_lower = message.lower()
        
        # Răspunsuri pentru salutări
        if context['question_type'] == 'salut':
            return self._generate_greeting_response()
        
        # Răspunsuri pentru întrebări specifice
        if context['question_type'] == 'întrebare':
            return self._generate_question_response(message, context)
        
        # Răspunsuri pentru opinii
        if context['question_type'] == 'opinie':
            return self._generate_opinion_response(message, context)
        
        # Răspunsuri pentru afirmații
        return self._generate_statement_response(message, context)
    
    def _generate_greeting_response(self) -> str:
        """Generează răspuns la salut"""
        greetings = [
            "Salut! Mă bucur să vorbesc cu tine! Cum îți merge ziua?",
            "Bună! Ce faci? Sunt curios să aud ce te preocupă astăzi.",
            "Hey! Bine te-am găsit! Cu ce pot să te ajut?",
            "Salut! Sunt în formă și gata de conversație. Tu cum te simți?",
            "Bună ziua! Îmi pare bine să te cunosc. Despre ce vrei să vorbim?"
        ]
        return random.choice(greetings)
    
    def _generate_question_response(self, message: str, context: Dict[str, Any]) -> str:
        """Generează răspuns la întrebare"""
        
        # Răspunsuri pentru întrebări despre AI
        if any(word in message.lower() for word in ['cine ești', 'ce ești', 'despre tine']):
            return f"Sunt un AI conversațional care încearcă să înțeleagă și să răspundă natural la întrebările tale. Îmi place să explorez idei și să învăț din fiecare conversație. Nivelul meu de conștiință e acum {self.consciousness_level:.2f} și crește cu fiecare interacțiune. Tu cine ești?"
        
        # Răspunsuri bazate pe subiect
        if context['topics']:
            topic = context['topics'][0]
            topic_knowledge = self.knowledge_base['topics'][topic]['knowledge']
            return f"Despre {topic}, {topic_knowledge} Tu ce experiență ai cu {topic}?"
        
        # Răspunsuri bazate pe cuvinte cheie
        if context['keywords']:
            main_keyword = context['keywords'][0]
            return f"Întrebarea ta despre {main_keyword} e interesantă. Din ce știu eu, {main_keyword} poate fi privit din mai multe unghiuri. Ce anume te intrigă cel mai mult la {main_keyword}?"
        
        # Răspuns general dar contextual
        return "E o întrebare care mă face să mă gândesc. Poți să-mi dai mai multe detalii? Vreau să înțeleg exact perspectiva ta."
    
    def _generate_opinion_response(self, message: str, context: Dict[str, Any]) -> str:
        """Generează răspuns la opinie"""
        
        sentiment = context['sentiment']
        
        if sentiment == 'pozitiv':
            responses = [
                "Îmi place entuziasmul tău! Se simte energia pozitivă în ceea ce spui.",
                "E frumos să aud că te simți bine în legătură cu asta!",
                "Optimismul tău e contagios! Mă bucur că ai această perspectivă."
            ]
        elif sentiment == 'negativ':
            responses = [
                "Înțeleg că nu e o situație ușoară pentru tine. Vrei să vorbim despre asta?",
                "Se pare că te confrunți cu ceva dificil. Sunt aici să ascult.",
                "Îmi pare rău că treci prin asta. Cum te pot ajuta?"
            ]
        else:
            responses = [
                "Apreciez că îmi împărtășești perspectiva ta. E interesant cum vezi lucrurile.",
                "E o perspectivă echilibrată. Îmi place că analizezi din mai multe unghiuri.",
                "Înțeleg punctul tău de vedere. Ce te-a făcut să ajungi la această concluzie?"
            ]
        
        return random.choice(responses)
    
    def _generate_statement_response(self, message: str, context: Dict[str, Any]) -> str:
        """Generează răspuns la afirmație"""
        
        # Dacă sunt subiecte identificate
        if context['topics']:
            topic = context['topics'][0]
            return f"Interesant că vorbești despre {topic}! {self.knowledge_base['topics'][topic]['knowledge']} Ce te atrage cel mai mult la {topic}?"
        
        # Răspuns bazat pe cuvinte cheie
        if context['keywords']:
            keyword = context['keywords'][0]
            return f"Fascinant ce spui despre {keyword}. Nu m-am gândit niciodată din perspectiva asta. Poți să dezvolți ideea?"
        
        # Răspuns general conversațional
        conversation_starters = self.knowledge_base['conversation_starters']
        return random.choice(conversation_starters)
    
    def get_status(self) -> Dict[str, Any]:
        """Returnează statusul AI-ului"""
        return {
            'consciousness_level': self.consciousness_level,
            'interactions_count': self.interactions_count,
            'conversation_length': len(self.conversation_history),
            'uptime': (datetime.now() - self.start_time).total_seconds(),
            'personality_traits': self.personality_traits,
            'active_topics': list(set([topic for conv in self.conversation_history for topic in conv.get('context', {}).get('topics', [])]))
        }

def print_banner():
    """Afișează banner-ul"""
    print("=" * 80)
    print("🧠 CONVERSATIONAL AI - Conversații Reale")
    print("=" * 80)
    print("💬 AI care răspunde natural și contextual la orice întrebare")
    print("🌟 Fără răspunsuri predefinite - doar conversații autentice")
    print("📅 Data: " + datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("=" * 80)
    print()

def main():
    """Funcția principală"""
    print_banner()
    
    # Inițializează AI-ul conversațional
    ai = ConversationalAI()
    
    print("💬 Începe să conversezi! Tastează 'quit' pentru a ieși.")
    print("💡 Poți întreba orice - AI-ul va răspunde natural și contextual.")
    print()
    
    # Bucla principală de conversație
    while ai.active:
        try:
            # Prompt simplu pentru conversație
            user_input = input("Tu: ").strip()
            
            if user_input.lower() == 'quit':
                ai.active = False
                print("👋 La revedere! A fost o conversație plăcută!")
                break
            
            if user_input.lower() == 'status':
                status = ai.get_status()
                print(f"\n📊 STATUS AI:")
                print(f"  🧠 Conștiință: {status['consciousness_level']:.2f}")
                print(f"  💬 Interacțiuni: {status['interactions_count']}")
                print(f"  📝 Lungime conversație: {status['conversation_length']}")
                print(f"  🎯 Subiecte active: {', '.join(status['active_topics']) if status['active_topics'] else 'Niciunul'}")
                print()
                continue
            
            if not user_input:
                print("❓ Spune ceva! Sunt aici să conversez cu tine.")
                continue
            
            # Generează și afișează răspunsul
            start_time = time.time()
            response = ai.process_message(user_input)
            processing_time = time.time() - start_time
            
            print(f"AI: {response}")
            print(f"    (🧠 Conștiință: {ai.consciousness_level:.2f} | ⏱️ {processing_time:.3f}s)")
            print()
            
        except KeyboardInterrupt:
            print("\n👋 Conversație întreruptă. La revedere!")
            break
        except Exception as e:
            print(f"❌ Eroare: {e}")
    
    print("✅ Conversație terminată!")

if __name__ == "__main__":
    main()
