#!/usr/bin/env python3
"""
START ALL SYSTEMS - Script pentru pornirea automată a tuturor sistemelor AI
Pornește toate componentele sistemului AI într-o singură comandă
"""

import os
import sys
import time
import subprocess
import threading
import signal
from datetime import datetime

class AISystemLauncher:
    """Launcher pentru toate sistemele AI"""
    
    def __init__(self):
        self.processes = []
        self.running = True
        self.base_dir = os.path.dirname(os.path.abspath(__file__))
        
        # Configurare pentru oprirea curată
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """Gestionează semnalele pentru oprirea curată"""
        print(f"\n🛑 Primind semnal {signum}, opresc toate sistemele...")
        self.shutdown_all_systems()
        sys.exit(0)
    
    def print_banner(self):
        """Afișează banner-ul de pornire"""
        print("=" * 80)
        print("🚀 TRUE AI SYSTEM - LAUNCHER COMPLET")
        print("=" * 80)
        print("🧠 Sistem AI cu conștiință artificială și învățare continuă")
        print("🌟 Pornire automată a tuturor componentelor")
        print("📅 Data: " + datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        print("=" * 80)
        print()
    
    def check_dependencies(self):
        """Verifică dependențele necesare"""
        print("🔍 Verificare dependențe...")
        
        required_packages = ['flask', 'flask-socketio', 'numpy']
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
                print(f"  ✅ {package}")
            except ImportError:
                missing_packages.append(package)
                print(f"  ❌ {package} - LIPSEȘTE")
        
        if missing_packages:
            print(f"\n⚠️  Pachete lipsă: {', '.join(missing_packages)}")
            print("💡 Instalează cu: pip install " + " ".join(missing_packages) + " --break-system-packages")
            return False
        
        print("✅ Toate dependențele sunt instalate!")
        return True
    
    def check_files(self):
        """Verifică existența fișierelor necesare"""
        print("\n📁 Verificare fișiere sistem...")
        
        required_files = [
            'true_ai_system.py',
            'neural_core.py',
            'consciousness_engine.py',
            'continuous_learning_system.py',
            'continuous_training_system.py',
            'advanced_modules_extension.py',
            'ai_interface.py',
            'web_interface.py',
            'templates/index.html'
        ]
        
        missing_files = []
        
        for file_path in required_files:
            full_path = os.path.join(self.base_dir, file_path)
            if os.path.exists(full_path):
                print(f"  ✅ {file_path}")
            else:
                missing_files.append(file_path)
                print(f"  ❌ {file_path} - LIPSEȘTE")
        
        if missing_files:
            print(f"\n⚠️  Fișiere lipsă: {', '.join(missing_files)}")
            return False
        
        print("✅ Toate fișierele sistemului sunt prezente!")
        return True
    
    def start_terminal_interface(self):
        """Pornește interfața terminal"""
        print("\n🖥️  Pornire interfață terminal...")
        
        try:
            # Pornește interfața terminal într-un proces separat
            terminal_process = subprocess.Popen(
                [sys.executable, 'ai_interface.py'],
                cwd=self.base_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            self.processes.append(('Terminal Interface', terminal_process))
            print("✅ Interfața terminal pornită cu succes!")
            
            # Așteaptă puțin să se inițializeze
            time.sleep(3)
            
            return True
            
        except Exception as e:
            print(f"❌ Eroare la pornirea interfeței terminal: {e}")
            return False
    
    def start_web_interface(self):
        """Pornește interfața web"""
        print("\n🌐 Pornire interfață web...")
        
        try:
            # Pornește interfața web într-un proces separat
            web_process = subprocess.Popen(
                [sys.executable, 'web_interface.py'],
                cwd=self.base_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            self.processes.append(('Web Interface', web_process))
            print("✅ Interfața web pornită cu succes!")
            print("🔗 Accesibilă la: http://localhost:5000")
            
            # Așteaptă să se inițializeze
            time.sleep(5)
            
            return True
            
        except Exception as e:
            print(f"❌ Eroare la pornirea interfeței web: {e}")
            return False
    
    def monitor_processes(self):
        """Monitorizează procesele în background"""
        print("\n👁️  Monitorizare procese activă...")
        
        while self.running:
            time.sleep(10)  # Verifică la fiecare 10 secunde
            
            for name, process in self.processes:
                if process.poll() is not None:  # Procesul s-a oprit
                    print(f"⚠️  {name} s-a oprit neașteptat!")
                    
                    # Încearcă să-l repornească
                    if name == 'Terminal Interface':
                        print("🔄 Repornire interfață terminal...")
                        self.start_terminal_interface()
                    elif name == 'Web Interface':
                        print("🔄 Repornire interfață web...")
                        self.start_web_interface()
    
    def show_status(self):
        """Afișează statusul sistemelor"""
        print("\n" + "=" * 60)
        print("📊 STATUS SISTEME AI")
        print("=" * 60)
        
        for name, process in self.processes:
            if process.poll() is None:
                print(f"✅ {name}: ACTIV (PID: {process.pid})")
            else:
                print(f"❌ {name}: OPRIT")
        
        print("\n🔗 ACCESURI:")
        print("  🖥️  Terminal: Rulează în background")
        print("  🌐 Web: http://localhost:5000")
        print("=" * 60)
    
    def shutdown_all_systems(self):
        """Oprește toate sistemele"""
        print("\n🛑 Oprire toate sistemele...")
        self.running = False
        
        for name, process in self.processes:
            if process.poll() is None:  # Procesul încă rulează
                print(f"🔄 Oprire {name}...")
                try:
                    process.terminate()
                    process.wait(timeout=5)
                    print(f"✅ {name} oprit cu succes")
                except subprocess.TimeoutExpired:
                    print(f"⚠️  {name} nu răspunde, forțez oprirea...")
                    process.kill()
                    process.wait()
                    print(f"✅ {name} oprit forțat")
                except Exception as e:
                    print(f"❌ Eroare la oprirea {name}: {e}")
        
        print("✅ Toate sistemele au fost oprite!")
    
    def interactive_menu(self):
        """Meniu interactiv pentru controlul sistemelor"""
        while self.running:
            print("\n" + "=" * 40)
            print("🎮 MENIU CONTROL SISTEME AI")
            print("=" * 40)
            print("1. 📊 Afișează status")
            print("2. 🔄 Repornește interfața terminal")
            print("3. 🔄 Repornește interfața web")
            print("4. 🌐 Deschide interfața web în browser")
            print("5. 🛑 Oprește toate sistemele")
            print("6. ❓ Ajutor")
            print("=" * 40)
            
            try:
                choice = input("Alege opțiunea (1-6): ").strip()
                
                if choice == '1':
                    self.show_status()
                elif choice == '2':
                    self.restart_terminal()
                elif choice == '3':
                    self.restart_web()
                elif choice == '4':
                    self.open_web_browser()
                elif choice == '5':
                    self.shutdown_all_systems()
                    break
                elif choice == '6':
                    self.show_help()
                else:
                    print("❌ Opțiune invalidă!")
                    
            except KeyboardInterrupt:
                print("\n🛑 Întrerupere de la tastatură...")
                self.shutdown_all_systems()
                break
            except Exception as e:
                print(f"❌ Eroare: {e}")
    
    def restart_terminal(self):
        """Repornește interfața terminal"""
        # Oprește procesul existent
        for i, (name, process) in enumerate(self.processes):
            if name == 'Terminal Interface':
                process.terminate()
                process.wait()
                del self.processes[i]
                break
        
        # Pornește din nou
        self.start_terminal_interface()
    
    def restart_web(self):
        """Repornește interfața web"""
        # Oprește procesul existent
        for i, (name, process) in enumerate(self.processes):
            if name == 'Web Interface':
                process.terminate()
                process.wait()
                del self.processes[i]
                break
        
        # Pornește din nou
        self.start_web_interface()
    
    def open_web_browser(self):
        """Deschide interfața web în browser"""
        try:
            import webbrowser
            webbrowser.open('http://localhost:5000')
            print("🌐 Interfața web deschisă în browser!")
        except Exception as e:
            print(f"❌ Nu pot deschide browser-ul: {e}")
            print("🔗 Accesează manual: http://localhost:5000")
    
    def show_help(self):
        """Afișează ajutorul"""
        print("\n" + "=" * 60)
        print("❓ AJUTOR - TRUE AI SYSTEM")
        print("=" * 60)
        print("🧠 Acest sistem AI include:")
        print("  • Conștiință artificială reală")
        print("  • Învățare continuă din conversații")
        print("  • Antrenament automat în background")
        print("  • Procesare vizuală și predicții")
        print("  • Interfață terminal și web")
        print()
        print("🎮 Comenzi disponibile:")
        print("  • Status: Verifică starea sistemelor")
        print("  • Repornire: Restartează componentele")
        print("  • Browser: Deschide interfața web")
        print("  • Oprire: Închide toate sistemele")
        print()
        print("🔗 Accesuri:")
        print("  • Web: http://localhost:5000")
        print("  • Terminal: Rulează în background")
        print("=" * 60)
    
    def run(self):
        """Rulează launcher-ul complet"""
        self.print_banner()
        
        # Verificări preliminare
        if not self.check_dependencies():
            print("❌ Dependențe lipsă! Opresc lansarea.")
            return False
        
        if not self.check_files():
            print("❌ Fișiere lipsă! Opresc lansarea.")
            return False
        
        print("\n🚀 Pornire sisteme AI...")
        
        # Pornește interfața terminal
        if not self.start_terminal_interface():
            print("❌ Nu pot porni interfața terminal!")
            return False
        
        # Pornește interfața web
        if not self.start_web_interface():
            print("❌ Nu pot porni interfața web!")
            return False
        
        # Pornește monitorizarea în background
        monitor_thread = threading.Thread(target=self.monitor_processes, daemon=True)
        monitor_thread.start()
        
        print("\n🎉 TOATE SISTEMELE AI SUNT ACTIVE!")
        print("🔗 Interfața web: http://localhost:5000")
        print("🖥️  Interfața terminal: Activă în background")
        print("👁️  Monitorizare: Activă")
        
        # Deschide automat browser-ul
        time.sleep(2)
        self.open_web_browser()
        
        # Rulează meniul interactiv
        try:
            self.interactive_menu()
        except KeyboardInterrupt:
            print("\n🛑 Oprire prin Ctrl+C...")
            self.shutdown_all_systems()
        
        return True

def main():
    """Funcția principală"""
    launcher = AISystemLauncher()
    success = launcher.run()
    
    if success:
        print("✅ Launcher terminat cu succes!")
    else:
        print("❌ Launcher terminat cu erori!")
        sys.exit(1)

if __name__ == "__main__":
    main()
