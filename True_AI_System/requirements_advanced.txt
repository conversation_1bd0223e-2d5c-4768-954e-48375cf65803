# INTEGRATED AI SYSTEM - Advanced Requirements
# Dependențe pentru sistemul AI complet cu LFM2-1.2B

# Core AI Engine - ESSENTIAL
llama-cpp-python>=0.2.20

# Web Framework and Real-time Communication - ESSENTIAL
Flask>=2.3.0
Flask-SocketIO>=5.3.0
eventlet>=0.33.0
python-socketio>=5.8.0

# Data Processing and Analysis - ESSENTIAL
numpy>=1.21.0
pandas>=1.5.0
scipy>=1.7.0

# System Monitoring and Performance - ESSENTIAL
psutil>=5.9.0

# HTTP and Networking - ESSENTIAL
requests>=2.28.0
urllib3>=1.26.0
websocket-client>=1.6.0

# JSON and Data Serialization - ESSENTIAL
jsonschema>=4.17.0

# Date and Time Handling - ESSENTIAL
python-dateutil>=2.8.0

# Text Processing and NLP - RECOMMENDED
regex>=2022.10.31
nltk>=3.8
textblob>=0.17.1

# Mathematical and Scientific Computing - RECOMMENDED
scikit-learn>=1.1.0
matplotlib>=3.5.0
seaborn>=0.11.0

# Logging and Configuration - RECOMMENDED
colorlog>=6.7.0
python-dotenv>=1.0.0

# Memory and Performance Optimization - OPTIONAL
memory-profiler>=0.60.0

# Development and Testing Tools - OPTIONAL
pytest>=7.2.0
black>=22.10.0
flake8>=6.0.0

# Optional GPU Support (uncomment if needed)
# torch>=2.0.0
# transformers>=4.36.0

# Optional Advanced Features (uncomment if needed)
# GPUtil>=1.4.0
# nvidia-ml-py3>=7.352.0
