# Advanced AI System Requirements
# LFM2-1.2B Integration with Continuous Learning

# Core ML/AI Libraries
torch>=2.0.0
transformers>=4.36.0
accelerate>=0.20.0
bitsandbytes>=0.41.0
datasets>=2.14.0
peft>=0.6.0
trl>=0.7.0

# Web Interface
flask>=2.0.0
flask-socketio>=5.0.0
python-socketio>=5.0.0

# Data Processing
numpy>=1.21.0
pandas>=1.3.0
scikit-learn>=1.0.0

# Utilities
tqdm>=4.64.0
psutil>=5.8.0
requests>=2.25.0

# Optional GPU Acceleration
# nvidia-ml-py3>=7.352.0  # Uncomment for NVIDIA GPU monitoring

# Development Tools (Optional)
# jupyter>=1.0.0
# matplotlib>=3.5.0
# seaborn>=0.11.0
