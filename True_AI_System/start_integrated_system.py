#!/usr/bin/env python3
"""
START INTEGRATED SYSTEM - Script principal pentru pornirea sistemului AI complet
Pornește toate componentele: model, antrenament, învățare, interfețe web
"""

import os
import sys
import time
import subprocess
import threading
import signal
import logging
from datetime import datetime

# Configurare logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('system_startup.log'),
        logging.StreamHandler()
    ]
)

class IntegratedSystemLauncher:
    """Launcher pentru sistemul AI integrat complet"""
    
    def __init__(self):
        self.processes = {}
        self.is_running = False
        self.base_dir = os.path.dirname(os.path.abspath(__file__))
        
        # Configurări pornire
        self.config = {
            "start_terminal_interface": True,
            "start_web_interface": True,
            "start_integrated_dashboard": True,
            "auto_open_browser": True,
            "check_dependencies": True,
            "create_backups": True
        }
        
        # Porturi pentru interfețe
        self.ports = {
            "lfm2_terminal": None,  # Terminal interface
            "lfm2_web": 5005,       # Basic web interface
            "integrated_dashboard": 5010  # Advanced dashboard
        }
        
        logging.info("🚀 Integrated System Launcher inițializat")
    
    def check_model_file(self) -> bool:
        """Verifică dacă modelul LFM2-1.2B este descărcat"""
        model_path = os.path.join(self.base_dir, "LFM2-1.2B-Q4_K_M.gguf")
        
        if os.path.exists(model_path):
            file_size = os.path.getsize(model_path)
            expected_size = 730893248  # 731 MB
            
            if file_size >= expected_size * 0.95:  # 95% din mărimea așteptată
                logging.info(f"✅ Model LFM2-1.2B găsit și complet: {file_size / 1024 / 1024:.1f} MB")
                return True
            else:
                logging.warning(f"⚠️ Model LFM2-1.2B incomplet: {file_size / 1024 / 1024:.1f} MB din {expected_size / 1024 / 1024:.1f} MB")
                return False
        else:
            logging.error("❌ Model LFM2-1.2B nu a fost găsit!")
            return False
    
    def check_dependencies(self) -> bool:
        """Verifică dependențele necesare"""
        if not self.config["check_dependencies"]:
            return True
        
        logging.info("🔍 Verificare dependențe...")
        
        required_packages = [
            "flask", "flask-socketio", "llama-cpp-python", 
            "numpy", "psutil", "requests"
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package.replace("-", "_"))
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            logging.error(f"❌ Pachete lipsă: {', '.join(missing_packages)}")
            logging.info("💡 Rulează: pip install -r requirements_advanced.txt")
            return False
        
        logging.info("✅ Toate dependențele sunt instalate")
        return True
    
    def create_backup(self):
        """Creează backup pentru datele importante"""
        if not self.config["create_backups"]:
            return
        
        try:
            backup_dir = os.path.join(self.base_dir, "backups")
            os.makedirs(backup_dir, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"system_backup_{timestamp}"
            
            # Lista fișierelor importante de backup
            important_files = [
                "continuous_training_stats.json",
                "training_history.json",
                "integrated_system_stats.json",
                "components_status.json",
                "system_config.json",
                "learning_state.json",
                "training_memory.json",
                "session_history.json"
            ]
            
            backed_up = 0
            for file_name in important_files:
                file_path = os.path.join(self.base_dir, file_name)
                if os.path.exists(file_path):
                    backup_path = os.path.join(backup_dir, f"{backup_name}_{file_name}")
                    subprocess.run(["cp", file_path, backup_path], check=True)
                    backed_up += 1
            
            if backed_up > 0:
                logging.info(f"💾 Backup creat: {backed_up} fișiere salvate în {backup_dir}")
            
        except Exception as e:
            logging.warning(f"⚠️ Eroare creare backup: {e}")
    
    def start_terminal_interface(self):
        """Pornește interfața terminal"""
        if not self.config["start_terminal_interface"]:
            return
        
        try:
            logging.info("🖥️ Pornire interfață terminal...")
            
            # Pornește sistemul GGUF în terminal
            process = subprocess.Popen(
                [sys.executable, "lfm2_gguf_system.py"],
                cwd=self.base_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            self.processes["terminal"] = process
            logging.info("✅ Interfață terminal pornită")
            
        except Exception as e:
            logging.error(f"❌ Eroare pornire interfață terminal: {e}")
    
    def start_web_interface(self):
        """Pornește interfața web de bază"""
        if not self.config["start_web_interface"]:
            return
        
        try:
            logging.info("🌐 Pornire interfață web de bază...")
            
            process = subprocess.Popen(
                [sys.executable, "lfm2_gguf_web.py"],
                cwd=self.base_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            self.processes["web_basic"] = process
            logging.info(f"✅ Interfață web de bază pornită pe port {self.ports['lfm2_web']}")
            
        except Exception as e:
            logging.error(f"❌ Eroare pornire interfață web de bază: {e}")
    
    def start_integrated_dashboard(self):
        """Pornește dashboard-ul integrat avansat"""
        if not self.config["start_integrated_dashboard"]:
            return
        
        try:
            logging.info("🚀 Pornire dashboard integrat avansat...")
            
            process = subprocess.Popen(
                [sys.executable, "integrated_web_interface.py"],
                cwd=self.base_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            self.processes["dashboard"] = process
            logging.info(f"✅ Dashboard integrat pornit pe port {self.ports['integrated_dashboard']}")
            
        except Exception as e:
            logging.error(f"❌ Eroare pornire dashboard integrat: {e}")
    
    def open_browser_interfaces(self):
        """Deschide interfețele în browser"""
        if not self.config["auto_open_browser"]:
            return
        
        try:
            time.sleep(5)  # Așteaptă ca serverele să pornească
            
            urls = []
            
            if self.config["start_web_interface"]:
                urls.append(f"http://localhost:{self.ports['lfm2_web']}")
            
            if self.config["start_integrated_dashboard"]:
                urls.append(f"http://localhost:{self.ports['integrated_dashboard']}")
            
            for url in urls:
                try:
                    subprocess.run(["xdg-open", url], check=True)
                    logging.info(f"🌐 Browser deschis pentru: {url}")
                except:
                    logging.info(f"💡 Deschide manual în browser: {url}")
            
        except Exception as e:
            logging.warning(f"⚠️ Eroare deschidere browser: {e}")
    
    def monitor_processes(self):
        """Monitorizează procesele pornite"""
        while self.is_running:
            try:
                for name, process in list(self.processes.items()):
                    if process.poll() is not None:
                        logging.warning(f"⚠️ Procesul {name} s-a oprit neașteptat")
                        # Încearcă repornirea automată
                        self.restart_process(name)
                
                time.sleep(30)  # Verifică la 30 secunde
                
            except Exception as e:
                logging.error(f"Eroare monitorizare procese: {e}")
                time.sleep(60)
    
    def restart_process(self, process_name: str):
        """Repornește un proces oprit"""
        try:
            logging.info(f"🔄 Repornire proces: {process_name}")
            
            if process_name == "terminal":
                self.start_terminal_interface()
            elif process_name == "web_basic":
                self.start_web_interface()
            elif process_name == "dashboard":
                self.start_integrated_dashboard()
            
        except Exception as e:
            logging.error(f"❌ Eroare repornire proces {process_name}: {e}")
    
    def start_all_systems(self):
        """Pornește toate sistemele"""
        logging.info("🚀 PORNIRE SISTEM AI INTEGRAT COMPLET")
        logging.info("=" * 60)
        
        # 1. Verificări preliminare
        if not self.check_model_file():
            logging.error("❌ Modelul LFM2-1.2B nu este disponibil!")
            return False
        
        if not self.check_dependencies():
            logging.error("❌ Dependențe lipsă!")
            return False
        
        # 2. Creează backup
        self.create_backup()
        
        # 3. Pornește componentele
        self.is_running = True
        
        # Pornește interfața terminal
        self.start_terminal_interface()
        time.sleep(2)
        
        # Pornește interfața web de bază
        self.start_web_interface()
        time.sleep(2)
        
        # Pornește dashboard-ul integrat
        self.start_integrated_dashboard()
        time.sleep(3)
        
        # 4. Deschide browser-ul
        browser_thread = threading.Thread(target=self.open_browser_interfaces, daemon=True)
        browser_thread.start()
        
        # 5. Pornește monitorizarea
        monitor_thread = threading.Thread(target=self.monitor_processes, daemon=True)
        monitor_thread.start()
        
        # 6. Afișează informații finale
        self.display_startup_info()
        
        return True
    
    def display_startup_info(self):
        """Afișează informațiile de pornire"""
        logging.info("🎉 SISTEM AI INTEGRAT PORNIT CU SUCCES!")
        logging.info("=" * 60)
        logging.info("🧠 Model LFM2-1.2B: ACTIV")
        logging.info("🎯 Antrenament Continuu: PORNIT")
        logging.info("🎓 Învățare 24/7: ACTIVĂ")
        logging.info("🤖 Procesare Cognitivă: ACTIVĂ")
        logging.info("✨ Motor Conștiință: ACTIV")
        logging.info("=" * 60)
        logging.info("📱 INTERFEȚE DISPONIBILE:")
        
        if "terminal" in self.processes:
            logging.info("🖥️  Terminal Interface: ACTIVĂ")
        
        if "web_basic" in self.processes:
            logging.info(f"🌐 Web Interface: http://localhost:{self.ports['lfm2_web']}")
        
        if "dashboard" in self.processes:
            logging.info(f"🚀 Dashboard Integrat: http://localhost:{self.ports['integrated_dashboard']}")
        
        logging.info("=" * 60)
        logging.info("💡 Pentru oprire: Ctrl+C")
        logging.info("📊 Monitorizare automată: ACTIVĂ")
        logging.info("💾 Backup automat: ACTIV")
        logging.info("🔄 Repornire automată: ACTIVĂ")
        logging.info("=" * 60)
    
    def stop_all_systems(self):
        """Oprește toate sistemele"""
        logging.info("⏹️ OPRIRE SISTEM AI INTEGRAT...")
        
        self.is_running = False
        
        # Oprește toate procesele
        for name, process in self.processes.items():
            try:
                logging.info(f"⏹️ Oprire {name}...")
                process.terminate()
                
                # Așteaptă 5 secunde pentru oprire gracioasă
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    # Forțează oprirea
                    process.kill()
                    process.wait()
                
                logging.info(f"✅ {name} oprit")
                
            except Exception as e:
                logging.error(f"❌ Eroare oprire {name}: {e}")
        
        # Creează backup final
        self.create_backup()
        
        logging.info("✅ SISTEM AI INTEGRAT OPRIT CU SUCCES!")
    
    def signal_handler(self, signum, frame):
        """Handler pentru semnale de oprire"""
        logging.info(f"📡 Semnal primit: {signum}")
        self.stop_all_systems()
        sys.exit(0)

def main():
    """Funcția principală"""
    print("🚀 INTEGRATED AI SYSTEM LAUNCHER")
    print("=" * 50)
    print("Pregătire pentru pornirea sistemului AI complet...")
    print()
    
    # Creează launcher-ul
    launcher = IntegratedSystemLauncher()
    
    # Configurează handler-ele pentru semnale
    signal.signal(signal.SIGINT, launcher.signal_handler)
    signal.signal(signal.SIGTERM, launcher.signal_handler)
    
    try:
        # Pornește toate sistemele
        if launcher.start_all_systems():
            # Rulează până la oprire
            while launcher.is_running:
                time.sleep(1)
        else:
            logging.error("❌ Eroare pornire sistem!")
            return 1
            
    except KeyboardInterrupt:
        logging.info("⏹️ Oprire solicitată de utilizator...")
    except Exception as e:
        logging.error(f"❌ Eroare neașteptată: {e}")
    finally:
        launcher.stop_all_systems()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
