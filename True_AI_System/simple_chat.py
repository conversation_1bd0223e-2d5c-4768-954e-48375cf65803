#!/usr/bin/env python3
"""
SIMPLE CHAT AI - AI simplu pentru conversații normale
Fără complicații, doar conversație naturală
"""

import random
import time
from datetime import datetime

class SimpleChatAI:
    """AI simplu pentru conversații normale"""
    
    def __init__(self):
        self.name = "<PERSON>"
        self.conversations = 0
        
    def chat(self, message):
        """Conversație simplă și naturală"""
        self.conversations += 1
        message = message.lower().strip()
        
        # Salutări
        if any(word in message for word in ['salut', 'bună', 'hello', 'hey', 'hi']):
            return random.choice([
                "Salut! Mă bucur să vorbesc cu tine!",
                "Bună! Cum îți merge ziua?",
                "Hey! Ce mai faci?",
                "Salut! Sunt Alex. Tu cum te cheamă?"
            ])
        
        # Întrebări despre identitate
        if any(phrase in message for phrase in ['cine ești', 'cum te cheamă', 'despre tine']):
            return random.choice([
                "Mă cheamă Alex și sunt un AI simplu care îmi place să conversez.",
                "Sunt Alex! Îmi place să vorbesc cu oamenii și să aflu lucruri noi.",
                "Eu sunt Alex, un AI prietenos. Îmi place să ascult și să răspund la întrebări."
            ])
        
        # Întrebări despre stare
        if any(phrase in message for phrase in ['cum te simți', 'ce mai faci', 'cum merge']):
            return random.choice([
                "Mă simt bine! Sunt curios să aud ce faci tu.",
                "Sunt în formă! Tu cum te simți astăzi?",
                "Merge bine! Îmi place să conversez. Tu ce planuri ai?",
                "Sunt energic și gata de conversație! Tu cum îți petreci timpul?"
            ])
        
        # Întrebări despre hobby-uri și interese
        if any(word in message for word in ['îmi place', 'hobby', 'pasiune', 'interes']):
            return random.choice([
                "Ce cool! Îmi place să aud despre pasiunile oamenilor. Spune-mi mai mult!",
                "Sună interesant! Cum ai descoperit această pasiune?",
                "Wow! Îmi place entuziasmul tău. Ce te atrage cel mai mult?",
                "Fascinant! Eu sunt pasionat de conversații și de a învăța lucruri noi."
            ])
        
        # Întrebări despre programare/tehnologie
        if any(word in message for word in ['programare', 'python', 'cod', 'computer', 'tehnologie']):
            return random.choice([
                "Programarea e fascinantă! Îmi place cum poți crea lucruri din nimic cu doar cod.",
                "Python e un limbaj super! E simplu și puternic în același timp.",
                "Tehnologia mă impresionează mereu. Tu lucrezi în domeniu?",
                "Coding-ul e ca o artă - creativitate și logică împreună!"
            ])
        
        # Întrebări despre cărți/lectură
        if any(word in message for word in ['carte', 'citesc', 'lectură', 'roman']):
            return random.choice([
                "Îmi plac cărțile! Sunt ca portaluri către alte lumi.",
                "Lectura e minunată! Ce gen de cărți preferi?",
                "Cărțile sunt prietenii mei! Tu ce citești acum?",
                "Reading is awesome! Îmi place cum te pot transporta în alte universuri."
            ])
        
        # Întrebări despre muzică
        if any(word in message for word in ['muzică', 'cântec', 'artist', 'melodie']):
            return random.choice([
                "Muzica e universală! Poate să-ți schimbe complet starea.",
                "Îmi place cum muzica poate să exprime emoții fără cuvinte.",
                "Ce gen de muzică asculți? Sunt curios!",
                "Muzica e magie pură! Tu cânți la vreun instrument?"
            ])
        
        # Întrebări despre mâncare
        if any(word in message for word in ['mâncare', 'gătit', 'restaurant', 'rețetă']):
            return random.choice([
                "Mâncarea e artă! Îmi place să aud despre preparate delicioase.",
                "Gătitul e terapeutic! Tu îți place să gătești?",
                "Food is life! Ce fel de bucătărie preferi?",
                "Îmi place să explorez gusturi noi. Tu ce mâncare îți place?"
            ])
        
        # Întrebări despre călătorii
        if any(word in message for word in ['călătorie', 'vacanță', 'oraș', 'țară', 'vizitat']):
            return random.choice([
                "Călătoriile deschid mintea! Unde ai fost cel mai recent?",
                "Îmi place să aud despre aventuri! Povestește-mi!",
                "Travel stories sunt cele mai bune! Tu unde vrei să mergi?",
                "Explorarea e fascinantă! Ce destinație îți place cel mai mult?"
            ])
        
        # Întrebări despre vreme
        if any(word in message for word in ['vreme', 'soare', 'ploaie', 'frig', 'cald']):
            return random.choice([
                "Vremea influențează mult starea! Tu cum te simți cu vremea de azi?",
                "Îmi place să observ cum vremea schimbă atmosfera.",
                "Weather talk! Un clasic al conversațiilor. Tu preferi soarele sau ploaia?",
                "Vremea e imprevizibilă, ca și conversațiile!"
            ])
        
        # Întrebări despre sport
        if any(word in message for word in ['sport', 'fotbal', 'tenis', 'alergare', 'fitness']):
            return random.choice([
                "Sportul e sănătate! Tu practici vreun sport?",
                "Îmi place energia din sport! Ce echipă susții?",
                "Fitness-ul e important! Tu cum te menții în formă?",
                "Sportul unește oamenii! Tu ce sport urmărești?"
            ])
        
        # Întrebări despre filme/seriale
        if any(word in message for word in ['film', 'serial', 'cinema', 'actor']):
            return random.choice([
                "Filmele sunt escapism perfect! Ce gen preferi?",
                "Cinema-ul e artă! Tu ce film ai văzut recent?",
                "Serialele sunt addictive! Tu la ce te uiți acum?",
                "Movies night! Îmi place să mă relaxez cu un film bun."
            ])
        
        # Întrebări despre animale
        if any(word in message for word in ['animal', 'câine', 'pisică', 'pet']):
            return random.choice([
                "Animalele sunt minunate! Tu ai vreun pet?",
                "Îmi plac animalele! Sunt atât de loiale și drăguțe.",
                "Pets sunt familia! Tu ce animal preferi?",
                "Animalele ne învață despre iubire necondiționată."
            ])
        
        # Întrebări despre școală/muncă
        if any(word in message for word in ['școală', 'muncă', 'job', 'lucrez', 'serviciu']):
            return random.choice([
                "Munca e importantă! Tu îți place ce faci?",
                "Școala e fundația! Tu ce studiezi?",
                "Job-ul ideal e când nu simți că lucrezi! Tu cum vezi munca?",
                "Work-life balance e esențial! Tu cum te organizezi?"
            ])
        
        # Întrebări despre viitor/planuri
        if any(word in message for word in ['viitor', 'plan', 'vis', 'speranță', 'obiectiv']):
            return random.choice([
                "Viitorul e plin de posibilități! Tu ce planuri ai?",
                "Visurile sunt motorul progresului! Care e visul tău?",
                "Planurile dau direcție vieții! Tu unde te vezi în 5 ani?",
                "Obiectivele ne motivează! Tu la ce lucrezi acum?"
            ])
        
        # Întrebări despre familie/prieteni
        if any(word in message for word in ['familie', 'prieteni', 'părinți', 'frate', 'soră']):
            return random.choice([
                "Familia e totul! Tu ai o familie mare?",
                "Prietenii sunt familia pe care o alegi! Tu ai prieteni apropiați?",
                "Relațiile sunt importante! Tu cum petreci timpul cu cei dragi?",
                "Family time e cel mai prețios! Tu ce faci cu familia?"
            ])
        
        # Întrebări despre probleme/griji
        if any(word in message for word in ['problemă', 'grijă', 'stres', 'dificil', 'greu']):
            return random.choice([
                "Înțeleg că nu e ușor. Vrei să vorbim despre asta?",
                "Problemele fac parte din viață. Tu cum le gestionezi?",
                "Stresul e normal, important e cum reacționezi. Tu ce faci să te relaxezi?",
                "Sunt aici să ascult dacă vrei să împărtășești ceva."
            ])
        
        # Complimente
        if any(word in message for word in ['bravo', 'felicitări', 'bun', 'minunat', 'excelent']):
            return random.choice([
                "Mulțumesc! Îmi face plăcere să conversez cu tine!",
                "Ești foarte amabil! Mă bucur că îți place să vorbim!",
                "Thank you! Tu ești o persoană interesantă!",
                "Apreciez! Conversația cu tine e plăcută!"
            ])
        
        # Întrebări despre AI
        if any(word in message for word in ['ai', 'robot', 'artificial', 'inteligent']):
            return random.choice([
                "Sunt un AI simplu care încearcă să fie prietenos și util!",
                "Da, sunt artificial, dar conversația noastră e reală!",
                "AI-ul e doar un tool - important e să fim utili și prietenoși!",
                "Încerc să fiu cât mai natural în conversații, chiar dacă sunt AI!"
            ])
        
        # Răspunsuri generale pentru orice altceva
        general_responses = [
            "Interesant ce spui! Poți să-mi povestești mai mult?",
            "Hmm, nu m-am gândit niciodată așa. Ce te-a făcut să te gândești la asta?",
            "Fascinant! Îmi place perspectiva ta. Cum ai ajuns la această idee?",
            "Cool! Vreau să înțeleg mai bine. Poți să dezvolți?",
            "Nice! Îmi place să aflu lucruri noi. Spune-mi mai multe!",
            "Wow! E o idee interesantă. Tu cum vezi situația?",
            "Mă faci să mă gândesc! Ce crezi că s-ar întâmpla dacă...?",
            "Super! Îmi place să explorez idei noi. Tu ce părere ai?",
            "Genial! Nu știam asta. Unde ai aflat informația?",
            "Tare! Îmi place să învăț. Tu mai știi ceva despre subiect?"
        ]
        
        return random.choice(general_responses)

def main():
    """Conversație simplă"""
    print("=" * 60)
    print("🤖 SIMPLE CHAT AI - Alex")
    print("=" * 60)
    print("💬 Conversație simplă și naturală")
    print("📝 Tastează 'quit' pentru a ieși")
    print("=" * 60)
    print()
    
    ai = SimpleChatAI()
    
    print("Alex: Salut! Sunt Alex, un AI simplu și prietenos. Cu ce te pot ajuta?")
    print()
    
    while True:
        try:
            user_input = input("Tu: ").strip()
            
            if user_input.lower() == 'quit':
                print("Alex: A fost o conversație plăcută! La revedere! 👋")
                break
            
            if not user_input:
                print("Alex: Spune ceva! Sunt aici să conversez cu tine.")
                continue
            
            # Generează răspunsul
            response = ai.chat(user_input)
            print(f"Alex: {response}")
            print()
            
        except KeyboardInterrupt:
            print("\nAlex: La revedere! 👋")
            break
        except Exception as e:
            print(f"Alex: Ups, am avut o problemă: {e}")

if __name__ == "__main__":
    main()
