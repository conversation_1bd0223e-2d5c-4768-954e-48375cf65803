#!/usr/bin/env python3
"""
INTEGRATED WEB INTERFACE - Interfață web completă pentru sistemul AI integrat
Dashboard avansat cu monitorizare în timp real, control și analytics
"""

from flask import Flask, render_template, request, jsonify
from flask_socketio import Socket<PERSON>, emit
import threading
import time
import json
import os
from datetime import datetime
from integrated_ai_system import IntegratedAISystem

app = Flask(__name__)
app.config['SECRET_KEY'] = 'integrated_ai_system_secret_key'
socketio = SocketIO(app, cors_allowed_origins="*")

# Instanța globală a sistemului AI integrat
integrated_ai = None
status_broadcast_thread = None

def initialize_integrated_ai():
    """Inițializează sistemul AI integrat"""
    global integrated_ai
    if integrated_ai is None:
        print("🚀 Inițializare sistem AI integrat...")
        integrated_ai = IntegratedAISystem()
        
        if integrated_ai.initialize_system():
            if integrated_ai.start_system():
                print("✅ Sistem AI integrat pornit cu succes!")
            else:
                print("❌ Eroare pornire sistem AI integrat!")
        else:
            print("❌ Eroare inițializare sistem AI integrat!")
    
    return integrated_ai

def broadcast_system_status():
    """Transmite statusul sistemului în timp real"""
    while True:
        try:
            if integrated_ai and integrated_ai.is_running:
                status = integrated_ai.get_system_status()
                socketio.emit('system_status_update', status)
            time.sleep(5)  # Update la fiecare 5 secunde
        except Exception as e:
            print(f"Eroare broadcast status: {e}")
            time.sleep(10)

@app.route('/')
def dashboard():
    """Dashboard principal"""
    return render_template('integrated_dashboard.html')

@app.route('/api/chat', methods=['POST'])
def chat():
    """Endpoint pentru chat cu sistemul AI integrat"""
    try:
        data = request.get_json()
        message = data.get('message', '').strip()
        context = data.get('context', {})
        
        if not message:
            return jsonify({'error': 'Mesajul nu poate fi gol'}), 400
        
        # Inițializează sistemul dacă nu e inițializat
        ai = initialize_integrated_ai()
        
        if not ai or not ai.is_running:
            return jsonify({'error': 'Sistemul AI nu este disponibil'}), 503
        
        # Procesează cu sistemul AI integrat
        result = ai.process_conversation(message, context)
        
        # Transmite mesajul în timp real
        socketio.emit('new_message', {
            'user_message': message,
            'ai_response': result.get('response', 'Eroare procesare'),
            'timestamp': datetime.now().isoformat(),
            'processing_time': result.get('processing_time', 0),
            'components_used': result.get('components_used', []),
            'enhanced_context': result.get('enhanced_context', {})
        })
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/system/status')
def system_status():
    """Status complet al sistemului"""
    try:
        ai = initialize_integrated_ai()
        if ai:
            return jsonify(ai.get_system_status())
        else:
            return jsonify({'error': 'Sistem nu este inițializat'}), 503
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/system/command', methods=['POST'])
def system_command():
    """Execută comenzi de control sistem"""
    try:
        data = request.get_json()
        command = data.get('command')
        params = data.get('params', {})
        
        if not command:
            return jsonify({'error': 'Comandă lipsă'}), 400
        
        ai = initialize_integrated_ai()
        if not ai:
            return jsonify({'error': 'Sistem nu este inițializat'}), 503
        
        result = ai.execute_command(command, params)
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/training/status')
def training_status():
    """Status antrenament continuu"""
    try:
        ai = initialize_integrated_ai()
        if ai and ai.training_engine:
            status = ai.training_engine.get_training_status()
            return jsonify(status)
        else:
            return jsonify({'error': 'Motor antrenament nu este disponibil'}), 503
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/learning/status')
def learning_status():
    """Status învățare continuă"""
    try:
        ai = initialize_integrated_ai()
        if ai and ai.learning_system:
            status = ai.learning_system.get_learning_stats()
            return jsonify(status)
        else:
            return jsonify({'error': 'Sistem învățare nu este disponibil'}), 503
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/analytics/performance')
def performance_analytics():
    """Analytics de performanță"""
    try:
        ai = initialize_integrated_ai()
        if not ai:
            return jsonify({'error': 'Sistem nu este inițializat'}), 503
        
        # Colectează date de performanță
        performance_data = {
            'response_times': [],
            'accuracy_scores': [],
            'training_progress': [],
            'learning_metrics': [],
            'system_resources': {
                'memory_usage': ai.system_stats.get('memory_usage_mb', 0),
                'cpu_usage': ai.system_stats.get('cpu_usage_percent', 0),
                'uptime': ai.system_stats.get('uptime_seconds', 0)
            }
        }
        
        # Adaugă date istorice dacă există
        if os.path.exists('performance_history.json'):
            with open('performance_history.json', 'r', encoding='utf-8') as f:
                history = json.load(f)
                performance_data.update(history)
        
        return jsonify(performance_data)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/model/info')
def model_info():
    """Informații despre modelul LFM2-1.2B"""
    try:
        ai = initialize_integrated_ai()
        if not ai:
            return jsonify({'error': 'Sistem nu este inițializat'}), 503
        
        model_info = {
            'model_name': 'LiquidAI/LFM2-1.2B',
            'model_format': 'GGUF (Q4_K_M)',
            'model_type': 'Hybrid Liquid Model',
            'parameters': '1.17B',
            'quantization': 'Q4_K_M (4-bit)',
            'file_size': '731 MB',
            'context_length': '32,768 tokens',
            'architecture': 'Multiplicative gates + short convolutions',
            'inference_engine': 'llama.cpp',
            'is_loaded': ai.lfm2_system.model_manager.is_loaded if ai.lfm2_system else False,
            'model_file': 'LFM2-1.2B-Q4_K_M.gguf',
            'supported_languages': ['English', 'Romanian', 'Arabic', 'Chinese', 'French', 'German', 'Japanese', 'Korean', 'Spanish'],
            'features': [
                'Real-time inference',
                'Continuous learning',
                'Non-stop training',
                'Cognitive processing',
                'Consciousness simulation',
                'Advanced neural modules',
                'Memory efficient',
                'High quality responses',
                'Long context support'
            ],
            'capabilities': [
                'Natural conversation',
                'Problem solving',
                'Creative writing',
                'Mathematical reasoning',
                'Logical deduction',
                'Pattern recognition',
                'Emotional intelligence',
                'Knowledge synthesis'
            ]
        }
        
        return jsonify(model_info)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@socketio.on('connect')
def handle_connect():
    """Client conectat"""
    print(f"Client conectat: {request.sid}")
    
    # Trimite statusul inițial
    try:
        ai = initialize_integrated_ai()
        if ai:
            status = ai.get_system_status()
            emit('system_status_update', status)
    except Exception as e:
        print(f"Eroare trimitere status inițial: {e}")

@socketio.on('disconnect')
def handle_disconnect():
    """Client deconectat"""
    print(f"Client deconectat: {request.sid}")

@socketio.on('request_system_status')
def handle_status_request():
    """Cerere status sistem"""
    try:
        ai = initialize_integrated_ai()
        if ai:
            status = ai.get_system_status()
            emit('system_status_update', status)
    except Exception as e:
        emit('error', {'message': str(e)})

@socketio.on('execute_command')
def handle_command(data):
    """Execută comandă sistem"""
    try:
        command = data.get('command')
        params = data.get('params', {})
        
        ai = initialize_integrated_ai()
        if ai:
            result = ai.execute_command(command, params)
            emit('command_result', result)
        else:
            emit('error', {'message': 'Sistem nu este inițializat'})
    except Exception as e:
        emit('error', {'message': str(e)})

def start_background_services():
    """Pornește serviciile de background"""
    global status_broadcast_thread
    
    if status_broadcast_thread is None:
        status_broadcast_thread = threading.Thread(target=broadcast_system_status, daemon=True)
        status_broadcast_thread.start()

if __name__ == '__main__':
    print("🌐 INTEGRATED WEB INTERFACE pentru Sistemul AI Complet")
    print("=" * 70)
    print("🔗 Accesează: http://localhost:5010")
    print("🧠 Model real: LiquidAI/LFM2-1.2B (GGUF)")
    print("🎯 Antrenament continuu: ACTIV")
    print("🎓 Învățare continuă: ACTIVĂ")
    print("🤖 Procesare cognitivă: ACTIVĂ")
    print("✨ Motor conștiință: ACTIV")
    print("📊 Monitorizare în timp real: ACTIVĂ")
    print("🚀 Dashboard avansat cu control complet")
    print("=" * 70)
    print()
    
    # Inițializează sistemul AI integrat
    initialize_integrated_ai()
    
    # Pornește serviciile de background
    start_background_services()
    
    # Pornește serverul
    socketio.run(app, host='0.0.0.0', port=5010, debug=False)
