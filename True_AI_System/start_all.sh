#!/bin/bash

# START ALL SYSTEMS - Script rapid pentru pornirea sistemului AI complet
# <PERSON><PERSON><PERSON><PERSON> toate componentele cu o singură comandă

echo "🚀 INTEGRATED AI SYSTEM - STARTUP SCRIPT"
echo "========================================================"
echo "🧠 Model: LiquidAI/LFM2-1.2B (GGUF)"
echo "🎯 Antrenament: Continuu 24/7"
echo "🎓 Învățare: Adaptivă în timp real"
echo "🤖 Procesare: Cognitivă avansată"
echo "✨ Conștiință: Simulare activă"
echo "========================================================"
echo

# Verifică dacă suntem în directorul corect
if [ ! -f "LFM2-1.2B-Q4_K_M.gguf" ]; then
    echo "❌ EROARE: Modelul LFM2-1.2B nu a fost găsit!"
    echo "💡 Asigură-te că ești în directorul True_AI_System"
    echo "💡 și că modelul a fost descărcat complet."
    exit 1
fi

# Verifică mărimea modelului
MODEL_SIZE=$(stat -c%s "LFM2-1.2B-Q4_K_M.gguf" 2>/dev/null || echo "0")
EXPECTED_SIZE=730893248  # 731 MB

if [ "$MODEL_SIZE" -lt $((EXPECTED_SIZE * 95 / 100)) ]; then
    echo "⚠️  ATENȚIE: Modelul pare incomplet!"
    echo "📊 Mărime actuală: $((MODEL_SIZE / 1024 / 1024)) MB"
    echo "📊 Mărime așteptată: $((EXPECTED_SIZE / 1024 / 1024)) MB"
    echo "❓ Continui oricum? (y/N)"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        echo "⏹️  Oprire..."
        exit 1
    fi
fi

echo "✅ Model LFM2-1.2B verificat: $((MODEL_SIZE / 1024 / 1024)) MB"
echo

# Verifică Python și dependențele
echo "🔍 Verificare Python și dependențe..."

if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 nu este instalat!"
    exit 1
fi

# Verifică dacă există requirements
if [ -f "requirements_advanced.txt" ]; then
    echo "📦 Instalare/actualizare dependențe..."
    pip3 install -r requirements_advanced.txt --quiet
    if [ $? -ne 0 ]; then
        echo "⚠️  Eroare instalare dependențe, dar continuăm..."
    fi
fi

echo "✅ Dependențe verificate"
echo

# Creează directoare necesare
mkdir -p backups
mkdir -p logs
mkdir -p templates

# Setează permisiuni
chmod +x *.py 2>/dev/null

echo "🚀 PORNIRE SISTEM AI INTEGRAT..."
echo "========================================================"
echo "⏳ Se inițializează toate componentele..."
echo "🖥️  Terminal Interface"
echo "🌐 Web Interface (port 5005)"
echo "🚀 Dashboard Integrat (port 5010)"
echo "🧠 Model LFM2-1.2B cu antrenament continuu"
echo "========================================================"
echo

# Pornește sistemul principal
python3 start_integrated_system.py

# Verifică codul de ieșire
EXIT_CODE=$?

echo
echo "========================================================"
if [ $EXIT_CODE -eq 0 ]; then
    echo "✅ SISTEM AI INTEGRAT OPRIT CU SUCCES"
else
    echo "❌ SISTEM AI INTEGRAT OPRIT CU ERORI (cod: $EXIT_CODE)"
fi
echo "💾 Toate datele au fost salvate automat"
echo "📊 Logurile sunt disponibile în directorul logs/"
echo "🔄 Pentru repornire, rulează din nou: ./start_all.sh"
echo "========================================================"

exit $EXIT_CODE
