#!/usr/bin/env python3
"""
SIMPLE WEB - Interfață web simplificată pentru TRUE AI SYSTEM
Funcționează stabil fără thread-uri problematice
"""

from flask import Flask, render_template, request, jsonify
import json
import os
import time
from datetime import datetime
from conversational_ai import ConversationalAI

app = Flask(__name__)
ai = ConversationalAI()

@app.route('/')
def index():
    """Pagina principală"""
    return render_template('simple_index.html')

@app.route('/api/chat', methods=['POST'])
def chat():
    """Endpoint pentru chat"""
    try:
        data = request.get_json()
        message = data.get('message', '').strip()
        
        if not message:
            return jsonify({'error': 'Mesajul nu poate fi gol'}), 400
        
        # Procesează mesajul cu AI-ul conversațional
        start_time = time.time()
        response = ai.process_message(message)
        processing_time = time.time() - start_time

        return jsonify({
            'response': response,
            'processing_time': processing_time,
            'consciousness_level': ai.consciousness_level,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/status')
def status():
    """Endpoint pentru status"""
    try:
        status_data = ai.get_status()
        return jsonify(status_data)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/stats')
def stats():
    """Endpoint pentru statistici"""
    try:
        status = ai.get_status()
        stats = {
            'total_conversations': status['conversation_length'],
            'knowledge_patterns': len(status['active_topics']),
            'consciousness_level': status['consciousness_level'],
            'interactions_count': status['interactions_count'],
            'uptime': status['uptime']
        }
        return jsonify(stats)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    print("🌐 Pornire interfață web simplificată...")
    print("🔗 Accesează: http://localhost:5001")
    print("✅ Interfața web este activă!")
    
    app.run(host='0.0.0.0', port=5001, debug=False)
