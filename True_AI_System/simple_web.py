#!/usr/bin/env python3
"""
SIMPLE WEB - Interfață web simplificată pentru TRUE AI SYSTEM
Funcționează stabil fără thread-uri problematice
"""

from flask import Flask, render_template, request, jsonify
import json
import os
from datetime import datetime
from simple_ai import SimpleAI

app = Flask(__name__)
ai = SimpleAI()

@app.route('/')
def index():
    """Pagina principală"""
    return render_template('simple_index.html')

@app.route('/api/chat', methods=['POST'])
def chat():
    """Endpoint pentru chat"""
    try:
        data = request.get_json()
        message = data.get('message', '').strip()
        
        if not message:
            return jsonify({'error': 'Mesajul nu poate fi gol'}), 400
        
        # Procesează mesajul
        result = ai.process_request(message)
        
        return jsonify({
            'response': result['response'],
            'processing_time': result['processing_time'],
            'consciousness_level': result['consciousness_level'],
            'timestamp': result['timestamp']
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/status')
def status():
    """Endpoint pentru status"""
    try:
        status_data = ai.get_status()
        return jsonify(status_data)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/stats')
def stats():
    """Endpoint pentru statistici"""
    try:
        stats = {
            'total_conversations': len(ai.conversation_memory),
            'knowledge_patterns': len(ai.knowledge_patterns),
            'consciousness_level': ai.consciousness_level,
            'interactions_count': ai.interactions_count,
            'uptime': (datetime.now() - ai.start_time).total_seconds()
        }
        return jsonify(stats)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    print("🌐 Pornire interfață web simplificată...")
    print("🔗 Accesează: http://localhost:5001")
    print("✅ Interfața web este activă!")
    
    app.run(host='0.0.0.0', port=5001, debug=False)
