"""
DENSE STORAGE SYSTEM - Sistem de stocare densă cu compresie avansată
Implementează algoritmi de compresie inteligentă și stocare eficientă
"""

import os
import json
import pickle
import zlib
import lzma
import bz2
import hashlib
import struct
import threading
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, asdict
import time

@dataclass
class StorageBlock:
    """Bloc de stocare cu metadata"""
    id: str
    data: bytes
    compression_type: str
    original_size: int
    compressed_size: int
    access_count: int
    last_access: datetime
    importance: float
    tags: List[str]
    checksum: str

@dataclass
class StorageStats:
    """Statistici de stocare"""
    total_blocks: int
    total_size_mb: float
    compression_ratio: float
    access_frequency: Dict[str, int]
    storage_efficiency: float
    fragmentation_level: float

class IntelligentCompressor:
    """Compressor inteligent care alege algoritmul optim"""
    
    def __init__(self):
        self.compression_algorithms = {
            'zlib': {'func': zlib.compress, 'decomp': zlib.decompress, 'speed': 'fast', 'ratio': 'medium'},
            'lzma': {'func': lzma.compress, 'decomp': lzma.decompress, 'speed': 'slow', 'ratio': 'high'},
            'bz2': {'func': bz2.compress, 'decomp': bz2.decompress, 'speed': 'medium', 'ratio': 'high'},
        }
        self.performance_stats = {alg: {'total_time': 0.0, 'total_ratio': 0.0, 'count': 0} for alg in self.compression_algorithms}
    
    def compress_intelligently(self, data: bytes, importance: float = 0.5, size_priority: bool = True) -> Tuple[bytes, str]:
        """Compresie inteligentă bazată pe importanță și prioritate"""
        
        # Pentru date foarte importante, folosește cea mai bună compresie
        if importance > 0.8:
            algorithm = 'lzma'
        # Pentru date de importanță medie, balansează viteza și compresia
        elif importance > 0.4:
            algorithm = 'bz2' if size_priority else 'zlib'
        # Pentru date mai puțin importante, prioritizează viteza
        else:
            algorithm = 'zlib'
        
        # Testează compresia
        start_time = time.time()
        compressed_data = self.compression_algorithms[algorithm]['func'](data)
        compression_time = time.time() - start_time
        
        # Actualizează statisticile
        compression_ratio = len(data) / len(compressed_data)
        self._update_performance_stats(algorithm, compression_time, compression_ratio)
        
        return compressed_data, algorithm
    
    def decompress(self, data: bytes, algorithm: str) -> bytes:
        """Decompresie folosind algoritmul specificat"""
        if algorithm not in self.compression_algorithms:
            raise ValueError(f"Algoritm necunoscut: {algorithm}")
        
        return self.compression_algorithms[algorithm]['decomp'](data)
    
    def _update_performance_stats(self, algorithm: str, time_taken: float, ratio: float):
        """Actualizează statisticile de performanță"""
        stats = self.performance_stats[algorithm]
        stats['total_time'] += time_taken
        stats['total_ratio'] += ratio
        stats['count'] += 1
    
    def get_best_algorithm_for_data(self, data_sample: bytes) -> str:
        """Determină cel mai bun algoritm pentru un tip de date"""
        # Testează pe un eșantion mic
        sample = data_sample[:1024] if len(data_sample) > 1024 else data_sample
        
        best_algorithm = 'zlib'
        best_ratio = 0.0
        
        for alg_name, alg_info in self.compression_algorithms.items():
            try:
                compressed = alg_info['func'](sample)
                ratio = len(sample) / len(compressed)
                
                if ratio > best_ratio:
                    best_ratio = ratio
                    best_algorithm = alg_name
            except:
                continue
        
        return best_algorithm

class DenseStorageSystem:
    """Sistem de stocare densă cu compresie avansată"""
    
    def __init__(self, storage_path: str = "True_AI_System/dense_storage", max_size_mb: int = 200):
        self.storage_path = storage_path
        self.max_size_bytes = max_size_mb * 1024 * 1024
        self.compressor = IntelligentCompressor()
        self.storage_blocks = {}
        self.index = {}
        self.access_lock = threading.RLock()
        
        # Creează directorul de stocare
        os.makedirs(storage_path, exist_ok=True)
        
        # Încarcă indexul existent
        self._load_index()
        
        # Thread pentru optimizare în background
        self.optimization_thread = threading.Thread(target=self._background_optimization, daemon=True)
        self.optimization_active = True
        self.optimization_thread.start()
        
        print(f"💾 Dense Storage System inițializat: {max_size_mb}MB")
    
    def store(self, key: str, data: Any, importance: float = 0.5, tags: List[str] = None) -> bool:
        """Stochează date cu compresie inteligentă"""
        try:
            with self.access_lock:
                # Serializează datele
                if isinstance(data, (str, int, float, bool)):
                    serialized = json.dumps(data).encode('utf-8')
                else:
                    serialized = pickle.dumps(data)
                
                # Compresie inteligentă
                compressed_data, compression_type = self.compressor.compress_intelligently(
                    serialized, importance, size_priority=True
                )
                
                # Calculează checksum
                checksum = hashlib.sha256(serialized).hexdigest()
                
                # Creează blocul de stocare
                block_id = hashlib.md5(f"{key}_{time.time()}".encode()).hexdigest()
                
                storage_block = StorageBlock(
                    id=block_id,
                    data=compressed_data,
                    compression_type=compression_type,
                    original_size=len(serialized),
                    compressed_size=len(compressed_data),
                    access_count=0,
                    last_access=datetime.now(),
                    importance=importance,
                    tags=tags or [],
                    checksum=checksum
                )
                
                # Verifică spațiul disponibil
                if not self._has_space_for_block(storage_block):
                    self._make_space_for_block(storage_block)
                
                # Salvează blocul
                block_path = os.path.join(self.storage_path, f"{block_id}.block")
                with open(block_path, 'wb') as f:
                    f.write(compressed_data)
                
                # Actualizează indexul
                self.storage_blocks[key] = storage_block
                self.index[key] = {
                    'block_id': block_id,
                    'compression_type': compression_type,
                    'original_size': len(serialized),
                    'compressed_size': len(compressed_data),
                    'importance': importance,
                    'tags': tags or [],
                    'checksum': checksum,
                    'created_at': datetime.now().isoformat()
                }
                
                # Salvează indexul
                self._save_index()
                
                return True
                
        except Exception as e:
            print(f"Eroare la stocare: {e}")
            return False
    
    def retrieve(self, key: str) -> Optional[Any]:
        """Recuperează date cu decompresie"""
        try:
            with self.access_lock:
                if key not in self.storage_blocks:
                    return None
                
                block = self.storage_blocks[key]
                
                # Încarcă datele comprimate
                block_path = os.path.join(self.storage_path, f"{block.id}.block")
                if not os.path.exists(block_path):
                    return None
                
                with open(block_path, 'rb') as f:
                    compressed_data = f.read()
                
                # Decompresie
                decompressed_data = self.compressor.decompress(compressed_data, block.compression_type)
                
                # Verifică integritatea
                checksum = hashlib.sha256(decompressed_data).hexdigest()
                if checksum != block.checksum:
                    print(f"Avertisment: Checksum invalid pentru {key}")
                
                # Deserializează
                try:
                    data = json.loads(decompressed_data.decode('utf-8'))
                except:
                    data = pickle.loads(decompressed_data)
                
                # Actualizează statisticile de acces
                block.access_count += 1
                block.last_access = datetime.now()
                
                return data
                
        except Exception as e:
            print(f"Eroare la recuperare: {e}")
            return None
    
    def store_large_dataset(self, key: str, dataset: List[Any], chunk_size: int = 1000) -> bool:
        """Stochează dataset-uri mari în chunk-uri"""
        try:
            chunks = [dataset[i:i + chunk_size] for i in range(0, len(dataset), chunk_size)]
            
            for i, chunk in enumerate(chunks):
                chunk_key = f"{key}_chunk_{i}"
                importance = 0.7  # Dataset-urile mari sunt importante
                
                if not self.store(chunk_key, chunk, importance, tags=['dataset', 'chunk']):
                    return False
            
            # Stochează metadata despre dataset
            metadata = {
                'total_chunks': len(chunks),
                'chunk_size': chunk_size,
                'total_items': len(dataset),
                'chunk_keys': [f"{key}_chunk_{i}" for i in range(len(chunks))]
            }
            
            return self.store(f"{key}_metadata", metadata, 0.9, tags=['metadata', 'dataset'])
            
        except Exception as e:
            print(f"Eroare la stocarea dataset-ului: {e}")
            return False
    
    def retrieve_large_dataset(self, key: str) -> Optional[List[Any]]:
        """Recuperează dataset-uri mari din chunk-uri"""
        try:
            # Încarcă metadata
            metadata = self.retrieve(f"{key}_metadata")
            if not metadata:
                return None
            
            # Încarcă toate chunk-urile
            dataset = []
            for chunk_key in metadata['chunk_keys']:
                chunk = self.retrieve(chunk_key)
                if chunk is None:
                    print(f"Avertisment: Chunk lipsă {chunk_key}")
                    continue
                dataset.extend(chunk)
            
            return dataset
            
        except Exception as e:
            print(f"Eroare la recuperarea dataset-ului: {e}")
            return None
    
    def _has_space_for_block(self, block: StorageBlock) -> bool:
        """Verifică dacă există spațiu pentru un bloc"""
        current_size = sum(os.path.getsize(os.path.join(self.storage_path, f"{b.id}.block")) 
                          for b in self.storage_blocks.values() 
                          if os.path.exists(os.path.join(self.storage_path, f"{b.id}.block")))
        
        return current_size + block.compressed_size <= self.max_size_bytes
    
    def _make_space_for_block(self, new_block: StorageBlock):
        """Eliberează spațiu pentru un bloc nou"""
        # Sortează blocurile după importanță și frecvența de acces
        blocks_by_priority = sorted(
            self.storage_blocks.items(),
            key=lambda x: (x[1].importance * (x[1].access_count + 1), x[1].last_access),
            reverse=False  # Cele mai puțin importante primul
        )
        
        space_needed = new_block.compressed_size
        space_freed = 0
        
        for key, block in blocks_by_priority:
            if space_freed >= space_needed:
                break
            
            # Nu șterge blocuri foarte importante
            if block.importance > 0.8:
                continue
            
            # Șterge blocul
            block_path = os.path.join(self.storage_path, f"{block.id}.block")
            if os.path.exists(block_path):
                space_freed += os.path.getsize(block_path)
                os.remove(block_path)
            
            del self.storage_blocks[key]
            if key in self.index:
                del self.index[key]
    
    def _background_optimization(self):
        """Optimizare în background"""
        while self.optimization_active:
            try:
                time.sleep(300)  # Rulează la 5 minute
                
                with self.access_lock:
                    self._optimize_storage()
                    self._defragment_storage()
                    
            except Exception as e:
                print(f"Eroare în optimizarea background: {e}")
                time.sleep(60)
    
    def _optimize_storage(self):
        """Optimizează stocarea prin recompresie"""
        for key, block in list(self.storage_blocks.items()):
            # Recompresie pentru blocuri accesate frecvent
            if block.access_count > 10 and block.importance < 0.9:
                try:
                    # Încarcă și recompresie cu algoritm mai bun
                    data = self.retrieve(key)
                    if data is not None:
                        # Șterge blocul vechi
                        old_path = os.path.join(self.storage_path, f"{block.id}.block")
                        if os.path.exists(old_path):
                            os.remove(old_path)
                        
                        # Stochează din nou cu importanță crescută
                        self.store(key, data, min(1.0, block.importance + 0.1), block.tags)
                        
                except Exception as e:
                    print(f"Eroare la recompresie pentru {key}: {e}")
    
    def _defragment_storage(self):
        """Defragmentează stocarea"""
        # Reorganizează fișierele pentru acces mai rapid
        temp_blocks = {}
        
        for key, block in self.storage_blocks.items():
            old_path = os.path.join(self.storage_path, f"{block.id}.block")
            if os.path.exists(old_path):
                # Creează un nou ID pentru defragmentare
                new_id = hashlib.md5(f"defrag_{key}_{time.time()}".encode()).hexdigest()
                new_path = os.path.join(self.storage_path, f"{new_id}.block")
                
                # Mută fișierul
                os.rename(old_path, new_path)
                
                # Actualizează blocul
                block.id = new_id
                temp_blocks[key] = block
        
        self.storage_blocks = temp_blocks
        self._save_index()
    
    def _load_index(self):
        """Încarcă indexul din fișier"""
        index_path = os.path.join(self.storage_path, "storage_index.json")
        
        try:
            if os.path.exists(index_path):
                with open(index_path, 'r', encoding='utf-8') as f:
                    self.index = json.load(f)
                
                # Recreează blocurile din index
                for key, index_data in self.index.items():
                    block = StorageBlock(
                        id=index_data['block_id'],
                        data=b'',  # Datele se încarcă la cerere
                        compression_type=index_data['compression_type'],
                        original_size=index_data['original_size'],
                        compressed_size=index_data['compressed_size'],
                        access_count=0,
                        last_access=datetime.fromisoformat(index_data['created_at']),
                        importance=index_data['importance'],
                        tags=index_data['tags'],
                        checksum=index_data['checksum']
                    )
                    self.storage_blocks[key] = block
                
                print(f"💾 Încărcat index cu {len(self.index)} intrări")
                
        except Exception as e:
            print(f"Eroare la încărcarea indexului: {e}")
            self.index = {}
            self.storage_blocks = {}
    
    def _save_index(self):
        """Salvează indexul în fișier"""
        index_path = os.path.join(self.storage_path, "storage_index.json")
        
        try:
            with open(index_path, 'w', encoding='utf-8') as f:
                json.dump(self.index, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"Eroare la salvarea indexului: {e}")
    
    def get_storage_stats(self) -> StorageStats:
        """Returnează statistici despre stocare"""
        total_size = 0
        total_original_size = 0
        access_freq = {}
        
        for key, block in self.storage_blocks.items():
            block_path = os.path.join(self.storage_path, f"{block.id}.block")
            if os.path.exists(block_path):
                total_size += os.path.getsize(block_path)
                total_original_size += block.original_size
                access_freq[key] = block.access_count
        
        compression_ratio = total_original_size / total_size if total_size > 0 else 1.0
        storage_efficiency = (self.max_size_bytes - total_size) / self.max_size_bytes
        
        return StorageStats(
            total_blocks=len(self.storage_blocks),
            total_size_mb=total_size / (1024 * 1024),
            compression_ratio=compression_ratio,
            access_frequency=access_freq,
            storage_efficiency=storage_efficiency,
            fragmentation_level=0.1  # Placeholder
        )
    
    def cleanup_old_data(self, days_old: int = 30):
        """Curăță datele vechi"""
        cutoff_date = datetime.now() - timedelta(days=days_old)
        
        keys_to_remove = []
        for key, block in self.storage_blocks.items():
            if block.last_access < cutoff_date and block.importance < 0.3:
                keys_to_remove.append(key)
        
        for key in keys_to_remove:
            block = self.storage_blocks[key]
            block_path = os.path.join(self.storage_path, f"{block.id}.block")
            if os.path.exists(block_path):
                os.remove(block_path)
            
            del self.storage_blocks[key]
            if key in self.index:
                del self.index[key]
        
        self._save_index()
        print(f"🗑️ Curățate {len(keys_to_remove)} blocuri vechi")
    
    def export_important_data(self, min_importance: float = 0.8) -> Dict[str, Any]:
        """Exportă datele importante"""
        important_data = {}
        
        for key, block in self.storage_blocks.items():
            if block.importance >= min_importance:
                data = self.retrieve(key)
                if data is not None:
                    important_data[key] = data
        
        return important_data
    
    def get_compression_report(self) -> Dict[str, Any]:
        """Generează raport despre compresie"""
        compression_stats = {}
        
        for key, block in self.storage_blocks.items():
            comp_type = block.compression_type
            if comp_type not in compression_stats:
                compression_stats[comp_type] = {
                    'count': 0,
                    'total_original': 0,
                    'total_compressed': 0,
                    'avg_ratio': 0.0
                }
            
            stats = compression_stats[comp_type]
            stats['count'] += 1
            stats['total_original'] += block.original_size
            stats['total_compressed'] += block.compressed_size
        
        # Calculează ratele medii
        for comp_type, stats in compression_stats.items():
            if stats['total_compressed'] > 0:
                stats['avg_ratio'] = stats['total_original'] / stats['total_compressed']
        
        return compression_stats
