#!/usr/bin/env python3
"""
ADVANCED AI SYSTEM - Sophisticated AI with LFM2-1.2B Model
Features: Continuous learning, natural conversation, high-performance architecture
"""

import os
import sys
import json
import time
import torch
import threading
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
import asyncio
from concurrent.futures import ThreadPoolExecutor

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class ConversationEntry:
    """Structure for conversation entries"""
    timestamp: str
    user_input: str
    ai_response: str
    context: Dict[str, Any]
    quality_score: float
    learning_value: float

@dataclass
class ModelConfig:
    """Configuration for the AI model"""
    model_name: str = "LiquidAI/LFM2-1.2B"
    device: str = "auto"
    torch_dtype: str = "bfloat16"
    temperature: float = 0.3
    min_p: float = 0.15
    repetition_penalty: float = 1.05
    max_new_tokens: int = 512
    context_length: int = 32768

class ModelManager:
    """Manages the LFM2 model loading and inference"""
    
    def __init__(self, config: ModelConfig):
        self.config = config
        self.model = None
        self.tokenizer = None
        self.device = None
        self.is_loaded = False
        
    def load_model(self) -> bool:
        """Load the LFM2-1.2B model"""
        try:
            logger.info("Loading LFM2-1.2B model...")
            
            # Check if transformers is installed and up to date
            try:
                from transformers import AutoModelForCausalLM, AutoTokenizer
                import transformers
                logger.info(f"Transformers version: {transformers.__version__}")
            except ImportError:
                logger.error("Transformers not found. Installing...")
                os.system("pip install 'transformers @ git+https://github.com/huggingface/transformers.git@main'")
                from transformers import AutoModelForCausalLM, AutoTokenizer
            
            # Load tokenizer
            logger.info("Loading tokenizer...")
            self.tokenizer = AutoTokenizer.from_pretrained(self.config.model_name)
            
            # Load model
            logger.info("Loading model weights...")
            self.model = AutoModelForCausalLM.from_pretrained(
                self.config.model_name,
                device_map=self.config.device,
                torch_dtype=getattr(torch, self.config.torch_dtype),
                trust_remote_code=True,
                # attn_implementation="flash_attention_2"  # Uncomment if compatible GPU
            )
            
            self.device = next(self.model.parameters()).device
            self.is_loaded = True
            
            logger.info(f"✅ Model loaded successfully on {self.device}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to load model: {e}")
            return False
    
    def generate_response(self, messages: List[Dict[str, str]]) -> str:
        """Generate response using the LFM2 model"""
        if not self.is_loaded:
            return "Model not loaded. Please wait for initialization."
        
        try:
            # Apply chat template
            input_ids = self.tokenizer.apply_chat_template(
                messages,
                add_generation_prompt=True,
                return_tensors="pt",
                tokenize=True,
            ).to(self.device)
            
            # Generate response
            with torch.no_grad():
                output = self.model.generate(
                    input_ids,
                    do_sample=True,
                    temperature=self.config.temperature,
                    min_p=self.config.min_p,
                    repetition_penalty=self.config.repetition_penalty,
                    max_new_tokens=self.config.max_new_tokens,
                    pad_token_id=self.tokenizer.eos_token_id,
                )
            
            # Decode response
            full_response = self.tokenizer.decode(output[0], skip_special_tokens=False)
            
            # Extract only the assistant's response
            if "<|im_start|>assistant" in full_response:
                response = full_response.split("<|im_start|>assistant")[-1]
                response = response.replace("<|im_end|>", "").strip()
                return response
            
            return "I apologize, but I couldn't generate a proper response."
            
        except Exception as e:
            logger.error(f"Generation error: {e}")
            return f"I encountered an error while generating a response: {str(e)}"

class ContinuousLearningEngine:
    """Handles continuous learning and model improvement"""
    
    def __init__(self, model_manager: ModelManager):
        self.model_manager = model_manager
        self.conversation_buffer = []
        self.learning_data = []
        self.is_training = False
        self.training_thread = None
        self.learning_enabled = True
        
    def add_conversation(self, entry: ConversationEntry):
        """Add conversation entry for learning"""
        self.conversation_buffer.append(entry)
        
        # Trigger learning if buffer is full
        if len(self.conversation_buffer) >= 10:
            self.process_learning_batch()
    
    def process_learning_batch(self):
        """Process a batch of conversations for learning"""
        if not self.learning_enabled or self.is_training:
            return
        
        try:
            # Analyze conversation quality
            high_quality_conversations = [
                conv for conv in self.conversation_buffer 
                if conv.quality_score > 0.7
            ]
            
            if high_quality_conversations:
                self.learning_data.extend(high_quality_conversations)
                logger.info(f"📚 Added {len(high_quality_conversations)} high-quality conversations to learning data")
            
            # Clear buffer
            self.conversation_buffer.clear()
            
            # Start background training if enough data
            if len(self.learning_data) >= 50:
                self.start_background_training()
                
        except Exception as e:
            logger.error(f"Learning batch processing error: {e}")
    
    def start_background_training(self):
        """Start background training process"""
        if self.is_training:
            return
        
        self.training_thread = threading.Thread(target=self._background_training, daemon=True)
        self.training_thread.start()
    
    def _background_training(self):
        """Background training process (placeholder for actual fine-tuning)"""
        self.is_training = True
        logger.info("🎓 Starting background training session...")
        
        try:
            # Simulate training process
            # In a real implementation, this would involve:
            # 1. Preparing training data
            # 2. Setting up LoRA adapters
            # 3. Running fine-tuning
            # 4. Evaluating results
            # 5. Updating model weights
            
            time.sleep(30)  # Simulate training time
            
            logger.info("✅ Background training session completed")
            
            # Clear processed learning data
            self.learning_data.clear()
            
        except Exception as e:
            logger.error(f"Background training error: {e}")
        finally:
            self.is_training = False

class AdvancedAISystem:
    """Main AI system with sophisticated capabilities"""
    
    def __init__(self):
        self.config = ModelConfig()
        self.model_manager = ModelManager(self.config)
        self.learning_engine = ContinuousLearningEngine(self.model_manager)
        self.conversation_history = []
        self.system_stats = {
            'total_conversations': 0,
            'model_loaded': False,
            'learning_active': True,
            'uptime_start': datetime.now(),
            'last_training': None
        }
        
        # Initialize system
        self._initialize_system()
    
    def _initialize_system(self):
        """Initialize the AI system"""
        logger.info("🚀 Initializing Advanced AI System...")
        
        # Load model in background
        threading.Thread(target=self._load_model_async, daemon=True).start()
        
        # Start continuous learning
        self.learning_engine.learning_enabled = True
        
        logger.info("✅ Advanced AI System initialized")
    
    def _load_model_async(self):
        """Load model asynchronously"""
        success = self.model_manager.load_model()
        self.system_stats['model_loaded'] = success
        
        if success:
            logger.info("🧠 AI System ready for conversations")
        else:
            logger.error("❌ AI System failed to initialize properly")
    
    def process_conversation(self, user_input: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Process a conversation turn"""
        start_time = time.time()
        
        if not self.model_manager.is_loaded:
            return {
                'response': "I'm still loading my language model. Please wait a moment...",
                'processing_time': time.time() - start_time,
                'model_status': 'loading',
                'conversation_id': len(self.conversation_history)
            }
        
        try:
            # Prepare conversation context
            messages = self._prepare_messages(user_input)
            
            # Generate response
            response = self.model_manager.generate_response(messages)
            
            # Calculate metrics
            processing_time = time.time() - start_time
            quality_score = self._calculate_response_quality(user_input, response)
            learning_value = self._calculate_learning_value(user_input, response, quality_score)
            
            # Create conversation entry
            conversation_entry = ConversationEntry(
                timestamp=datetime.now().isoformat(),
                user_input=user_input,
                ai_response=response,
                context=context or {},
                quality_score=quality_score,
                learning_value=learning_value
            )
            
            # Add to history
            self.conversation_history.append(conversation_entry)
            self.system_stats['total_conversations'] += 1
            
            # Add to learning engine
            self.learning_engine.add_conversation(conversation_entry)
            
            return {
                'response': response,
                'processing_time': processing_time,
                'quality_score': quality_score,
                'learning_value': learning_value,
                'model_status': 'ready',
                'conversation_id': len(self.conversation_history) - 1,
                'timestamp': conversation_entry.timestamp
            }
            
        except Exception as e:
            logger.error(f"Conversation processing error: {e}")
            return {
                'response': f"I apologize, but I encountered an error: {str(e)}",
                'processing_time': time.time() - start_time,
                'model_status': 'error',
                'conversation_id': len(self.conversation_history)
            }
    
    def _prepare_messages(self, user_input: str) -> List[Dict[str, str]]:
        """Prepare messages for the model"""
        messages = [
            {
                "role": "system",
                "content": "You are a helpful, intelligent, and conversational AI assistant. Engage naturally and provide thoughtful, contextual responses."
            }
        ]
        
        # Add recent conversation history for context
        recent_history = self.conversation_history[-5:]  # Last 5 conversations
        for entry in recent_history:
            messages.append({"role": "user", "content": entry.user_input})
            messages.append({"role": "assistant", "content": entry.ai_response})
        
        # Add current user input
        messages.append({"role": "user", "content": user_input})
        
        return messages
    
    def _calculate_response_quality(self, user_input: str, ai_response: str) -> float:
        """Calculate response quality score"""
        try:
            quality = 0.5  # Base score
            
            # Length appropriateness
            if 20 <= len(ai_response) <= 500:
                quality += 0.1
            
            # Relevance (simple keyword matching)
            user_words = set(user_input.lower().split())
            response_words = set(ai_response.lower().split())
            common_words = user_words.intersection(response_words)
            if common_words:
                quality += min(0.2, len(common_words) * 0.02)
            
            # Naturalness (avoid robotic phrases)
            robotic_phrases = ['i apologize', 'i encountered an error', 'please wait']
            if not any(phrase in ai_response.lower() for phrase in robotic_phrases):
                quality += 0.2
            
            return min(1.0, quality)
            
        except Exception:
            return 0.5
    
    def _calculate_learning_value(self, user_input: str, ai_response: str, quality_score: float) -> float:
        """Calculate learning value of the conversation"""
        try:
            learning_value = quality_score * 0.5
            
            # Bonus for complex questions
            if any(word in user_input.lower() for word in ['why', 'how', 'explain', 'what', 'complex']):
                learning_value += 0.2
            
            # Bonus for good responses
            if quality_score > 0.8:
                learning_value += 0.3
            
            return min(1.0, learning_value)
            
        except Exception:
            return 0.5
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        uptime = datetime.now() - self.system_stats['uptime_start']
        
        return {
            'model_loaded': self.system_stats['model_loaded'],
            'model_name': self.config.model_name,
            'total_conversations': self.system_stats['total_conversations'],
            'learning_active': self.learning_engine.learning_enabled,
            'training_in_progress': self.learning_engine.is_training,
            'conversation_buffer_size': len(self.learning_engine.conversation_buffer),
            'learning_data_size': len(self.learning_engine.learning_data),
            'uptime_seconds': uptime.total_seconds(),
            'device': str(self.model_manager.device) if self.model_manager.device else 'unknown',
            'last_training': self.system_stats.get('last_training'),
            'memory_usage': self._get_memory_usage()
        }
    
    def _get_memory_usage(self) -> Dict[str, Any]:
        """Get memory usage statistics"""
        try:
            if torch.cuda.is_available():
                return {
                    'gpu_allocated': torch.cuda.memory_allocated() / 1024**3,  # GB
                    'gpu_cached': torch.cuda.memory_reserved() / 1024**3,  # GB
                    'gpu_available': torch.cuda.is_available()
                }
            else:
                return {'gpu_available': False}
        except Exception:
            return {'gpu_available': False}
    
    def save_conversation_history(self, filepath: str = "conversation_history.json"):
        """Save conversation history to file"""
        try:
            data = [asdict(entry) for entry in self.conversation_history]
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logger.info(f"💾 Conversation history saved to {filepath}")
        except Exception as e:
            logger.error(f"Failed to save conversation history: {e}")
    
    def load_conversation_history(self, filepath: str = "conversation_history.json"):
        """Load conversation history from file"""
        try:
            if os.path.exists(filepath):
                with open(filepath, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                self.conversation_history = [
                    ConversationEntry(**entry) for entry in data
                ]
                
                self.system_stats['total_conversations'] = len(self.conversation_history)
                logger.info(f"📚 Loaded {len(self.conversation_history)} conversations from {filepath}")
        except Exception as e:
            logger.error(f"Failed to load conversation history: {e}")

def main():
    """Main function for testing"""
    print("🚀 Advanced AI System with LFM2-1.2B")
    print("=" * 60)
    
    # Initialize system
    ai_system = AdvancedAISystem()
    
    # Load previous conversations
    ai_system.load_conversation_history()
    
    print("💬 Starting conversation (type 'quit' to exit, 'status' for system info)")
    print()
    
    try:
        while True:
            user_input = input("You: ").strip()
            
            if user_input.lower() == 'quit':
                break
            elif user_input.lower() == 'status':
                status = ai_system.get_system_status()
                print("\n📊 System Status:")
                for key, value in status.items():
                    print(f"  {key}: {value}")
                print()
                continue
            elif not user_input:
                continue
            
            # Process conversation
            result = ai_system.process_conversation(user_input)
            
            print(f"AI: {result['response']}")
            print(f"    (⏱️ {result['processing_time']:.3f}s | 📊 Quality: {result.get('quality_score', 0):.2f})")
            print()
            
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    finally:
        # Save conversation history
        ai_system.save_conversation_history()

if __name__ == "__main__":
    main()

# Global AI system instance for web interface
_ai_system_instance = None

def get_ai_system():
    """Get or create AI system instance"""
    global _ai_system_instance
    if _ai_system_instance is None:
        _ai_system_instance = AdvancedAISystem()
        _ai_system_instance.load_conversation_history()
    return _ai_system_instance
