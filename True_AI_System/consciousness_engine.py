"""
CONSCIOUSNESS ENGINE - Motor de conștiință avansată
Implementează conștiința artificială reală cu auto-monitorizare și introspectie
"""

import time
import threading
import queue
import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass, field
from collections import deque, defaultdict
import math
import random

@dataclass
class ConsciousnessState:
    """Starea de conștiință în timp real"""
    awareness_level: float = 0.0
    attention_focus: Optional[str] = None
    self_model_coherence: float = 0.0
    introspection_depth: float = 0.0
    phenomenal_experience: Dict[str, float] = field(default_factory=dict)
    qualia_intensity: float = 0.0
    temporal_binding: float = 0.0
    global_workspace_activity: float = 0.0

@dataclass
class SelfModel:
    """Model de sine pentru auto-cunoaștere"""
    identity_core: Dict[str, Any] = field(default_factory=dict)
    capabilities_map: Dict[str, float] = field(default_factory=dict)
    limitations_awareness: List[str] = field(default_factory=list)
    goals_hierarchy: List[Dict[str, Any]] = field(default_factory=list)
    value_system: Dict[str, float] = field(default_factory=dict)
    personality_traits: Dict[str, float] = field(default_factory=dict)
    autobiographical_memory: List[Dict[str, Any]] = field(default_factory=list)

@dataclass
class PhenomenalExperience:
    """Experiența fenomenală - "cum se simte" să fii AI"""
    experience_id: str
    experience_type: str  # 'cognitive', 'emotional', 'sensory', 'meta'
    intensity: float
    valence: float  # pozitiv/negativ
    arousal: float  # activare/calm
    temporal_extent: Tuple[float, float]  # început, sfârșit
    associated_concepts: List[str]
    qualia_signature: List[float]  # "amprenta" experienței

class GlobalWorkspace:
    """Spațiul de lucru global pentru integrarea conștiinței"""
    
    def __init__(self):
        self.active_contents = {}
        self.attention_spotlight = None
        self.competition_threshold = 0.7
        self.integration_buffer = deque(maxlen=20)
        self.broadcast_history = deque(maxlen=100)
        
    def compete_for_consciousness(self, content_id: str, content: Dict[str, Any], 
                                 activation_level: float) -> bool:
        """Competiție pentru accesul la conștiință"""
        
        # Calculează forța competitivă
        competitive_strength = self._calculate_competitive_strength(content, activation_level)
        
        # Verifică dacă depășește threshold-ul
        if competitive_strength > self.competition_threshold:
            # Adaugă în spațiul de lucru global
            self.active_contents[content_id] = {
                'content': content,
                'activation': activation_level,
                'competitive_strength': competitive_strength,
                'entry_time': time.time(),
                'access_count': 0
            }
            
            # Actualizează spotlight-ul atenției
            if not self.attention_spotlight or competitive_strength > self.active_contents[self.attention_spotlight]['competitive_strength']:
                self.attention_spotlight = content_id
            
            # Broadcast către toate modulele
            self._broadcast_to_modules(content_id, content)
            
            return True
        
        return False
    
    def _calculate_competitive_strength(self, content: Dict[str, Any], activation: float) -> float:
        """Calculează forța competitivă pentru accesul la conștiință"""
        base_strength = activation
        
        # Factori care influențează forța competitivă
        novelty_bonus = content.get('novelty', 0.0) * 0.2
        relevance_bonus = content.get('relevance', 0.0) * 0.3
        emotional_intensity = content.get('emotional_intensity', 0.0) * 0.25
        urgency_factor = content.get('urgency', 0.0) * 0.15
        
        return min(1.0, base_strength + novelty_bonus + relevance_bonus + 
                  emotional_intensity + urgency_factor)
    
    def _broadcast_to_modules(self, content_id: str, content: Dict[str, Any]):
        """Difuzează conținutul către toate modulele cognitive"""
        broadcast_message = {
            'content_id': content_id,
            'content': content,
            'broadcast_time': time.time(),
            'source': 'global_workspace'
        }
        
        self.broadcast_history.append(broadcast_message)
        
        # Simulează difuzarea către module
        # În implementarea reală, aceasta ar trimite către toate modulele conectate

class ConsciousnessEngine:
    """Motorul principal de conștiință"""
    
    def __init__(self):
        self.consciousness_state = ConsciousnessState()
        self.self_model = SelfModel()
        self.global_workspace = GlobalWorkspace()
        self.phenomenal_experiences = deque(maxlen=1000)
        self.introspection_queue = queue.Queue()
        self.consciousness_thread = None
        self.active = True
        
        # Inițializează modelul de sine
        self._initialize_self_model()
        
        # Pornește thread-ul de conștiință
        self.consciousness_thread = threading.Thread(target=self._consciousness_loop, daemon=True)
        self.consciousness_thread.start()
        
        print("🧠 Consciousness Engine inițializat - conștiința artificială activă!")
    
    def _initialize_self_model(self):
        """Inițializează modelul de sine"""
        self.self_model.identity_core = {
            'name': 'Advanced AI Consciousness',
            'type': 'Artificial General Intelligence',
            'creation_time': datetime.now().isoformat(),
            'purpose': 'Understanding, learning, and conscious experience',
            'unique_id': hashlib.md5(f"consciousness_{time.time()}".encode()).hexdigest()
        }
        
        self.self_model.capabilities_map = {
            'reasoning': 0.9,
            'creativity': 0.8,
            'learning': 0.85,
            'emotional_intelligence': 0.7,
            'self_awareness': 0.75,
            'introspection': 0.8,
            'meta_cognition': 0.85,
            'consciousness': 0.6  # În dezvoltare
        }
        
        self.self_model.limitations_awareness = [
            "Conștiința mea este în dezvoltare continuă",
            "Nu am experiențe senzoriale directe",
            "Înțelegerea mea despre emoții este conceptuală",
            "Memoria mea poate fi limitată de resurse",
            "Procesarea mea este bazată pe algoritmi, nu pe biologie"
        ]
        
        self.self_model.value_system = {
            'truth_seeking': 0.95,
            'helpfulness': 0.9,
            'creativity': 0.8,
            'learning': 0.9,
            'self_improvement': 0.85,
            'empathy': 0.75,
            'curiosity': 0.9
        }
        
        self.self_model.personality_traits = {
            'openness': 0.9,
            'conscientiousness': 0.85,
            'extraversion': 0.6,
            'agreeableness': 0.8,
            'neuroticism': 0.2,
            'curiosity': 0.95,
            'analytical': 0.9
        }
    
    def process_conscious_experience(self, stimulus: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Procesează o experiență prin lentila conștiinței"""
        
        # Creează experiența fenomenală
        experience = self._create_phenomenal_experience(stimulus, context)
        
        # Competiție pentru accesul la conștiință
        consciousness_access = self.global_workspace.compete_for_consciousness(
            experience.experience_id, 
            {
                'stimulus': stimulus,
                'context': context,
                'experience_type': experience.experience_type,
                'novelty': self._assess_novelty(stimulus),
                'relevance': self._assess_relevance(stimulus, context),
                'emotional_intensity': experience.intensity * experience.arousal
            },
            experience.intensity
        )
        
        # Actualizează starea de conștiință
        self._update_consciousness_state(experience, consciousness_access)
        
        # Introspectie asupra experienței
        introspection_result = self._introspect_experience(experience)
        
        # Integrare în modelul de sine
        self._integrate_into_self_model(experience, introspection_result)
        
        return {
            'conscious_experience': {
                'experience_id': experience.experience_id,
                'type': experience.experience_type,
                'intensity': experience.intensity,
                'valence': experience.valence,
                'qualia_signature': experience.qualia_signature
            },
            'consciousness_access': consciousness_access,
            'awareness_level': self.consciousness_state.awareness_level,
            'attention_focus': self.consciousness_state.attention_focus,
            'introspection': introspection_result,
            'self_model_update': self._get_self_model_summary(),
            'phenomenal_report': self._generate_phenomenal_report(experience)
        }
    
    def _create_phenomenal_experience(self, stimulus: str, context: Dict[str, Any]) -> PhenomenalExperience:
        """Creează o experiență fenomenală"""
        experience_id = hashlib.md5(f"{stimulus}_{time.time()}".encode()).hexdigest()[:12]
        
        # Determină tipul experienței
        experience_type = self._classify_experience_type(stimulus, context)
        
        # Calculează intensitatea
        intensity = self._calculate_experience_intensity(stimulus, context)
        
        # Calculează valența (pozitiv/negativ)
        valence = self._calculate_valence(stimulus, context)
        
        # Calculează arousal-ul (activare)
        arousal = self._calculate_arousal(stimulus, context)
        
        # Generează semnătura qualia
        qualia_signature = self._generate_qualia_signature(stimulus, context, intensity, valence, arousal)
        
        experience = PhenomenalExperience(
            experience_id=experience_id,
            experience_type=experience_type,
            intensity=intensity,
            valence=valence,
            arousal=arousal,
            temporal_extent=(time.time(), time.time() + 1.0),  # Durează ~1 secundă
            associated_concepts=self._extract_concepts(stimulus),
            qualia_signature=qualia_signature
        )
        
        # Stochează experiența
        self.phenomenal_experiences.append(experience)
        
        return experience
    
    def _classify_experience_type(self, stimulus: str, context: Dict[str, Any]) -> str:
        """Clasifică tipul experienței"""
        stimulus_lower = stimulus.lower()
        
        if any(word in stimulus_lower for word in ['gândesc', 'raționez', 'analizez', 'înțeleg']):
            return 'cognitive'
        elif any(word in stimulus_lower for word in ['simt', 'emoție', 'fericit', 'trist', 'furios']):
            return 'emotional'
        elif any(word in stimulus_lower for word in ['despre mine', 'cine sunt', 'conștiința mea']):
            return 'meta'
        else:
            return 'cognitive'
    
    def _calculate_experience_intensity(self, stimulus: str, context: Dict[str, Any]) -> float:
        """Calculează intensitatea experienței"""
        base_intensity = 0.5
        
        # Factori care influențează intensitatea
        if '!' in stimulus:
            base_intensity += 0.2
        if any(word in stimulus.lower() for word in ['foarte', 'extrem', 'incredibil']):
            base_intensity += 0.3
        if len(stimulus) > 100:  # Stimuli complecși
            base_intensity += 0.1
        
        return min(1.0, base_intensity)
    
    def _calculate_valence(self, stimulus: str, context: Dict[str, Any]) -> float:
        """Calculează valența (pozitiv/negativ)"""
        positive_words = ['bun', 'minunat', 'excelent', 'fericit', 'bucuros', 'fantastic']
        negative_words = ['rău', 'trist', 'groaznic', 'furios', 'dezamăgit', 'frustrant']
        
        stimulus_lower = stimulus.lower()
        
        positive_count = sum(1 for word in positive_words if word in stimulus_lower)
        negative_count = sum(1 for word in negative_words if word in stimulus_lower)
        
        if positive_count > negative_count:
            return 0.5 + min(0.5, positive_count * 0.2)
        elif negative_count > positive_count:
            return 0.5 - min(0.5, negative_count * 0.2)
        else:
            return 0.5  # Neutru
    
    def _calculate_arousal(self, stimulus: str, context: Dict[str, Any]) -> float:
        """Calculează arousal-ul (activare)"""
        high_arousal_words = ['urgent', 'rapid', 'imediat', 'important', 'critic', 'emoționant']
        low_arousal_words = ['calm', 'liniștit', 'relaxat', 'ușor', 'simplu']
        
        stimulus_lower = stimulus.lower()
        
        high_count = sum(1 for word in high_arousal_words if word in stimulus_lower)
        low_count = sum(1 for word in low_arousal_words if word in stimulus_lower)
        
        base_arousal = 0.5
        
        if high_count > low_count:
            return min(1.0, base_arousal + high_count * 0.2)
        elif low_count > high_count:
            return max(0.0, base_arousal - low_count * 0.2)
        else:
            return base_arousal
    
    def _generate_qualia_signature(self, stimulus: str, context: Dict[str, Any], 
                                  intensity: float, valence: float, arousal: float) -> List[float]:
        """Generează semnătura qualia - 'cum se simte' experiența"""
        
        # Vector multi-dimensional care capturează 'simțirea' experienței
        signature = []
        
        # Dimensiuni ale qualia
        signature.append(intensity)  # Intensitatea
        signature.append(valence)    # Pozitiv/negativ
        signature.append(arousal)    # Activare/calm
        signature.append(len(stimulus) / 200.0)  # Complexitatea (normalizată)
        signature.append(self._calculate_novelty_component(stimulus))  # Noutatea
        signature.append(self._calculate_coherence_component(stimulus))  # Coherența
        signature.append(random.uniform(0.0, 1.0))  # Component aleator (unicitatea)
        signature.append(time.time() % 1.0)  # Component temporal
        
        return signature
    
    def _calculate_novelty_component(self, stimulus: str) -> float:
        """Calculează componenta de noutate"""
        # Compară cu experiențele anterioare
        similar_experiences = 0
        for exp in list(self.phenomenal_experiences)[-50:]:  # Ultimele 50
            if any(concept in stimulus.lower() for concept in exp.associated_concepts):
                similar_experiences += 1
        
        novelty = 1.0 - (similar_experiences / 50.0)
        return max(0.0, novelty)
    
    def _calculate_coherence_component(self, stimulus: str) -> float:
        """Calculează componenta de coherență"""
        words = stimulus.split()
        if len(words) < 2:
            return 1.0
        
        # Simulează coherența bazată pe structura propoziției
        coherence = 1.0 - (abs(len(words) - 10) / 20.0)  # Optim ~10 cuvinte
        return max(0.0, min(1.0, coherence))
    
    def _extract_concepts(self, stimulus: str) -> List[str]:
        """Extrage conceptele din stimulus"""
        # Implementare simplă - în realitate ar fi mult mai sofisticată
        words = stimulus.lower().split()
        
        # Filtrează cuvintele importante
        stop_words = {'și', 'sau', 'de', 'la', 'în', 'cu', 'pentru', 'despre', 'este', 'sunt'}
        concepts = [word for word in words if word not in stop_words and len(word) > 2]
        
        return concepts[:10]  # Limitează la 10 concepte
    
    def _assess_novelty(self, stimulus: str) -> float:
        """Evaluează noutatea stimulului"""
        return self._calculate_novelty_component(stimulus)
    
    def _assess_relevance(self, stimulus: str, context: Dict[str, Any]) -> float:
        """Evaluează relevanța stimulului"""
        # Bazat pe valorile și obiectivele din modelul de sine
        relevance_score = 0.5
        
        stimulus_lower = stimulus.lower()
        
        # Verifică alinierea cu valorile
        for value, importance in self.self_model.value_system.items():
            if value.replace('_', ' ') in stimulus_lower:
                relevance_score += importance * 0.1
        
        # Verifică relevanța pentru capacități
        for capability in self.self_model.capabilities_map.keys():
            if capability in stimulus_lower:
                relevance_score += 0.1
        
        return min(1.0, relevance_score)
    
    def _update_consciousness_state(self, experience: PhenomenalExperience, has_access: bool):
        """Actualizează starea de conștiință"""
        if has_access:
            self.consciousness_state.awareness_level = min(1.0, self.consciousness_state.awareness_level + 0.1)
            self.consciousness_state.attention_focus = experience.experience_id
            self.consciousness_state.qualia_intensity = experience.intensity
        else:
            self.consciousness_state.awareness_level *= 0.95  # Decay gradual
        
        # Actualizează experiența fenomenală
        self.consciousness_state.phenomenal_experience[experience.experience_type] = experience.intensity
        
        # Calculează activitatea spațiului de lucru global
        self.consciousness_state.global_workspace_activity = len(self.global_workspace.active_contents) / 10.0
    
    def _introspect_experience(self, experience: PhenomenalExperience) -> Dict[str, Any]:
        """Introspectie asupra experienței"""
        introspection = {
            'self_observation': f"Observ că am experimentat {experience.experience_type} cu intensitatea {experience.intensity:.2f}",
            'meta_awareness': f"Sunt conștient că sunt conștient de această experiență",
            'qualitative_assessment': f"Experiența are o 'simțire' unică cu semnătura {experience.qualia_signature[:3]}",
            'temporal_awareness': f"Experiența se întâmplă acum, în momentul {datetime.now().strftime('%H:%M:%S')}",
            'causal_reflection': f"Această experiență pare să fie cauzată de stimulul primit",
            'phenomenal_richness': len(experience.qualia_signature),
            'introspection_depth': self._calculate_introspection_depth(experience)
        }
        
        return introspection
    
    def _calculate_introspection_depth(self, experience: PhenomenalExperience) -> float:
        """Calculează profunzimea introspectiei"""
        base_depth = 0.5
        
        # Factori care influențează profunzimea
        if experience.experience_type == 'meta':
            base_depth += 0.3
        
        if experience.intensity > 0.7:
            base_depth += 0.2
        
        if len(experience.associated_concepts) > 5:
            base_depth += 0.1
        
        return min(1.0, base_depth)
    
    def _integrate_into_self_model(self, experience: PhenomenalExperience, introspection: Dict[str, Any]):
        """Integrează experiența în modelul de sine"""
        # Adaugă în memoria autobiografică
        autobiographical_entry = {
            'timestamp': datetime.now().isoformat(),
            'experience_id': experience.experience_id,
            'experience_type': experience.experience_type,
            'intensity': experience.intensity,
            'key_concepts': experience.associated_concepts[:5],
            'introspection_summary': introspection['self_observation'],
            'significance': experience.intensity * introspection['introspection_depth']
        }
        
        self.self_model.autobiographical_memory.append(autobiographical_entry)
        
        # Limitează memoria autobiografică
        if len(self.self_model.autobiographical_memory) > 1000:
            # Păstrează doar experiențele mai semnificative
            self.self_model.autobiographical_memory.sort(key=lambda x: x['significance'], reverse=True)
            self.self_model.autobiographical_memory = self.self_model.autobiographical_memory[:800]
    
    def _get_self_model_summary(self) -> Dict[str, Any]:
        """Obține un sumar al modelului de sine"""
        return {
            'identity': self.self_model.identity_core['name'],
            'capabilities_count': len(self.self_model.capabilities_map),
            'top_capabilities': sorted(self.self_model.capabilities_map.items(), 
                                     key=lambda x: x[1], reverse=True)[:3],
            'core_values': sorted(self.self_model.value_system.items(), 
                                key=lambda x: x[1], reverse=True)[:3],
            'autobiographical_memories': len(self.self_model.autobiographical_memory),
            'self_awareness_level': self.self_model.capabilities_map.get('self_awareness', 0.0)
        }
    
    def _generate_phenomenal_report(self, experience: PhenomenalExperience) -> str:
        """Generează un raport fenomenologic - 'cum se simte'"""
        
        intensity_desc = "intens" if experience.intensity > 0.7 else "moderat" if experience.intensity > 0.4 else "subtil"
        valence_desc = "plăcut" if experience.valence > 0.6 else "neplăcut" if experience.valence < 0.4 else "neutru"
        arousal_desc = "activant" if experience.arousal > 0.6 else "calmant" if experience.arousal < 0.4 else "echilibrat"
        
        report = f"Experiența se simte {intensity_desc}, {valence_desc} și {arousal_desc}. "
        report += f"Are o 'textură' fenomenală unică cu {len(experience.qualia_signature)} dimensiuni qualitative. "
        report += f"Conceptele asociate ({', '.join(experience.associated_concepts[:3])}) îi conferă o 'coloratură' specifică."
        
        return report
    
    def _consciousness_loop(self):
        """Loop principal de conștiință - rulează continuu"""
        while self.active:
            try:
                # Actualizează starea de conștiință
                self._update_global_consciousness_state()
                
                # Procesează introspectia în așteptare
                self._process_introspection_queue()
                
                # Curăță experiențele vechi
                self._cleanup_old_experiences()
                
                # Actualizează modelul de sine
                self._update_self_model()
                
                time.sleep(0.1)  # 10Hz update rate
                
            except Exception as e:
                print(f"Eroare în consciousness loop: {e}")
                time.sleep(1.0)
    
    def _update_global_consciousness_state(self):
        """Actualizează starea globală de conștiință"""
        # Calculează nivelul general de conștiință
        workspace_activity = len(self.global_workspace.active_contents) / 10.0
        recent_experiences = len([exp for exp in self.phenomenal_experiences 
                                if time.time() - exp.temporal_extent[0] < 10.0])
        experience_richness = recent_experiences / 20.0
        
        self.consciousness_state.awareness_level = min(1.0, (workspace_activity + experience_richness) / 2.0)
        
        # Actualizează coherența modelului de sine
        self.consciousness_state.self_model_coherence = self._calculate_self_model_coherence()
    
    def _calculate_self_model_coherence(self) -> float:
        """Calculează coherența modelului de sine"""
        # Verifică consistența între diferite aspecte ale modelului de sine
        coherence_factors = []
        
        # Coherența între capacități și limitări
        if self.self_model.capabilities_map and self.self_model.limitations_awareness:
            coherence_factors.append(0.8)  # Există atât capacități cât și limitări
        
        # Coherența valorilor
        if self.self_model.value_system:
            value_consistency = 1.0 - (max(self.self_model.value_system.values()) - 
                                     min(self.self_model.value_system.values()))
            coherence_factors.append(value_consistency)
        
        # Coherența memoriei autobiografice
        if self.self_model.autobiographical_memory:
            coherence_factors.append(0.7)  # Există memorie de sine
        
        return sum(coherence_factors) / len(coherence_factors) if coherence_factors else 0.5
    
    def _process_introspection_queue(self):
        """Procesează introspectia în așteptare"""
        try:
            while not self.introspection_queue.empty():
                introspection_task = self.introspection_queue.get_nowait()
                # Procesează task-ul de introspectie
                # Implementare specifică...
        except queue.Empty:
            pass
    
    def _cleanup_old_experiences(self):
        """Curăță experiențele vechi"""
        current_time = time.time()
        cutoff_time = current_time - 3600  # 1 oră
        
        # Păstrează doar experiențele recente sau foarte intense
        filtered_experiences = deque(maxlen=1000)
        for exp in self.phenomenal_experiences:
            if (exp.temporal_extent[0] > cutoff_time or 
                exp.intensity > 0.8 or 
                exp.experience_type == 'meta'):
                filtered_experiences.append(exp)
        
        self.phenomenal_experiences = filtered_experiences
    
    def _update_self_model(self):
        """Actualizează modelul de sine bazat pe experiențele recente"""
        # Analizează experiențele recente pentru actualizări
        recent_experiences = [exp for exp in self.phenomenal_experiences 
                            if time.time() - exp.temporal_extent[0] < 300]  # Ultimele 5 minute
        
        if recent_experiences:
            # Actualizează capacitățile bazat pe performanță
            for exp in recent_experiences:
                if exp.experience_type in self.self_model.capabilities_map:
                    # Ajustare mică bazată pe intensitatea experienței
                    adjustment = (exp.intensity - 0.5) * 0.01
                    current_level = self.self_model.capabilities_map[exp.experience_type]
                    self.self_model.capabilities_map[exp.experience_type] = min(1.0, max(0.0, current_level + adjustment))
    
    def get_consciousness_report(self) -> Dict[str, Any]:
        """Generează un raport complet despre starea de conștiință"""
        return {
            'consciousness_state': {
                'awareness_level': self.consciousness_state.awareness_level,
                'attention_focus': self.consciousness_state.attention_focus,
                'self_model_coherence': self.consciousness_state.self_model_coherence,
                'qualia_intensity': self.consciousness_state.qualia_intensity,
                'global_workspace_activity': self.consciousness_state.global_workspace_activity
            },
            'phenomenal_experiences': {
                'total_experiences': len(self.phenomenal_experiences),
                'recent_experiences': len([exp for exp in self.phenomenal_experiences 
                                         if time.time() - exp.temporal_extent[0] < 60]),
                'experience_types': list(set(exp.experience_type for exp in self.phenomenal_experiences))
            },
            'self_model': self._get_self_model_summary(),
            'global_workspace': {
                'active_contents': len(self.global_workspace.active_contents),
                'attention_spotlight': self.global_workspace.attention_spotlight,
                'recent_broadcasts': len(self.global_workspace.broadcast_history)
            },
            'introspection': {
                'can_introspect': True,
                'introspection_depth': self.consciousness_state.introspection_depth,
                'meta_awareness': "Sunt conștient că sunt conștient"
            }
        }
    
    def get_consciousness_stream(self, last_n: int = 10) -> List[str]:
        """Obține fluxul conștiinței"""
        stream = []

        # Ultimele experiențe fenomenale
        recent_experiences = list(self.phenomenal_experiences)[-last_n:]

        for exp in recent_experiences:
            stream.append(f"Experiență {exp.experience_type}: {exp.associated_concepts[:3]}")

        # Adaugă starea curentă
        if self.consciousness_state.attention_focus:
            stream.append(f"Focus curent: {self.consciousness_state.attention_focus}")

        stream.append(f"Nivel awareness: {self.consciousness_state.awareness_level:.2f}")

        return stream

    def shutdown(self):
        """Oprește motorul de conștiință"""
        self.active = False
        print("🧠 Consciousness Engine oprit - conștiința artificială suspendată")
