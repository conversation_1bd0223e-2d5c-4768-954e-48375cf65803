#!/usr/bin/env python3
"""
DEMO SYSTEM - Demonstrație sistem AI cu LFM2-1.2B real
Sistem funcțional cu model real și antrenament continuu
"""

import os
import sys
import time
import threading
import logging
from datetime import datetime
from lfm2_gguf_system import LFM2GGUFSystem
from continuous_training_engine import ContinuousTrainingEngine

# Configurare logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class DemoAISystem:
    """Sistem AI demo cu LFM2-1.2B real și antrenament continuu"""
    
    def __init__(self):
        self.lfm2_system = None
        self.training_engine = None
        self.is_running = False
        self.conversation_count = 0
        
        print("🚀 DEMO SISTEM AI CU LFM2-1.2B REAL")
        print("=" * 50)
        print("🧠 Model: LiquidAI/LFM2-1.2B (GGUF)")
        print("🎯 Antrenament: Continuu în background")
        print("💬 Conversații: Interactive în terminal")
        print("=" * 50)
        print()
    
    def initialize(self):
        """Inițializează sistemul"""
        try:
            print("🔧 Inițializare sistem AI...")
            
            # Inițializează sistemul LFM2
            print("📥 Încărcare model LFM2-1.2B...")
            self.lfm2_system = LFM2GGUFSystem()
            
            # Inițializează antrenamentul continuu
            print("🎯 Inițializare antrenament continuu...")
            self.training_engine = ContinuousTrainingEngine(self)
            
            self.is_running = True
            print("✅ Sistem inițializat cu succes!")
            return True
            
        except Exception as e:
            print(f"❌ Eroare inițializare: {e}")
            return False
    
    def start_training(self):
        """Pornește antrenamentul continuu în background"""
        if self.training_engine:
            print("🎯 Pornire antrenament continuu în background...")
            self.training_engine.start_continuous_training()
            print("✅ Antrenament continuu pornit!")
    
    def process_conversation(self, message):
        """Procesează o conversație"""
        if not self.lfm2_system:
            return {"error": "Sistem nu este inițializat"}
        
        try:
            result = self.lfm2_system.process_conversation(message)
            self.conversation_count += 1
            return result
        except Exception as e:
            return {"error": str(e)}
    
    def get_status(self):
        """Returnează statusul sistemului"""
        training_status = "Inactiv"
        if self.training_engine and self.training_engine.is_training:
            training_status = "Activ"
        
        return {
            "model_loaded": self.lfm2_system.model_manager.is_loaded if self.lfm2_system else False,
            "training_active": training_status,
            "conversations": self.conversation_count,
            "uptime": time.time() - self.start_time if hasattr(self, 'start_time') else 0
        }
    
    def interactive_chat(self):
        """Mod chat interactiv"""
        print("\n💬 MOD CHAT INTERACTIV")
        print("=" * 30)
        print("Comenzi speciale:")
        print("- 'quit' sau 'exit' - Ieșire")
        print("- 'status' - Afișează status sistem")
        print("- 'training start' - Pornește antrenament")
        print("- 'training stop' - Oprește antrenament")
        print("=" * 30)
        print()
        
        while self.is_running:
            try:
                user_input = input("Tu: ").strip()
                
                if user_input.lower() in ['quit', 'exit']:
                    break
                
                elif user_input.lower() == 'status':
                    status = self.get_status()
                    print(f"\n📊 STATUS SISTEM:")
                    print(f"🧠 Model încărcat: {'✅' if status['model_loaded'] else '❌'}")
                    print(f"🎯 Antrenament: {status['training_active']}")
                    print(f"💬 Conversații: {status['conversations']}")
                    print(f"⏱️ Uptime: {status['uptime']:.1f}s")
                    print()
                    continue
                
                elif user_input.lower() == 'training start':
                    self.start_training()
                    continue
                
                elif user_input.lower() == 'training stop':
                    if self.training_engine:
                        self.training_engine.stop_continuous_training()
                        print("⏹️ Antrenament oprit!")
                    continue
                
                elif not user_input:
                    continue
                
                # Procesează conversația
                print("🤔 LFM2 gândește...")
                start_time = time.time()
                
                result = self.process_conversation(user_input)
                
                processing_time = time.time() - start_time
                
                if "error" in result:
                    print(f"❌ Eroare: {result['error']}")
                else:
                    response = result.get("response", "Nu am putut genera un răspuns.")
                    quality = result.get("quality_score", 0)
                    learning = result.get("learning_value", 0)
                    
                    print(f"\n🧠 LFM2: {response}")
                    print(f"    (⏱️ {processing_time:.3f}s | 📊 Calitate: {quality:.2f} | 🎓 Învățare: {learning:.2f})")
                    print()
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"❌ Eroare: {e}")
        
        print("\n👋 La revedere!")
    
    def run_demo(self):
        """Rulează demo-ul complet"""
        self.start_time = time.time()
        
        # Inițializează sistemul
        if not self.initialize():
            return
        
        # Pornește antrenamentul în background
        self.start_training()
        
        # Afișează informații finale
        print("\n🎉 SISTEM AI DEMO PORNIT CU SUCCES!")
        print("=" * 40)
        print("🧠 Model LFM2-1.2B: ÎNCĂRCAT")
        print("🎯 Antrenament continuu: ACTIV")
        print("💬 Chat interactiv: DISPONIBIL")
        print("=" * 40)
        
        # Pornește chat-ul interactiv
        try:
            self.interactive_chat()
        finally:
            self.cleanup()
    
    def cleanup(self):
        """Curăță resursele"""
        print("\n🧹 Curățare resurse...")
        
        if self.training_engine:
            self.training_engine.stop_continuous_training()
        
        self.is_running = False
        print("✅ Resurse curățate!")

def main():
    """Funcția principală"""
    # Verifică dacă modelul există
    model_path = "LFM2-1.2B-Q4_K_M.gguf"
    if not os.path.exists(model_path):
        print("❌ EROARE: Modelul LFM2-1.2B nu a fost găsit!")
        print(f"💡 Asigură-te că fișierul {model_path} există în directorul curent.")
        return 1
    
    # Verifică mărimea modelului
    file_size = os.path.getsize(model_path)
    expected_size = 730893248  # 731 MB
    
    if file_size < expected_size * 0.95:
        print(f"⚠️ ATENȚIE: Modelul pare incomplet!")
        print(f"📊 Mărime actuală: {file_size / 1024 / 1024:.1f} MB")
        print(f"📊 Mărime așteptată: {expected_size / 1024 / 1024:.1f} MB")
        
        response = input("❓ Continui oricum? (y/N): ")
        if not response.lower().startswith('y'):
            return 1
    
    # Creează și rulează demo-ul
    demo = DemoAISystem()
    demo.run_demo()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
