"""
Neural Processing Engine - Sistem neural simplu pentru AI Assistant
<PERSON><PERSON><PERSON><PERSON><PERSON> procesarea neurală pentru înțelegerea și generarea de răspunsuri
"""

# import numpy as np  # Înlocuit cu implementare simplă
import json
import pickle
import hashlib
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import math
import random

@dataclass
class NeuralPattern:
    """Pattern neural pentru recunoaștere"""
    id: str
    pattern_type: str
    input_features: List[float]
    output_response: str
    confidence: float
    usage_count: int
    last_used: datetime

class SimpleNeuralNetwork:
    """Rețea neurală simplă pentru procesare (fără numpy)"""

    def __init__(self, input_size: int, hidden_size: int, output_size: int):
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.output_size = output_size

        # Inițializare ponderi aleatoare (implementare simplă)
        self.weights_input_hidden = [[random.uniform(-0.1, 0.1) for _ in range(hidden_size)] for _ in range(input_size)]
        self.weights_hidden_output = [[random.uniform(-0.1, 0.1) for _ in range(output_size)] for _ in range(hidden_size)]
        self.bias_hidden = [0.0] * hidden_size
        self.bias_output = [0.0] * output_size

        # Rata de învățare
        self.learning_rate = 0.01
    
    def sigmoid(self, x):
        """Funcția de activare sigmoid (implementare simplă)"""
        if isinstance(x, list):
            return [1 / (1 + math.exp(-max(-500, min(500, val)))) for val in x]
        else:
            return 1 / (1 + math.exp(-max(-500, min(500, x))))

    def sigmoid_derivative(self, x):
        """Derivata funcției sigmoid"""
        if isinstance(x, list):
            return [val * (1 - val) for val in x]
        else:
            return x * (1 - x)

    def matrix_multiply(self, a, b):
        """Înmulțire matrici simplă"""
        if isinstance(a[0], list):  # a este matrice
            result = []
            for i in range(len(a)):
                row = []
                for j in range(len(b[0]) if isinstance(b[0], list) else 1):
                    if isinstance(b[0], list):
                        val = sum(a[i][k] * b[k][j] for k in range(len(b)))
                    else:
                        val = sum(a[i][k] * b[k] for k in range(len(b)))
                    row.append(val)
                result.append(row)
            return result
        else:  # a este vector
            if isinstance(b[0], list):
                return [sum(a[k] * b[k][j] for k in range(len(a))) for j in range(len(b[0]))]
            else:
                return sum(a[k] * b[k] for k in range(len(a)))

    def forward(self, inputs):
        """Propagarea înainte (implementare simplă)"""
        # Input la hidden layer
        hidden_input = []
        for j in range(self.hidden_size):
            val = sum(inputs[i] * self.weights_input_hidden[i][j] for i in range(len(inputs))) + self.bias_hidden[j]
            hidden_input.append(val)

        self.hidden_output = self.sigmoid(hidden_input)

        # Hidden la output layer
        output_input = []
        for j in range(self.output_size):
            val = sum(self.hidden_output[i] * self.weights_hidden_output[i][j] for i in range(len(self.hidden_output))) + self.bias_output[j]
            output_input.append(val)

        self.output = self.sigmoid(output_input)
        return self.output
    
    def backward(self, inputs, targets, outputs):
        """Propagarea înapoi pentru învățare (implementare simplificată)"""
        # Calculează eroarea (implementare simplă)
        output_error = [targets[i] - outputs[i] for i in range(len(targets))]
        output_delta = [output_error[i] * self.sigmoid_derivative([outputs[i]])[0] for i in range(len(output_error))]

        # Actualizează ponderile (implementare simplificată)
        for i in range(len(self.weights_hidden_output)):
            for j in range(len(self.weights_hidden_output[i])):
                self.weights_hidden_output[i][j] += self.hidden_output[i] * output_delta[j] * self.learning_rate

        for j in range(len(self.bias_output)):
            self.bias_output[j] += output_delta[j] * self.learning_rate

    def train(self, training_data: List[Tuple[List[float], List[float]]], epochs: int = 100):
        """Antrenează rețeaua neurală (versiune simplificată)"""
        for epoch in range(epochs):
            total_error = 0
            for inputs, targets in training_data:
                outputs = self.forward(inputs)
                self.backward(inputs, targets, outputs)

                # Calculează eroarea
                error = sum((targets[i] - outputs[i]) ** 2 for i in range(len(targets)))
                total_error += error

            if epoch % 20 == 0:
                print(f"Epoch {epoch}, Error: {total_error/len(training_data):.6f}")

class NeuralProcessingEngine:
    """Engine principal pentru procesarea neurală"""
    
    def __init__(self, workspace_path: str = "."):
        self.workspace_path = workspace_path
        self.patterns = {}
        self.neural_networks = {}
        self.feature_extractors = {}
        
        # Inițializează rețelele neurale pentru diferite task-uri
        self.init_neural_networks()
        
        # Încarcă pattern-urile existente
        self.load_patterns()
        
        print("🧠 Neural Processing Engine inițializat!")
    
    def init_neural_networks(self):
        """Inițializează rețelele neurale pentru diferite capacități"""
        # Rețea pentru analiza sentimentelor
        self.neural_networks['sentiment'] = SimpleNeuralNetwork(100, 50, 3)  # pozitiv, neutru, negativ
        
        # Rețea pentru clasificarea intent-urilor
        self.neural_networks['intent'] = SimpleNeuralNetwork(100, 80, 10)  # 10 tipuri de intent
        
        # Rețea pentru generarea de răspunsuri
        self.neural_networks['response'] = SimpleNeuralNetwork(150, 100, 50)
        
        # Rețea pentru evaluarea complexității
        self.neural_networks['complexity'] = SimpleNeuralNetwork(50, 30, 5)  # 5 nivele de complexitate
    
    def extract_text_features(self, text: str) -> List[float]:
        """Extrage caracteristici numerice din text"""
        features = []
        
        # Caracteristici de bază
        features.append(len(text))  # Lungimea textului
        features.append(len(text.split()))  # Numărul de cuvinte
        features.append(len([c for c in text if c.isupper()]))  # Litere mari
        features.append(len([c for c in text if c.islower()]))  # Litere mici
        features.append(len([c for c in text if c.isdigit()]))  # Cifre
        features.append(text.count('?'))  # Întrebări
        features.append(text.count('!'))  # Exclamații
        features.append(text.count('.'))  # Puncte
        
        # Caracteristici lingvistice
        words = text.lower().split()
        
        # Cuvinte cheie pentru diferite domenii
        programming_keywords = ['cod', 'funcție', 'clasă', 'variabilă', 'algoritm', 'debug', 'test']
        web_keywords = ['site', 'pagină', 'html', 'css', 'javascript', 'server', 'client']
        ai_keywords = ['inteligență', 'artificială', 'neural', 'învățare', 'algoritm', 'model']
        
        features.append(sum(1 for word in words if word in programming_keywords))
        features.append(sum(1 for word in words if word in web_keywords))
        features.append(sum(1 for word in words if word in ai_keywords))
        
        # Caracteristici de sentiment
        positive_words = ['bun', 'excelent', 'minunat', 'perfect', 'grozav', 'fantastic']
        negative_words = ['rău', 'prost', 'groaznic', 'oribil', 'dezastru', 'problemă']
        
        features.append(sum(1 for word in words if word in positive_words))
        features.append(sum(1 for word in words if word in negative_words))
        
        # Completează cu zerouri până la 100 de caracteristici
        while len(features) < 100:
            features.append(0.0)
        
        # Normalizează caracteristicile
        max_val = max(features) if max(features) > 0 else 1
        features = [f / max_val for f in features]
        
        return features[:100]  # Limitează la 100
    
    def analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """Analizează sentimentul textului"""
        features = self.extract_text_features(text)
        
        # Folosește rețeaua neurală pentru sentiment
        sentiment_output = self.neural_networks['sentiment'].forward(features)

        sentiments = ['pozitiv', 'neutru', 'negativ']
        sentiment_scores = sentiment_output
        
        # Găsește sentimentul dominant
        max_index = sentiment_scores.index(max(sentiment_scores))
        dominant_sentiment = sentiments[max_index]
        confidence = float(max(sentiment_scores))
        
        return {
            'sentiment': dominant_sentiment,
            'confidence': confidence,
            'scores': {
                'pozitiv': float(sentiment_scores[0]),
                'neutru': float(sentiment_scores[1]),
                'negativ': float(sentiment_scores[2])
            }
        }
    
    def classify_intent(self, text: str) -> Dict[str, Any]:
        """Clasifică intenția din text"""
        features = self.extract_text_features(text)
        
        # Folosește rețeaua neurală pentru intent
        intent_output = self.neural_networks['intent'].forward(features)

        intents = [
            'informație', 'ajutor', 'creare', 'analiză', 'debugging',
            'învățare', 'căutare', 'explicație', 'comparație', 'recomandare'
        ]

        intent_scores = intent_output
        max_index = intent_scores.index(max(intent_scores))
        dominant_intent = intents[max_index]
        confidence = float(max(intent_scores))
        
        return {
            'intent': dominant_intent,
            'confidence': confidence,
            'all_scores': {intent: float(score) for intent, score in zip(intents, intent_scores)}
        }
    
    def evaluate_complexity(self, text: str) -> Dict[str, Any]:
        """Evaluează complexitatea cererii"""
        features = self.extract_text_features(text)[:50]  # Folosește doar primele 50
        
        complexity_output = self.neural_networks['complexity'].forward(features)

        complexity_levels = ['foarte simplu', 'simplu', 'mediu', 'complex', 'foarte complex']
        complexity_scores = complexity_output

        max_index = complexity_scores.index(max(complexity_scores))
        dominant_complexity = complexity_levels[max_index]
        confidence = float(max(complexity_scores))

        return {
            'complexity': dominant_complexity,
            'confidence': confidence,
            'numeric_score': float(max_index + 1),  # 1-5
            'all_scores': {level: float(score) for level, score in zip(complexity_levels, complexity_scores)}
        }
    
    def generate_response_features(self, request: str, context: Dict[str, Any]) -> List[float]:
        """Generează caracteristici pentru răspuns"""
        request_features = self.extract_text_features(request)
        
        # Adaugă caracteristici de context
        context_features = []
        context_features.append(len(context.get('conversation_history', [])))
        context_features.append(len(context.get('active_capabilities', [])))
        context_features.append(1.0 if context.get('current_task') else 0.0)
        
        # Completează cu zerouri
        while len(context_features) < 50:
            context_features.append(0.0)
        
        # Combină caracteristicile
        combined_features = request_features + context_features[:50]
        return combined_features[:150]  # Limitează la 150
    
    def learn_from_interaction(self, request: str, response: str, feedback: float):
        """Învață din interacțiunea cu utilizatorul"""
        # Extrage caracteristici
        features = self.extract_text_features(request)
        
        # Creează pattern nou
        pattern_id = hashlib.md5(f"{request}{response}".encode()).hexdigest()
        
        pattern = NeuralPattern(
            id=pattern_id,
            pattern_type="interaction",
            input_features=features,
            output_response=response,
            confidence=feedback,
            usage_count=1,
            last_used=datetime.now()
        )
        
        self.patterns[pattern_id] = pattern
        
        # Salvează pattern-ul
        self.save_patterns()
        
        print(f"🧠 Pattern învățat: {pattern_id[:8]} (feedback: {feedback})")
    
    def find_similar_patterns(self, features: List[float], threshold: float = 0.8) -> List[NeuralPattern]:
        """Găsește pattern-uri similare"""
        similar_patterns = []
        
        for pattern in self.patterns.values():
            # Calculează similaritatea cosinus
            similarity = self.cosine_similarity(features, pattern.input_features)
            
            if similarity >= threshold:
                similar_patterns.append((pattern, similarity))
        
        # Sortează după similaritate
        similar_patterns.sort(key=lambda x: x[1], reverse=True)
        
        return [pattern for pattern, _ in similar_patterns[:5]]
    
    def cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """Calculează similaritatea cosinus între doi vectori"""
        if len(vec1) != len(vec2):
            return 0.0
        
        dot_product = sum(a * b for a, b in zip(vec1, vec2))
        magnitude1 = math.sqrt(sum(a * a for a in vec1))
        magnitude2 = math.sqrt(sum(a * a for a in vec2))
        
        if magnitude1 == 0 or magnitude2 == 0:
            return 0.0
        
        return dot_product / (magnitude1 * magnitude2)
    
    def save_patterns(self):
        """Salvează pattern-urile în fișier"""
        patterns_data = {}
        for pattern_id, pattern in self.patterns.items():
            patterns_data[pattern_id] = {
                'id': pattern.id,
                'pattern_type': pattern.pattern_type,
                'input_features': pattern.input_features,
                'output_response': pattern.output_response,
                'confidence': pattern.confidence,
                'usage_count': pattern.usage_count,
                'last_used': pattern.last_used.isoformat()
            }
        
        with open(f"{self.workspace_path}/neural_patterns.json", 'w', encoding='utf-8') as f:
            json.dump(patterns_data, f, indent=2, ensure_ascii=False)
    
    def load_patterns(self):
        """Încarcă pattern-urile din fișier"""
        try:
            with open(f"{self.workspace_path}/neural_patterns.json", 'r', encoding='utf-8') as f:
                patterns_data = json.load(f)
            
            for pattern_id, data in patterns_data.items():
                pattern = NeuralPattern(
                    id=data['id'],
                    pattern_type=data['pattern_type'],
                    input_features=data['input_features'],
                    output_response=data['output_response'],
                    confidence=data['confidence'],
                    usage_count=data['usage_count'],
                    last_used=datetime.fromisoformat(data['last_used'])
                )
                self.patterns[pattern_id] = pattern
            
            print(f"🧠 Încărcate {len(self.patterns)} pattern-uri neurale")
            
        except FileNotFoundError:
            print("🧠 Nu există pattern-uri salvate, se va crea fișierul la prima salvare")
        except Exception as e:
            print(f"⚠️ Eroare la încărcarea pattern-urilor: {e}")

# Exemplu de utilizare
if __name__ == "__main__":
    engine = NeuralProcessingEngine()
    
    # Test analiză sentiment
    sentiment = engine.analyze_sentiment("Această aplicație este fantastică!")
    print(f"Sentiment: {sentiment}")
    
    # Test clasificare intent
    intent = engine.classify_intent("Cum pot să creez o funcție în Python?")
    print(f"Intent: {intent}")
    
    # Test evaluare complexitate
    complexity = engine.evaluate_complexity("Implementează un algoritm de sortare quicksort optimizat cu complexitate O(n log n)")
    print(f"Complexitate: {complexity}")
