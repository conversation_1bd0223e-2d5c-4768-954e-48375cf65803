"""
AI Assistant Core - Recrearea capacităților într-un mediu local
Acest fișier demonstrează cum am putea organiza funcționalitățile de bază
Versiunea extinsă cu toate modulele integrate
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
from web_search_module import WebSearchModule
from task_management_module import TaskManager, TaskPriority, TaskState
from file_processor_module import FileProcessor

class AIAssistantCore:
    def __init__(self, workspace_path: str):
        self.workspace_path = workspace_path
        self.knowledge_base = {}
        self.conversation_history = []
        self.capabilities = {
            "code_analysis": True,
            "file_operations": True,
            "web_search": True,
            "project_management": True,
            "learning": True,
            "task_management": True,
            "advanced_file_processing": True
        }

        # Inițializează modulele
        self.web_search = WebSearchModule()
        self.task_manager = TaskManager(os.path.join(workspace_path, "tasks.json"))
        self.file_processor = FileProcessor(workspace_path)

        print("🤖 AI Assistant Core inițializat cu toate modulele!")
        
    def save_knowledge(self, topic: str, information: Dict[str, Any]):
        """Salvează cunoștințe în baza de date locală"""
        if topic not in self.knowledge_base:
            self.knowledge_base[topic] = []
        
        self.knowledge_base[topic].append({
            "timestamp": datetime.now().isoformat(),
            "data": information
        })
        
        # Salvează în fișier pentru persistență
        self._save_to_file()
    
    def retrieve_knowledge(self, topic: str) -> List[Dict[str, Any]]:
        """Recuperează cunoștințe din baza de date"""
        return self.knowledge_base.get(topic, [])
    
    def analyze_code(self, file_path: str) -> Dict[str, Any]:
        """Analizează cod dintr-un fișier"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            analysis = {
                "file_path": file_path,
                "lines_count": len(content.split('\n')),
                "language": self._detect_language(file_path),
                "functions": self._extract_functions(content),
                "classes": self._extract_classes(content)
            }
            
            return analysis
        except Exception as e:
            return {"error": str(e)}
    
    def create_project_structure(self, project_name: str, project_type: str):
        """Creează structura unui proiect nou"""
        project_path = os.path.join(self.workspace_path, project_name)
        
        structures = {
            "web_app": ["src", "public", "tests", "docs"],
            "python_project": ["src", "tests", "docs", "requirements"],
            "data_science": ["data", "notebooks", "src", "models", "reports"]
        }
        
        if project_type in structures:
            os.makedirs(project_path, exist_ok=True)
            for folder in structures[project_type]:
                os.makedirs(os.path.join(project_path, folder), exist_ok=True)
            
            return f"Proiect {project_name} creat cu succes!"
        
        return "Tip de proiect necunoscut"
    
    def _detect_language(self, file_path: str) -> str:
        """Detectează limbajul de programare"""
        extension = os.path.splitext(file_path)[1].lower()
        language_map = {
            '.py': 'Python',
            '.js': 'JavaScript',
            '.html': 'HTML',
            '.css': 'CSS',
            '.java': 'Java',
            '.cpp': 'C++',
            '.c': 'C'
        }
        return language_map.get(extension, 'Unknown')
    
    def _extract_functions(self, content: str) -> List[str]:
        """Extrage funcțiile din cod (implementare simplă)"""
        functions = []
        lines = content.split('\n')
        for line in lines:
            if line.strip().startswith('def '):
                func_name = line.strip().split('(')[0].replace('def ', '')
                functions.append(func_name)
        return functions
    
    def _extract_classes(self, content: str) -> List[str]:
        """Extrage clasele din cod"""
        classes = []
        lines = content.split('\n')
        for line in lines:
            if line.strip().startswith('class '):
                class_name = line.strip().split('(')[0].replace('class ', '').replace(':', '')
                classes.append(class_name)
        return classes
    
    def _save_to_file(self):
        """Salvează baza de cunoștințe în fișier"""
        knowledge_file = os.path.join(self.workspace_path, "knowledge_base.json")
        with open(knowledge_file, 'w', encoding='utf-8') as f:
            json.dump(self.knowledge_base, f, indent=2, ensure_ascii=False)

    # ===== METODE INTEGRATE PENTRU TOATE MODULELE =====

    def search_web(self, query: str, num_results: int = 5) -> List[Dict[str, Any]]:
        """Căutare web integrată"""
        results = self.web_search.web_search(query, num_results)

        # Salvează rezultatele în baza de cunoștințe
        self.save_knowledge("web_searches", {
            "query": query,
            "results": results,
            "timestamp": datetime.now().isoformat()
        })

        return results

    def fetch_webpage(self, url: str) -> Dict[str, Any]:
        """Fetch pagină web integrată"""
        result = self.web_search.web_fetch(url)

        # Salvează conținutul în baza de cunoștințe
        if result.get("status") == "success":
            self.save_knowledge("web_content", {
                "url": url,
                "title": result.get("title"),
                "content_preview": result.get("content", "")[:500] + "...",
                "timestamp": datetime.now().isoformat()
            })

        return result

    def create_task(self, name: str, description: str, priority: str = "MEDIUM") -> Dict[str, Any]:
        """Creează un task nou"""
        try:
            priority_enum = TaskPriority(priority.upper())
            task = self.task_manager.create_task(name, description, priority_enum)

            return {
                "status": "success",
                "task_id": task.id,
                "name": task.name,
                "message": f"Task '{name}' creat cu succes"
            }
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }

    def update_task_status(self, task_id: str, new_state: str) -> Dict[str, Any]:
        """Actualizează starea unui task"""
        try:
            state_enum = TaskState(new_state.upper())
            success = self.task_manager.update_task(task_id, state=state_enum)

            if success:
                return {
                    "status": "success",
                    "message": f"Task actualizat la starea {new_state}"
                }
            else:
                return {
                    "status": "error",
                    "message": "Task nu a fost găsit"
                }
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }

    def get_task_progress(self) -> Dict[str, Any]:
        """Obține progresul task-urilor"""
        return self.task_manager.get_progress_report()

    def edit_file(self, file_path: str, old_text: str, new_text: str) -> Dict[str, Any]:
        """Editează un fișier folosind str_replace"""
        result = self.file_processor.str_replace(file_path, old_text, new_text)

        # Salvează în istoric
        if result.get("status") == "success":
            self.save_knowledge("file_edits", {
                "file": file_path,
                "action": "str_replace",
                "replacements": result.get("replacements", 0),
                "timestamp": datetime.now().isoformat()
            })

        return result

    def analyze_file(self, file_path: str) -> Dict[str, Any]:
        """Analizează structura unui fișier"""
        analysis = self.file_processor.analyze_code_structure(file_path)

        # Salvează analiza în baza de cunoștințe
        if analysis.get("status") != "error":
            self.save_knowledge("code_analysis", {
                "file": file_path,
                "language": analysis.get("language"),
                "functions_count": len(analysis.get("functions", [])),
                "classes_count": len(analysis.get("classes", [])),
                "complexity": analysis.get("complexity_score", 0),
                "timestamp": datetime.now().isoformat()
            })

        return analysis

    def comprehensive_project_setup(self, project_name: str, project_type: str,
                                  requirements: List[str]) -> Dict[str, Any]:
        """Setup complet pentru un proiect nou"""
        results = {
            "project_name": project_name,
            "steps_completed": [],
            "errors": []
        }

        try:
            # 1. Creează structura proiectului
            structure_result = self.create_project_structure(project_name, project_type)
            results["steps_completed"].append(f"Structură creată: {structure_result}")

            # 2. Generează plan de task-uri
            tasks = self.task_manager.generate_project_plan(project_name, requirements)
            results["steps_completed"].append(f"Plan generat cu {len(tasks)} task-uri")

            # 3. Caută documentație relevantă
            if project_type in ["web_app", "python_project"]:
                tech = "python" if project_type == "python_project" else "javascript"
                docs = self.web_search.search_documentation(tech)
                results["steps_completed"].append(f"Documentație găsită: {len(docs)} resurse")

            # 4. Salvează informațiile proiectului
            self.save_knowledge("projects", {
                "name": project_name,
                "type": project_type,
                "requirements": requirements,
                "tasks_created": len(tasks),
                "created_at": datetime.now().isoformat()
            })

            results["status"] = "success"
            results["message"] = f"Proiect '{project_name}' configurat complet!"

        except Exception as e:
            results["status"] = "error"
            results["errors"].append(str(e))

        return results

# Exemplu de utilizare
if __name__ == "__main__":
    # Inițializează asistentul AI în folderul curent
    ai = AIAssistantCore("/home/<USER>/Desktop/claude ai")
    
    # Demonstrează capacitățile
    print("🤖 AI Assistant recreat cu succes!")
    print("📁 Workspace:", ai.workspace_path)
    print("⚡ Capacități disponibile:", list(ai.capabilities.keys()))
    
    # Salvează niște cunoștințe
    ai.save_knowledge("programming", {
        "best_practices": ["Clean code", "Testing", "Documentation"],
        "languages": ["Python", "JavaScript", "HTML", "CSS"]
    })
    
    print("💾 Cunoștințe salvate în baza de date locală!")
